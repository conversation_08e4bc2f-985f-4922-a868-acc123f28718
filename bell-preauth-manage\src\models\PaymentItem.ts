import { CreditCardDetails, BankAccountDetails } from './index';
import { PaymentItemAccountType, PaymentItemBillStatus, MakeOneTimePaymentStatus, RegisterPACCStatus, RegisterPADStatus, UnEnrollStatus, PaymentMethod } from './Enums';

export interface PaymentItem {
  AccountType: PaymentItemAccountType;
  BanID: string;
  Ban: string;
  BillName: string;
  NickName: string;
  Due: number;
  DueStr: string;
  Amount: number;
  AmountStr: string;
  AmountStrInvalid: string | null;
  BalanceAmount: number;
  BillStatus: PaymentItemBillStatus;
  isPaymentCompleted: boolean;
  isSignUpCompleted: boolean;
  IsOnPreauthorizedPayments: boolean;
  TransactionID: string | null;
  PaymentTransactionID: string | null;
  IsChecked: boolean;
  ErrorCode: string | null;
  ErrorCodeDescription: string | null;
  ErrorCodeOTP: string | null;
  ErrorCodeOTPDescription: string | null;
  MakeOneTimePaymentStatus?: MakeOneTimePaymentStatus | null;
  RegisterPACCStatus?: RegisterPACCStatus | null;
  RegisterPADStatus?: RegisterPADStatus | null;
  UnEnrollStatus?: UnEnrollStatus | null;
  PaymentMethod: PaymentMethod;
  PreviousPaymentMethod: PaymentMethod;
  TimeStampInfo: TimeStampInfo;
  CreditCardDetails: CreditCardDetails | null;
  BankAccountDetails: BankAccountDetails | null;
  IsOTCCBlocked: boolean;
  CustomerId: string | null;
  DueDate?: Date | null;
  IsNM1Account: boolean;
  PayBillBalance: boolean;
  BanAccType: string;
  BanAccSubType: string;
  isOneTimePaymentEligible: boolean;
  isOneTimeCreditCardPaymentEnabled: boolean;
  subscriberId?: string | null;
  isLastBillOnPreauth?: boolean | null;
}

export interface TimeStampInfo {
  LastUpdateDate: string;
  LastUpdateStamp: number;
  LastUpdateDateTicks: number; 
}

export interface BanTimeStampInfo {
  LastUpdateDate: Date;
  LastUpdateStamp: number;
}

export interface PaymentItemAccountTypeName
{
  MyBill: string;
  Mobility: string;
  OneBill: string;
  TV: string;
  Internet: string;
  HomePhone: string;
  MobilityAndOneBill: string;
  SingleBan: string;
}

export default PaymentItem;

