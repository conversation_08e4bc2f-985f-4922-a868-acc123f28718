
include:
  - project: 'uxp/devops/templates'
    ref: master
    file: '.widget-dependency.yml'
trigger_bell_preauth_manage:
  stage: upstream
  extends: .trigger-common-rules
  variables:
    JOBTRIGGER: $CI_PROJECT_NAME
    BRANCHTRIGGER: ${CI_COMMIT_BRANCH}
  trigger:
    project: uxp/bell-preauth-manage-bundle
    branch: ${CI_COMMIT_BRANCH}

trigger_bell_preauth_setup:
  stage: upstream
  extends: .trigger-common-rules
  variables:
    JOBTRIGGER: $CI_PROJECT_NAME
    BRANCHTRIGGER: ${CI_COMMIT_BRANCH}
  trigger:
    project: uxp/bell-preauth-setup-bundle
    branch: ${CI_COMMIT_BRANCH}