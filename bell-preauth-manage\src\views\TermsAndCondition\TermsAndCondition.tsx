import * as React from "react";
import { injectIntl } from "react-intl";

import {
  Accordion,
  AccordionItem,
  AccordionContent,
  AccordionTrigger,
  AccordionToggle<PERSON>itle,
  Button,
  Divider,
  useWindowResize, 
  useResponsiveHeight
} from "@bell/bell-ui-library";

interface HeightsType {
  mobile: { maxHeight: string } | { minHeight: string } | { height: string };
  tablet: { maxHeight: string } | { minHeight: string } | { height: string };
  desktop: { maxHeight: string } | { minHeight: string } | { height: string };
}

export interface TermsAndConditionProps {
  collapseHeightDynamic?: HeightsType;
  expandHeightDynamic?: HeightsType;
  intl: any;
  onSubmitClick?: () => void;
  onCancelClick?: () => void;
  province: string;
  language: string;
  userProfileProv?: string;
}

const TermsAndConditionsComponent = (
  {
    collapseHeightDynamic,
    expandHeightDynamic,
    intl,
    onSubmitClick,
    onCancelClick,
    province,
    language,
    userProfileProv
  }
  : TermsAndConditionProps) => {
  const { width } = useWindowResize(100);
  const getHeight = (size: { maxHeight: string } | { minHeight: string } | { height: string }): string => {
    if ('height' in size) {
      return size.height;
    }
    return "";
  };
  const collapseHeight = getHeight(collapseHeightDynamic ? useResponsiveHeight(width, collapseHeightDynamic) : { height: "90px" });
  const expandHeight = getHeight(expandHeightDynamic ? useResponsiveHeight(width, expandHeightDynamic) : { height: "460px" });
  const termsAndConDislaimer = intl.formatMessage({id: "TERMS_AND_CONDITION_DISCLAIMER"});
  const termsAndConList1 = intl.formatMessage({ id: "TERMS_AND_CON_DESC_LIST_1" }); 
  const termsAndConList2 = intl.formatMessage({ id: "TERMS_AND_CON_DESC_LIST_2" });
  const termsAndConList2Item1 = intl.formatMessage({ id: "TERMS_AND_CON_DESC_LIST_2_ITEM1" });
  const termsAndConList2Item2 = intl.formatMessage({ id: "TERMS_AND_CON_DESC_LIST_2_ITEM2" });
  const termsAndConList1Item3 = intl.formatMessage({ id: "TERMS_AND_CON_DESC_LIST_1_ITEM3" });
  const termsAndConDesc2 = intl.formatMessage({ id: "TERMS_AND_CON_DESC_2" });
  const termsAndConDesc3 = intl.formatMessage({ id: "TERMS_AND_CON_DESC_3" });

  const termsAndConList1_QC = intl.formatMessage({ id: "TERMS_AND_CON_DESC_LIST_1_QC" }); 
  const termsAndConList2_QC = intl.formatMessage({ id: "TERMS_AND_CON_DESC_LIST_2_QC" });
  const termsAndConList2Item1_QC = intl.formatMessage({ id: "TERMS_AND_CON_DESC_LIST_2_ITEM1_QC" });
  const termsAndConList2Item2_QC = intl.formatMessage({ id: "TERMS_AND_CON_DESC_LIST_2_ITEM2_QC" });
  const termsAndConList1Item3_QC = intl.formatMessage({ id: "TERMS_AND_CON_DESC_LIST_1_ITEM3_QC" });
  const termsAndConDesc2_QC = intl.formatMessage({ id: "TERMS_AND_CON_DESC_2_QC" });
  const termsAndConDesc3_QC = intl.formatMessage({ id: "TERMS_AND_CON_DESC_3_QC" });

  return (
    <div>
      <div>
        <Accordion mode="single">
          <AccordionItem key={1} index={1}>
            <AccordionContent
              aria-labelledby="terms-and-condition-trigger"
              id="terms-and-condition-content"
              collapseHeight={collapseHeight}
              expandHeight={expandHeight}
              className="brui-text-14 brui-text-gray brui-leading-18 brui-pr-20 payment-overflow-y-scroll payment-scrollbar"
            >
                            
              {userProfileProv === "QC" && language === "en" ? 
              // {province == "QC" && language == "en" ?
                (
                  <div>
                    <p>  
                      <p className="payment-mb-15" >{intl.formatMessage({id: "TERMS_AND_CON_DESC_1_QC"})}</p>
                      <p dangerouslySetInnerHTML={{ __html: termsAndConList1_QC }}></p>
                      <ul className="payment-list-disc payment-list-inside payment-mb-10 payment-ml-10">
                        <li><span className="payment-ml-[-6px]">{intl.formatMessage({id: "TERMS_AND_CON_DESC_LIST_1_ITEM1_QC"})}</span></li>
                        <li><span className="payment-ml-[-6px]">{intl.formatMessage({id: "TERMS_AND_CON_DESC_LIST_1_ITEM2_QC"})}</span></li>
                        <li><span className="payment-ml-[-6px]" dangerouslySetInnerHTML={{ __html: termsAndConList1Item3_QC}}></span></li>
                        {/* <li className="payment-font-bold">{intl.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM3_QC"})}</li> */}
                      </ul>

                      <p dangerouslySetInnerHTML={{ __html: termsAndConList2_QC }}></p>
                      <ul className="payment-list-disc payment-list-inside payment-mb-10">
                        <li><span className="payment-ml-[-6px]" dangerouslySetInnerHTML={{ __html: termsAndConList2Item1_QC}}></span></li>
                        <li><span className="payment-ml-[-6px]" dangerouslySetInnerHTML={{ __html: termsAndConList2Item2_QC}}></span></li>
                      </ul>

                      <p className="payment-mb-15" dangerouslySetInnerHTML={{ __html: termsAndConDesc2_QC }}></p>
                      <p className="payment-mb-15" dangerouslySetInnerHTML={{ __html: termsAndConDesc3_QC }}></p>
                      <p className="">{intl.formatMessage({id: "TERMS_AND_CON_DESC_4_QC"})}</p>
                      <p className="">{intl.formatMessage({id: "TERMS_AND_CON_ZIP_CODE_QC"})}</p>
                      <p className="">{intl.formatMessage({id: "TERMS_AND_CON_REGION_QC"})}</p>
                      <p className="">{intl.formatMessage({id: "TERMS_AND_CON_REGION_2_QC"})}</p>
                      <p className="">{intl.formatMessage({id: "TERMS_AND_CON_TEL_QC"})}</p>

                    </p>

                    <p className="payment-mt-10">
                      <p className="payment-mb-15" >{intl.formatMessage({id: "TERMS_AND_CON_DESC_1"})}</p>
                      <p dangerouslySetInnerHTML={{ __html: termsAndConList1 }}></p>
                      <ul className="payment-list-disc payment-list-inside payment-mb-10 payment-ml-10">
                        <li><span className="payment-ml-[-6px]">{intl.formatMessage({id: "TERMS_AND_CON_DESC_LIST_1_ITEM1"})}</span></li>
                        <li><span className="payment-ml-[-6px]">{intl.formatMessage({id: "TERMS_AND_CON_DESC_LIST_1_ITEM2"})}</span></li>
                        <li><span className="payment-ml-[-6px]" dangerouslySetInnerHTML={{ __html: termsAndConList1Item3}}></span></li>
                        {/* <li className="payment-font-bold">{intl.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM3"})}</li> */}
                      </ul>

                      <p dangerouslySetInnerHTML={{ __html: termsAndConList2 }}></p>
                      <ul className="payment-list-disc payment-list-inside payment-mb-10 payment-ml-10">
                        <li><span className="payment-ml-[-6px]" dangerouslySetInnerHTML={{ __html: termsAndConList2Item1}}></span></li>
                        <li><span className="payment-ml-[-6px]" dangerouslySetInnerHTML={{ __html: termsAndConList2Item2}}></span></li>
                      </ul>

                      <p className="payment-mb-15" dangerouslySetInnerHTML={{ __html: termsAndConDesc2 }}></p>
                      <p className="payment-mb-15" dangerouslySetInnerHTML={{ __html: termsAndConDesc3 }}></p>
                      <p className="">{intl.formatMessage({id: "TERMS_AND_CON_DESC_4"})}</p>
                      <p className="">{intl.formatMessage({id: "TERMS_AND_CON_ZIP_CODE"})}</p>
                      <p className="">{intl.formatMessage({id: "TERMS_AND_CON_REGION"})}</p>
                      <p className="">{intl.formatMessage({id: "TERMS_AND_CON_REGION_2"})}</p>
                      <p className="">{intl.formatMessage({id: "TERMS_AND_CON_TEL"})}</p>

                    </p>
                  </div>
                ):(
                  <div>
                    <p>
                      <p className="payment-mb-15" >{intl.formatMessage({id: "TERMS_AND_CON_DESC_1"})}</p>
                      <p dangerouslySetInnerHTML={{ __html: termsAndConList1 }}></p>
                      <ul className="payment-list-disc payment-list-inside payment-mb-10 payment-ml-10">
                        <li><span className="payment-ml-[-6px]">{intl.formatMessage({id: "TERMS_AND_CON_DESC_LIST_1_ITEM1"})}</span></li>
                        <li><span className="payment-ml-[-6px]">{intl.formatMessage({id: "TERMS_AND_CON_DESC_LIST_1_ITEM2"})}</span></li>
                        <li><span className="payment-ml-[-6px]" dangerouslySetInnerHTML={{ __html: termsAndConList1Item3}}></span></li>
                        {/* <li className="payment-font-bold">{intl.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM3"})}</li> */}
                      </ul>

                      <p dangerouslySetInnerHTML={{ __html: termsAndConList2 }}></p>
                      <ul className="payment-list-disc payment-list-inside payment-mb-10 payment-ml-10">
                        <li><span className="payment-ml-[-6px]" dangerouslySetInnerHTML={{ __html: termsAndConList2Item1}}></span></li>
                        <li><span className="payment-ml-[-6px]" dangerouslySetInnerHTML={{ __html: termsAndConList2Item2}}></span></li>
                      </ul>

                      <p className="payment-mb-15" dangerouslySetInnerHTML={{ __html: termsAndConDesc2 }}></p>
                      <p className="payment-mb-15" dangerouslySetInnerHTML={{ __html: termsAndConDesc3 }}></p>
                      <p className="">{intl.formatMessage({id: "TERMS_AND_CON_DESC_4"})}</p>
                      <p className="">{intl.formatMessage({id: "TERMS_AND_CON_ZIP_CODE"})}</p>
                      <p className="">{intl.formatMessage({id: "TERMS_AND_CON_REGION"})}</p>
                      <p className="">{intl.formatMessage({id: "TERMS_AND_CON_REGION_2"})}</p>
                      <p className="">{intl.formatMessage({id: "TERMS_AND_CON_TEL"})}</p>

                    </p>
                  </div>
                )}
                            
                            
                            
            </AccordionContent>
            <div className="payment-pt-15">
              <AccordionTrigger
                id="terms-and-condition-trigger"
                aria-controls="terms-and-condition-content"
                className="brui-text-14 brui-text-blue brui-underline brui-leading-18 hover:brui-text-blue-1 hover:brui-no-underline"
              >
                <AccordionToggleTitle titleExpand={intl.formatMessage({id: "CTA_EXPAND_TERMS"})} titleCollapse={intl.formatMessage({id: "CTA_COLLAPSE_TERMS"})} />
              </AccordionTrigger>
            </div>
          </AccordionItem>
        </Accordion>
      </div>
      <div className="payment-bg-gray-3 payment-mx-[-15px] sm:payment-mx-[-30px] md:payment-mx-[-15px] payment-mt-30">
        <Divider width={1} direction="horizontal" />
        <div className="payment-px-15 sm:payment-px-30 md:payment-px-15 payment-pt-30 payment-pb-45">
          <div className="brui-text-gray brui-text-14 brui-leading-18 payment-max-w-[500px]">
            <div dangerouslySetInnerHTML={{ __html: termsAndConDislaimer }}></div>
          </div>
          <div className="brui-inline-flex brui-flex-wrap brui-items-center">
            <div className="payment-pr-30 payment-pt-30">
              <Button
                variant="primary"
                size="regular"
                onClick={onSubmitClick}
              >
                {intl.formatMessage({id: "CTA_CONFIRM"})}
              </Button>
            </div>
            <div className="payment-pt-30">
              <Button
                variant="textBlue"
                size="regular"
                className="!brui-text-14 brui-leading-18"
                onClick={onCancelClick}
              >
                {intl.formatMessage({id: "CTA_CANCEL"})}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export const TermsAndConditions = injectIntl(TermsAndConditionsComponent);
