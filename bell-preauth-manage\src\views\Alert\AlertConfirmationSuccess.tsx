import * as React from "react";
import { Alert, Heading, Link, Icon, Text } from "@bell/bell-ui-library";
import { FormattedMessage, injectIntl } from "react-intl";
import { AccountInputValues, AutopayEligibleSubscribers, AutopayOffers, OfferImpactType, PaymentItem, SubscriberOffersWithBan } from "../../models";

interface AlertConfirmationSuccessProps {
  intl: any;
  submitMultiOrderPayment: any;
  accountInputValue: AccountInputValues[];
  isBankPayment?: boolean;
  checkedBillItems: PaymentItem[];
  paymentItem: PaymentItem[];
  creditCardAutopayOffers: SubscriberOffersWithBan[];
  debitCardAutopayOffers: SubscriberOffersWithBan[];
  language: "en" | "fr";
  isBanCreditPreauth: boolean
}
const AlertConfirmationSuccessComponent = ({
  intl,
  submitMultiOrderPayment,
  accountInputValue,
  isBankPayment,
  checkedBillItems,
  language,
  paymentItem,
  creditCardAutopayOffers,
  debitCardAutopayOffers,
  isBanCreditPreauth
}: AlertConfirmationSuccessProps) => {
  const AlertConfirmation = ((!isBankPayment || (checkedBillItems && checkedBillItems[0].CreditCardDetails)) || paymentItem.length > 1) ? intl.formatMessage({ id: "ALERT_CONFIRMATION_SUCCESS_MESSAGE" }) : intl.formatMessage({ id: "ALERT_CONFIRMATION_SUCCESS_MESSAGE_V2" });
  //    const OTPSuccessMessage = isBankPayment 
  //    ? language === "fr" ? "ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD_FR" : "ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD"
  //    : "ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PACC";
  const OTPSuccessMessage = isBankPayment
    ? (language === "fr"
      ? "ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD_FR"
      : "ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD")
    : "ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PACC";

  // autopay changes

  // autopay changes
  const checkeddebitOffers = () => {
    const filteredOffer: any = [];
    debitCardAutopayOffers && debitCardAutopayOffers?.map((item) => {
      checkedBillItems && checkedBillItems.map((billItem) => {
        if (item.Ban === billItem.BillName) {
          filteredOffer.push(item);
        }
      });
    });

    return filteredOffer;
  };

  const checkedcreditOffers = () => {
    const filteredOffer: any = [];
    creditCardAutopayOffers && creditCardAutopayOffers?.map((item) => {
      checkedBillItems && checkedBillItems.map((billItem) => {
        if (item.Ban === billItem.BillName) {
          filteredOffer.push(item);
        }
      });
    });

    return filteredOffer;
  };
  const autopayOffers = {
    label: intl.formatMessage({ id: "PAYMENT_METHOD" }),
    debits: isBankPayment ? (paymentItem && paymentItem.length > 1 ? checkeddebitOffers() : debitCardAutopayOffers) : null,
    credits: !isBankPayment ? (paymentItem && paymentItem.length > 1 ? checkedcreditOffers() : creditCardAutopayOffers) : null,
  };
  // const hasDebitOffer = autopayOffers && 
  //                         autopayOffers.credits && 
  //                         autopayOffers.credits.length > 0 && 
  //                         autopayOffers.credits[0].AutopayEligibleSubscribers && 
  //                         autopayOffers.credits[0].AutopayEligibleSubscribers.length > 0;
  // const hasCreditsOffer = autopayOffers && 
  //                         autopayOffers.credits && 
  //                         autopayOffers.credits.length > 0 && 
  //                         autopayOffers.credits[0].AutopayEligibleSubscribers && 
  //                         autopayOffers.credits[0].AutopayEligibleSubscribers.length > 0;


  const offerImpactRemovedCredit = (): boolean => {
    if (autopayOffers.debits) {
      // Bank payment
      return autopayOffers.debits.some((debit: any) =>
        debit.AutopayEligibleSubscribers?.some((item: any) =>
          item?.autopayOffers?.some((credit: any) => credit.offerImpact === OfferImpactType.REMOVE)
        )
      ) || false;
    } else {
      // Creadit Payment
      return autopayOffers?.credits?.some((debit: any) =>
        debit.AutopayEligibleSubscribers?.some((item: any) =>
          item?.autopayOffers?.some((credit: any) => credit.offerImpact === OfferImpactType.REMOVE)
        )
      ) || false;
    }
    // Return false if no match found
  };
  const offerImpactIncrease = React.useCallback((): boolean => {
    if (autopayOffers.debits) {
      // Bank payment
      return autopayOffers.debits.some((debit: any) =>
        debit.AutopayEligibleSubscribers?.some((item: any) =>
          item?.autopayOffers?.some((credit: any) => credit.offerImpact === OfferImpactType.INCREASE)
        )
      ) || false;
    } else {
      // Creadit Payment
      return autopayOffers?.credits?.some((debit: any) =>
        debit.AutopayEligibleSubscribers?.some((item: any) =>
          item?.autopayOffers?.some((credit: any) => credit.offerImpact === OfferImpactType.INCREASE)
        )
      ) || false;
    }
    // Return false if no match found
  },[autopayOffers.debits, autopayOffers.debits]);
  const offerImpactReduce = React.useCallback((): boolean => {
    if (autopayOffers.debits) {
      // Bank payment
      return autopayOffers.debits.some((debit: SubscriberOffersWithBan) =>
        debit.AutopayEligibleSubscribers?.some((item: AutopayEligibleSubscribers) =>
          item?.autopayOffers?.some((credit: AutopayOffers) => credit.offerImpact === OfferImpactType.REDUCE)
        )
      ) || false;
    } else {
      // Creadit Payment
      return autopayOffers?.credits?.some((debit: SubscriberOffersWithBan) =>
        debit.AutopayEligibleSubscribers?.some((item: AutopayEligibleSubscribers) =>
          item?.autopayOffers?.some((credit: AutopayOffers) => credit.offerImpact === OfferImpactType.REDUCE)
        )
      ) || false;
    }
    // Return false if no match found
  },[autopayOffers.debits, autopayOffers.debits]);
  const offerImpactGain = React.useCallback((): boolean => {
    if (autopayOffers.debits) {
      // Bank payment
      return autopayOffers.debits.some((debit: any) =>
        debit.AutopayEligibleSubscribers?.some((item: any) =>
          item?.autopayOffers?.some((credit: any) => credit.offerImpact === OfferImpactType.GAIN)
        )
      ) || false;
    } else {
      // Creadit Payment
      return autopayOffers?.credits?.some((debit: any) =>
        debit.AutopayEligibleSubscribers?.some((item: any) =>
          item?.autopayOffers?.some((credit: any) => credit.offerImpact === OfferImpactType.GAIN)
        )
      ) || false;
    }
    // Return false if no match found
  },[autopayOffers.debits, autopayOffers.debits]);
  
  function hasValidAutopayOffers(autopayOffers:any) {
    return autopayOffers?.[0]?.AutopayEligibleSubscribers?.[0]?.autopayOffers?.length > 0;
  }
  const isshowAutopaysuccess = {
    show: hasValidAutopayOffers(autopayOffers?.credits) || hasValidAutopayOffers(autopayOffers?.debits)
  };

  // const isshowAutopaysuccess =
  // {
  //   show : hasValidAutopayOffers(autopayOffers)
  //   // show: autopayOffers && autopayOffers.credits && autopayOffers.credits.length > 0 && autopayOffers.credits[0].AutopayEligibleSubscribers && autopayOffers.credits[0].AutopayEligibleSubscribers.length > 0 && autopayOffers.credits[0].AutopayEligibleSubscribers[0].autopayOffers && autopayOffers.credits[0].AutopayEligibleSubscribers[0].autopayOffers.length > 0 ||
  //   //   autopayOffers && autopayOffers.debits && autopayOffers.debits.length > 0 && autopayOffers.debits[0].AutopayEligibleSubscribers && autopayOffers.debits[0].AutopayEligibleSubscribers.length > 0 && autopayOffers.debits[0].AutopayEligibleSubscribers[0].autopayOffers && autopayOffers.debits[0].AutopayEligibleSubscribers[0].autopayOffers.length > 0
  // };
  return (
    <Alert
      variant="success"
      className="brui-border brui-relative sm:payment-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 payment-py-30 sm:payment-flex brui-block"
      iconSize="36"
      id="alert-3"
      role="">
      <Text id="Confirmation-message" elementType="div" className="payment-pl-0 sm:payment-pl-16 payment-mt-15 sm:payment-mt-0 sm:brui-pt-0">
        <Heading level="h3" variant="xs" className="brui-mb-15 brui-font-sans brui-leading-22">
          {intl.formatMessage({ id: "ALERT_CONFIRMATION_SUCCESS_HEADING" })}
        </Heading>
        <Text elementType="div" role="list" className="brui-text-14 brui-mb-30 brui-text-black brui-ml-5">
          {
            submitMultiOrderPayment.map((item: any, index: any) => {
              // Define the variable to store the filtered account number
              // const accountNumber = accountInputValue
              //     .filter((x) => x.transactionID === item.OrderFormId)
              //     .map((x) => x.accountNumber)[0];
              const accountNumber = accountInputValue.find((x) => x.transactionID === item.OrderFormId)?.accountNumber;

              // const NickName =  checkedBillItems.filter((x) => x.Ban === accountNumber).map((x) => x.NickName)[0];
              const NickName = checkedBillItems.find((x) => x.Ban === accountNumber)?.NickName;



              const displayTxtAccount = (NickName ? NickName : accountNumber);

              const OTPSuccessMessageAccount = intl.formatMessage(
                { id: OTPSuccessMessage },
                { account: displayTxtAccount } // Passing dynamic values for account
              );

              //   const OTPSuccessMessageAccountRender = OTPSuccessMessageAccount.replace("{account}", '<strong> ${accountNumber} </strong>');

              return (
                <>
                  {item?.otp != null && item?.otp.isSuccess && (
                    <Text elementType="div" role="listitem" className="brui-flex brui-items-start payment-mb-15">
                      <Icon className="brui-text-12 brui-text-blue payment-mt-3" iconClass="bi_brui" iconName="bi_check_light" />
                      <span className="brui-text-16 brui-leading-20 payment-ml-10" dangerouslySetInnerHTML={{ __html: OTPSuccessMessageAccount }}>
                        {/* <FormattedMessage
                                                id={isBankPayment ? "ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD" : "ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PACC" }
                                                values={{
                                                    account: <strong>{accountNumber}</strong>,
                                                }}
                                            /> */}

                        {/* The current balance for {accountNumber} has been paid. */}
                      </span>
                    </Text>
                  )}

                </>
              );
            })
          }
          <Text elementType="div" role="listitem" className="brui-flex brui-items-start payment-mb-15">
            <Icon className="brui-text-12 brui-text-blue payment-mt-3 " iconClass="bi_brui" iconName="bi_check_light" />
            <span className="brui-text-16 brui-leading-20 payment-ml-10"><div dangerouslySetInnerHTML={{ __html: AlertConfirmation }}></div></span>
          </Text>
          {/* {(isBanCreditPreauth || !isBankPayment )&&
                <>
                <Text elementType="div" role="listitem" className="brui-flex brui-items-start payment-mb-15"> 
                    <Icon className="brui-text-12 brui-text-blue payment-mt-3 " iconClass="bi_brui" iconName="bi_check_light" /> 
                    <span className="brui-text-16 brui-leading-20 payment-ml-10"><div dangerouslySetInnerHTML={{ __html: AlertConfirmation_Desc_1 }}></div></span>
                </Text>
                <Text elementType="div" role="listitem" className="brui-flex brui-items-start payment-mb-15"> 
                    <Icon className="brui-text-12 brui-text-blue payment-mt-3 " iconClass="bi_brui" iconName="bi_check_light" /> 
                    <span className="brui-text-16 brui-leading-20 payment-ml-10"><div dangerouslySetInnerHTML={{ __html: AlertConfirmation_Desc_2 }}></div></span>
                </Text>
                </>

                 } */}

          {(isshowAutopaysuccess.show && offerImpactRemovedCredit() === false && (offerImpactIncrease() || offerImpactReduce() || offerImpactGain())) ?
            <Text elementType="div" role="listitem" className="brui-flex brui-items-start payment-mb-15">
              <Icon className="brui-text-12 brui-text-blue payment-mt-3" iconClass="bi_brui" iconName="bi_check_light" />
              <span className="brui-text-16 brui-leading-20 payment-ml-10">
                {offerImpactIncrease() ? intl.formatMessage({ id: "AUTOPAY_ALERT_INCREASE" }) : offerImpactReduce() || offerImpactGain() ? intl.formatMessage({ id: "AUTOPAY_ALERT" }) : ""}

              </span>
            </Text> : ""
          }

        </Text>
        <p className="brui-text-14 brui-mb-5 brui-text-gray brui-leading-18">
          {intl.formatMessage({ id: "ALERT_CONFIRMATION_SUCCESS_NUMBER" })} <strong>{submitMultiOrderPayment[0].PaymentConfirmationNumber}</strong>
        </p>
        <p className="brui-text-14 brui-text-gray brui-leading-18">
          <FormattedMessage
            id="ALERT_CONFIRMATION_SUCCESS_DESC"
            values={{
              email: <strong>{submitMultiOrderPayment[0].ConfirmationEmailAddress}</strong>,
            }}
          />
          {" "}<Link variant="textBlue" size="small" href="/MyProfile/EditProfile?editField=EMAIL_ADDRESS" className="">{intl.formatMessage({ id: "ALERT_CONFIRMATION_SUCCESS_DESC_LINK" })}</Link>
        </p>
      </Text>
    </Alert>
  );
};
export const AlertConfirmationSuccess = injectIntl(AlertConfirmationSuccessComponent);
