import * as React from "react";
import { injectIntl } from "react-intl";
import { PaymentRadioCard } from "./PaymentMethodRadio";
import {NotifCard} from "../NotifCard";
import { Heading, ListItem, Price, Text , Alert } from "@bell/bell-ui-library";
import { AlertNotificationList, AlertNotificationListItem } from "../Alert";


interface CancelPaymentProps {
    intl: any;
    children: React.ReactNode;
    Checked: boolean;
}

const CancelPaymentComponent = ({ intl, children, Checked }:CancelPaymentProps) => {

    const CANCEL_PAYMENT_LABEL = intl.formatMessage({id: "CANCEL_PAYMENT_LABEL"});

    return (
        <div className="brui-mb-15">
            <PaymentRadioCard 
                id="payment-radio-cancel" 
                name="payment-radio" 
                label={CANCEL_PAYMENT_LABEL}
                headingLevel="h3"
                defaultChecked={Checked ? true : undefined}
            >
                <div>
              <Heading
                variant="default"
                level="h4"
                className="brui-font-sans brui-text-black brui-text-18 brui-text-left brui-leading-22 brui-font-bold"
              >
                Are you sure you want to cancel?
              </Heading>
              <span className="brui-text-gray brui-text-14 brui-leading-18 brui-mt-5">
                Pre-authorized payments are the best way to ensure your Bell bill is always paid on time, and avoids late fees.
              </span>
            </div>

            {/* Warning message - Final payment */}
            <div className="brui-pt-30">
              <Alert
                variant="warning"
                iconSize="36"
                className="sm:brui-flex sm:brui-border sm:brui-border-gray-4 sm:payment-rounded-[16px] sm:brui-p-30"
              >
                <div className="brui-w-full brui-mt-15 sm:brui-mt-0 sm:brui-ml-15">
                  <div>
                    <Heading
                      variant="default"
                      level="h4"
                      className="brui-font-sans brui-text-black brui-text-18 brui-text-left brui-leading-22 brui-font-bold"
                    >
                      Final payment
                    </Heading>
                    <Text elementType="span" className="brui-text-14 brui-text-gray brui-leading-18 brui-mt-5">
                      One final pre-authorized payment will be withdrawn for the following:
                    </Text>
                  </div>

                  <AlertNotificationList className="brui-mt-15">
                    <AlertNotificationListItem
                      variant="accountList"
                      label="MyBill"
                      labelDescription="Home"
                      cardDetails="American Express ending in 1234, valid until 00/00"
                    />
                  </AlertNotificationList>
                </div>
              </Alert>
            </div>

            {/* Autopay credits removed message */}
            <div className="brui-pt-15 sm:brui-pt-30">
              <NotifCard
                hasNotifCard
                variant="notifCardWarning"
                label={<span className='brui-font-bold brui-text-black'>Your autopay credit(s) will be removed</span>}
                label1
                label2
                label3
              >
                <ul className="brui-list-disc brui-list-inside brui-mb-15">
                  <ListItem className="brui-text-14 brui-text-gray brui-leading-18">
                    (555) 555-5555 -&nbsp;<Price className="brui-inline brui-lowercase !brui-text-14 brui-font-normal brui-text-gray" price={3.00} variant="defaultPrice" suffixText="perMonth" language="en"></Price>
                  </ListItem>
                  <ListItem className="brui-text-14 brui-text-gray brui-leading-18">
                    (555) 555-5555 -&nbsp;<Price className="brui-inline brui-lowercase !brui-text-14 brui-font-normal brui-text-gray" price={3.00} variant="defaultPrice" suffixText="perMonth" language="en"></Price>
                  </ListItem>
                </ul>
              </NotifCard>
            </div>
            </PaymentRadioCard>
        </div>
    );
}

export const CancelPayment = injectIntl(CancelPaymentComponent);