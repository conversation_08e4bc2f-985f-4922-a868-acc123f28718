import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ing,Icon, Text } from "@bell/bell-ui-library";

export interface ErrorPageProps {
  iconVariant: "error" | "warning";
  errorHeaderText: string;
  errorHeaderStyle?: string;
  errorHeaderTextSR?: string;
  errorText: React.ReactNode;
  errorTextStyle?: string;
  buttonText: string;
  onButtonClick: () => void;
  suggestionList?: {
    label: string,
    items: string[]
  }
}

const variant = {
  error: "payment-text-red payment-text-[60px] payment-box-border payment-static payment-text-center",
  warning: "payment-text-yellow payment-text-[60px] payment-box-border payment-static payment-text-center"
};

const ErrorPage = ({
  iconVariant,
  errorHeaderText,
  errorHeaderTextSR,
  errorHeaderStyle,
  errorTextStyle,
  errorText,
  buttonText,
  onButtonClick,
  suggestionList
}: ErrorPageProps) => (
  <div className="payment-text-center payment-mt-60 sm:payment-max-w-[543px] md:payment-max-w-[548px] payment-mx-auto">
    <span aria-label="Warning">
      <span role="img" aria-hidden="true">
        <Icon
          iconClass={"bi_brui"}
          iconName={"bi_exclamation_c"}
          className={variant[iconVariant]}
        />
      </span>
    </span>
    <Heading level="h2" variant="default" className={["payment-text-32 payment-leading-38 payment-text-black payment-mb-5 payment-mt-30", errorHeaderStyle].join(" ").trim()}>
      <span aria-hidden="true">{errorHeaderText}</span>
      <span aria-hidden="false" className="payment-sr-only">{errorHeaderTextSR}</span>
    </Heading>
    <p className={["payment-text-gray payment-overflow-x-hidden payment-font-sans payment-text-[16px] payment-leading-[25px]", errorTextStyle].join(" ").trim()}>{errorText}</p>

    {suggestionList && 
        <>
          <Divider direction="horizontal" width={1} className="payment-my-45 payment-bg-gray-4" />
          <div className="payment-mb-45">
            <Text className="brui-text-14 payment-text-black payment-font-bold">{suggestionList.label}</Text>
            <div className="payment-mt-15 payment-text-left sm:payment-text-center">
              {suggestionList.items.map((item) => (
                <div className="payment-flex sm:payment-justify-center payment-mb-5 last:payment-mb-0 payment-gap-5">
                  <span className="payment-mr-3" role="img" aria-hidden="true">
                    <Icon
                      iconClass={"bi_brui"}
                      iconName={"bi_check_small_flat_fin"}
                      className="payment-text-24 payment-text-blue"
                    />
                  </span>
                  <Text className="payment-leading-18 brui-text-14 payment-mt-3 payment-text-gray">{item}</Text>
                </div>
              ))}
            </div>
          </div>
        </>
    }
      
    <div className="payment-rounded-lg payment-m-20 payment-[focus-within:ring-4]">
      <Button variant="primary" onClick={onButtonClick}>
        {buttonText}
      </Button>
    </div>
  </div>
);

export default ErrorPage;
