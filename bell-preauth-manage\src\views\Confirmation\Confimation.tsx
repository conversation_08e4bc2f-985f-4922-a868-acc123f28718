import * as React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Text } from "@bell/bell-ui-library";
import { FormattedMessage, injectIntl } from "react-intl";
import { SingleRowInformation } from "../SingleRowInformation";
import {  SummaryInformationHeading } from "../SummaryInformationHeading";
import { AlertConfirmationSuccess } from "../Alert";
import { PaymentItem, InputBankAccountDetail, SelectListItem, PaymentItemAccountTypeName, AccountInputValues, CurrentSection, SubscriberOffersWithBan} from "../../models";
import { PaymentSummary } from "../PaymentSummary";
import { State } from "../../store";
import { connect } from "react-redux";
import { getItemAccountTypeName } from "../../utils";
import { IRequestStatus } from "../../models/App";
import { OmnitureOnConfirmation, apiConfirmationStatus, OmnitureOnConfirmationFailure } from "../../store/Actions";
import { getConfirmationOmniture, getOmnitureOnConfirmationFailure } from "../../utils/Omniture";
import Loader from "../Loader/Loader";

interface ConfirmationComponentProps {
  intl: any;
  paymentItem: PaymentItem[];
  checkedBillItems: PaymentItem[];
    
  showPaymentSummary: boolean;
    
  isNewbank: boolean;
  inputValue: any;
  inputBankValue?: InputBankAccountDetail;
  isShow: boolean; // to be cofirm with dave
  isBankPaymentSelected: boolean;
  submitMultiOrderPayment: any;
  submitMultiOrderFormStatus: IRequestStatus;
  BankList: SelectListItem[];
  showCurrentBalance: boolean;
  accountInputValues: AccountInputValues[];
  currentSection: CurrentSection;
  language: "en" | "fr";
  notOptedBalanceItems: PaymentItem[];
  setApiSatusIsFailed: Function;
  creditCardAutopayOffers: SubscriberOffersWithBan[];
  debitCardAutopayOffers: SubscriberOffersWithBan[];
  bankitems: any[];
  setOmnitureOnConfirmation: Function,
  setApiConfirmationStatus: Function;
  apiSatusIsFailed: boolean,
  isBanCreditPreauth: boolean,
  setOmnitureOnConfirmationFailure: Function;
}

const ConfirmationComponent = ({ intl,
  paymentItem,
  checkedBillItems,
        
  showPaymentSummary,
        
  isNewbank,inputValue,
  inputBankValue, 
  isShow, 
  isBankPaymentSelected, 
  submitMultiOrderPayment,
  submitMultiOrderFormStatus,
  accountInputValues,
  BankList, 
  showCurrentBalance,
  currentSection, 
  language,
  notOptedBalanceItems,
  setApiSatusIsFailed,
  creditCardAutopayOffers,
  debitCardAutopayOffers,
  bankitems,
  setOmnitureOnConfirmation,
  setApiConfirmationStatus,
  apiSatusIsFailed,
  isBanCreditPreauth,
  setOmnitureOnConfirmationFailure

}: ConfirmationComponentProps) => {
  const accountTypename : PaymentItemAccountTypeName = {
    MyBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_MYBILL"}),
    Mobility: intl.formatMessage({id: "ACCOUNT_TYPENAME_MOBILITY"}),
    OneBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_ONEBILL"}),
    TV: intl.formatMessage({id: "ACCOUNT_TYPENAME_TV"}),
    Internet: intl.formatMessage({id: "ACCOUNT_TYPENAME_INTERNET"}),
    HomePhone: intl.formatMessage({id: "ACCOUNT_TYPENAME_HOMEPHONE"}),
    MobilityAndOneBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_MOBILITY_ONEBILL"}),
    SingleBan: intl.formatMessage({id: "ACCOUNT_TYPENAME_SINGLEBAN"})
  };
  const checkeddebitOffers = () => {
    const filteredOffer: any = [];
    debitCardAutopayOffers && debitCardAutopayOffers?.map((item) => {
      checkedBillItems && checkedBillItems.map((billItem) => {
        if (item.Ban === billItem.BillName) {
          filteredOffer.push(item);
        }
      });
    });

    return filteredOffer;
  };

  const checkedcreditOffers = () => {
    const filteredOffer: any = [];
    creditCardAutopayOffers && creditCardAutopayOffers?.map((item) => {
      checkedBillItems && checkedBillItems.map((billItem) => {
        if (item.Ban === billItem.BillName) {
          filteredOffer.push(item);
        }
      });
    });

    return filteredOffer;
  };
  const autopayOffers =
    {
      label: intl.formatMessage({ id: "PAYMENT_METHOD" }),
      debits: isBankPaymentSelected ? paymentItem && paymentItem.length > 1 ? checkeddebitOffers() : debitCardAutopayOffers : null,
      credits: !isBankPaymentSelected ? paymentItem && paymentItem.length > 1 ? checkedcreditOffers() : creditCardAutopayOffers : null,
    };

  function hasValidAutopayOffers(autopayOffers:any) {
    return autopayOffers?.[0]?.AutopayEligibleSubscribers?.[0]?.autopayOffers?.length > 0;
  }
  const isshowAutopaysuccess = {
    show: hasValidAutopayOffers(autopayOffers?.credits) || hasValidAutopayOffers(autopayOffers?.debits)
  };

  function getSrText(discount: number, language: string): string {
    const amount = discount.toFixed(2);
    const formattedAmount = language === 'fr' ? amount.replace('.', ',') : amount;
 
    return language === 'en'
      ? `${formattedAmount} dollars per month`
      : `${formattedAmount} dollars par mois`;
  }
  const checkedBanOffers = () => {
    const filteredOffer: any = [];
    !isBankPaymentSelected ?
      creditCardAutopayOffers && creditCardAutopayOffers?.map((item) => {
        checkedBillItems && checkedBillItems?.map((billItem) => {
          if (item.Ban === billItem.BillName) {
            filteredOffer.push(item);
          }
        });
      }):
      debitCardAutopayOffers && debitCardAutopayOffers?.map((item) =>{
        checkedBillItems && checkedBillItems.map((billItem)=>{
          if(item.Ban === billItem.BillName){
            filteredOffer.push(item);
          }
        });
      });
    return filteredOffer;
  };
  const getTotalOffers = () => bankitems && bankitems.length > 1 ? checkedBanOffers().reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {
         
    const offersCount = ban.AutopayEligibleSubscribers.reduce(
      (count, subscriber) => count + subscriber.autopayOffers.length,
      0
    );
    return total + offersCount;
  }, 0): !isBankPaymentSelected ? creditCardAutopayOffers && creditCardAutopayOffers.reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {
         
    const offersCount = ban.AutopayEligibleSubscribers.reduce(
      (count, subscriber) => count + subscriber.autopayOffers.length,
      0
    );
    return total + offersCount;
  }, 0): debitCardAutopayOffers && debitCardAutopayOffers.reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {
         
    const offersCount = ban.AutopayEligibleSubscribers.reduce(
      (count, subscriber) => count + subscriber.autopayOffers.length,
      0
    );
    return total + offersCount;
  }, 0);
  const debitOffers =      
{
  label: intl.formatMessage({id: "PAYMENT_METHOD"}),            
  credits: bankitems && bankitems.length > 1 ? checkedBanOffers() : debitCardAutopayOffers
};
  const CreditOffers =
{
  label: intl.formatMessage({ id: "PAYMENT_METHOD" }),
  credits: bankitems && bankitems.length > 1 ? checkedBanOffers() : creditCardAutopayOffers
};
  const offerImpactGain = (): boolean => 
    debitOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => (credit.offerImpact === "GAIN" ||credit.offerImpact === "" || credit.offerImpact === null)&&(credit.offerImpact !== "RETAIN")) 
      )
    ) || false // Return false if no match found
      ;
  const offerImpactGainCredit = (): boolean => 
    CreditOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => (credit.offerImpact === "GAIN" ||credit.offerImpact === "" || credit.offerImpact === null)&&(credit.offerImpact !== "RETAIN")) 
      )
    ) || false // Return false if no match found
      ;
     
  const offerImpactRemoved = (): boolean => 
    debitOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => credit.offerImpact === "REMOVE")
      )
    ) || false // Return false if no match found
      ;
  const offerImpactRemovedCredit = (): boolean => 
    CreditOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => credit.offerImpact === "REMOVE")
      )
    ) || false // Return false if no match found
      ;
  const offerImpactReduce = (): boolean => 
    debitOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => credit.offerImpact === "REDUCE")
      )
    ) || false // Return false if no match found
      ;
  const offerRetainDebit = (): boolean => 
    (offerImpactGain() || offerImpactIncreaseDebit() || offerImpactReduce() ||offerImpactRemoved() ? false : true) // Return false if no match found
      ;
  const offerRetainCredit = (): boolean => 
    (offerImpactGainCredit() || offerImpactIncrease() || offerImpactReduceCredit() ||offerImpactRemovedCredit() ? false : true) // Return false if no match found
      ;
  const offerImpactIncrease = (): boolean => 
    CreditOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => credit.offerImpact === "INCREASE")
      )
    ) || false // Return false if no match found
      ;
  const offerImpactIncreaseDebit = (): boolean => 
    debitOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => credit.offerImpact === "INCREASE")
      )
    ) || false // Return false if no match found
      ;
  const offerImpactReduceCredit = (): boolean => 
    CreditOffers?.credits?.some((debit: any) =>
      debit.AutopayEligibleSubscribers?.some((item: any) =>
        item?.autopayOffers?.some((credit: any) => credit.offerImpact === "REDUCE")
      )
    ) || false // Return false if no match found
      ;
      
      
  const reduce =      
    {
      
      label: getTotalOffers() >1 ?intl.formatMessage({id: "LABEL_LOADED_OFFERS_REDUCED_DEBIT_TITLE"}): intl.formatMessage({id: "LABEL_LOADED_OFFER_REDUCED_DEBIT_TITLE"})          
      
    };
  const gain =      
    {
      
      label: getTotalOffers() >1 ?intl.formatMessage({id: "LABEL_LOADED_OFFERS_DEBIT_TITLE_CONFIRMATION"}): intl.formatMessage({id: "LABEL_LOADED_OFFER_DEBIT_TITLE_CONFIRMATION"})          
      
    };
  const remove =      
    {
      
      label: getTotalOffers() >1 ?intl.formatMessage({id: "LABEL_LOADED_OFFERS_REMOVED_DEBIT_TITLE_CONFIRMATION"}): intl.formatMessage({id: "LABEL_LOADED_OFFER_REMOVED_DEBIT_TITLE_CONFIRMATION"})          
      
    };
  const increase =      
    {
      
      label: getTotalOffers() >1 ?intl.formatMessage({id: "LABEL_LOADED_OFFERS_INCREASED_CREDIT_TITLE_CONFIRMATION"}): intl.formatMessage({id: "LABEL_LOADED_OFFER_INCREASED_CREDIT_TITLE_CONFIRMATION"})          
      
    };
  const PaymentInformationItems = checkedBillItems.map((item) => ({ 
    label: <FormattedMessage 
      id="SELECT_BILLS_ACCOUNT_TITLE"
      values={{
        accounttype: getItemAccountTypeName(item.AccountType,item.IsNM1Account,accountTypename)
      }} />,
    value: item.NickName,
    key: getItemAccountTypeName(item.AccountType,item.IsNM1Account,accountTypename),
  })
  );
    // let notPaidCurrentBalance: PaymentItem[] = checkedBillItems.filter((item) => !checkedCurrentBalanceItems.includes(item) && item.Due > 0);
   


  const containerRef = React.useRef<HTMLDivElement | null>(null);


  const handleBackClick = () => {
    window.location.href = '/';
  };

  const getOmnitureOnConfirmFailure = () => {
    if(currentSection === CurrentSection.Confirmation)
    {
      if(isBankPaymentSelected)
      {
        setOmnitureOnConfirmationFailure(getOmnitureOnConfirmationFailure(true , checkedBillItems));
      }
      else{
        setOmnitureOnConfirmationFailure(getOmnitureOnConfirmationFailure(false , checkedBillItems , inputValue.cardType));
      }
    }
  };


  React.useEffect(() => {
    if (submitMultiOrderFormStatus === IRequestStatus.FAILED){
      setApiSatusIsFailed(true);
      setApiConfirmationStatus("FAILED");
      getOmnitureOnConfirmFailure();
    } else if (paymentItem.length === 1){
      if(submitMultiOrderFormStatus === IRequestStatus.COMPLETED && Object.values(submitMultiOrderPayment).length > 0){
        for (const item of Object.values(submitMultiOrderPayment) as any) {
          if (item.ErrorCodeID.trim() !== "") {
            setApiSatusIsFailed(true);
            setApiConfirmationStatus("FAILED");
            getOmnitureOnConfirmFailure();
            break; 
          } else {  // added for action call to get the Bell icon on confirmation page
            setApiConfirmationStatus("COMPLETED");
            break;
          }
        }
      }
    }

    if (submitMultiOrderFormStatus === IRequestStatus.COMPLETED && Object.values(submitMultiOrderPayment).length > 0 && paymentItem.length > 1)
    {
      const succeededItems = Object.values(submitMultiOrderPayment).filter((item: any) => !(item.OrderFormStatus === 'Confirmation'));
      if (succeededItems?.length > 0)
      {
        setApiSatusIsFailed(true);
        setApiConfirmationStatus("FAILED");
        getOmnitureOnConfirmFailure();

      }else {
        setApiConfirmationStatus("COMPLETED");
      }
    }
    if(submitMultiOrderFormStatus === IRequestStatus.COMPLETED && Object.values(submitMultiOrderPayment).length > 0){
      if(!apiSatusIsFailed){
        if(isBankPaymentSelected){
          setOmnitureOnConfirmation(getConfirmationOmniture(isBankPaymentSelected,checkedBillItems,Object.values(submitMultiOrderPayment)));
        }
        else{
          setOmnitureOnConfirmation(getConfirmationOmniture(isBankPaymentSelected,checkedBillItems,Object.values(submitMultiOrderPayment), inputValue.cardType));
        }
      }
    }
    
        
    if (submitMultiOrderFormStatus === IRequestStatus.COMPLETED && containerRef.current){
      // For reading heading and title in confirmation in pageload only accessibility
      const firstFocusableElement = containerRef.current.querySelector('.payment-focus-sr');
      if (firstFocusableElement) {
        firstFocusableElement.scrollIntoView({ behavior: 'smooth' });
        (firstFocusableElement as HTMLElement).focus();
      }

      setTimeout(()=>{
        const headingFocusableElement = containerRef?.current?.querySelector('.payment-focus-heading');
        if (headingFocusableElement) {
          headingFocusableElement.scrollIntoView({ behavior: 'smooth' });
          (headingFocusableElement as HTMLElement).focus();
        }           
      },100);

      setTimeout(()=>{
        const firstFocusableElement = containerRef?.current?.querySelector('.payment-focus-sr') as HTMLElement;
        if (firstFocusableElement) {
          firstFocusableElement.style.display = 'none';
        }
      },1000);

      // setTimeout(()=>{
      //     setShowAlertErrorOneTimePayment(true);
      // },200)
    }
  }, [submitMultiOrderFormStatus]); 
    
  const confirmationHeadingTitle = () => {
    if (Object.values(submitMultiOrderPayment).some((item:any) => item?.otp && !item?.otp.isSuccess)){
      return   intl.formatMessage({ id: "TRANSACTION_SUBMITTED_HEADING" });
    }else{
      return   intl.formatMessage({ id: "CONFIRMATION_HEADING" });
    }   
  };
    

  return (
    <>
      {submitMultiOrderFormStatus === IRequestStatus.PENDING &&
                <Loader variant="submitOrder"/>
      }

      {(Object.values(submitMultiOrderPayment).length > 0 && submitMultiOrderFormStatus === IRequestStatus.COMPLETED) && 
        
                <div className="brui-border-gray-4" ref={containerRef}>
                  <div className="payment-mb-15 brui-flex brui-items-center brui-justify-between payment-mt-45" id="ConfirmationDivID">
                    {/* Additional tag for SR only reading*/}
                    <span className="payment-focus-sr payment-sr-only" id="pageConfirmationId"  tabIndex={-1}>{intl.formatMessage({id: "PAGE_TITLE_CONFIRMATON"})}</span>
                    <Heading 
                      className="payment-focus-heading" 
                      level="h2" 
                      variant="lg" 
                      tabIndex={-1}
                      id={isShow ? "payment-manage-heading" : undefined}
                      aria-hidden={isShow ? "true" : undefined}>
                      {confirmationHeadingTitle()}
                    </Heading>
                  </div>

                  {/* {(!isBankPaymentSelected && Object.values(submitMultiOrderPayment).length > 0 && Object.values(submitMultiOrderPayment).some((item:any) => item?.otp && !item?.otp.isSuccess) && showAlertErrorOneTimePayment) && (
                    <div className="payment-pt-15 payment-pb-0">
                        <AlertErrorOneTimePayment 
                            checkedCurrentBalanceItems={checkedCurrentBalanceItems}
                            submitMultiOrderPayment={Object.values(submitMultiOrderPayment)} 
                            accountInputValue={accountInputValues} 
                            language={language}
                            notOptedBalanceItems={notOptedBalanceItems} 
                        />
                    </div>
                )} */}

                  <div className="payment-pt-15 payment-pb-15">
                    <AlertConfirmationSuccess 
                      submitMultiOrderPayment={Object.values(submitMultiOrderPayment)} 
                      accountInputValue={accountInputValues} 
                      isBankPayment={isBankPaymentSelected}
                      checkedBillItems={checkedBillItems}
                      language={language}
                      paymentItem = {paymentItem}
                      creditCardAutopayOffers = {creditCardAutopayOffers}
                      debitCardAutopayOffers={debitCardAutopayOffers}
                      isBanCreditPreauth={isBanCreditPreauth}
                    />
                        
                  </div>
                
                
            
                  {/* {(paymentItem.find(item => item.AccountType != PaymentItemAccountType.OneBill) && notPaidCurrentBalance.length > 0 && Object.values(submitMultiOrderPayment).some((item:any) => item?.otp && !item?.otp.isSuccess) == false)  && (
                    <div className="payment-pb-15">
                        <AlertWarningWithSomeBalancesPaidMulti
                        multiban={paymentItem.length > 1 ? true : false}
                        submitMultiOrderPayment={Object.values(submitMultiOrderPayment)}
                        accountInputValue={accountInputValues} 
                        paymentItem={paymentItem}
                        language={language}
                        />
                    </div>
                 )}

                 
                {paymentItem.find(item => item.AccountType === PaymentItemAccountType.OneBill) && (
                <div className="payment-pb-15">
                    <AlertConfirmationInfo
                    />
                </div>
                
                )} */}
                  <div className="payment-block payment-border payment-rounded-20 payment-relative payment-px-15 sm:payment-px-30 payment-py-30 sm:payment-pb-45">
                    <div>
                      <Heading level="h3" variant="md">
                        {intl.formatMessage({ id: "PAYMENT_SUMMARY_TITLE" })}
                      </Heading>
                    </div>
                    <div className="payment-mt-30 sm:payment-mt-30">
                      <SummaryInformationHeading
                        title={intl.formatMessage({ id: "BILL_INFORMATION_TITLE" })}
                      >
                        {PaymentInformationItems.map((item, index) => (
                          <SingleRowInformation
                            className={index > 0 ? "payment-mt-5" : ""}
                            label={item.label}
                            value={item.value}
                          />
                        ))}
                      </SummaryInformationHeading>
                    </div>
                    <div className="payment-mt-45">
                      <SummaryInformationHeading title={intl.formatMessage({ id: "PAYMENT_INFORMATION_TITLE" })}>
                        <PaymentSummary paymentItem={paymentItem} className={showPaymentSummary ? "" : "payment-hidden" } inputValue={inputValue} isNewbank={isNewbank} isPreauth={false} inputBankValue={inputBankValue} showHeading={false} isBankPaymentSelected={isBankPaymentSelected} bankList={BankList}creditCardAutopayOffers={creditCardAutopayOffers}
                          debitCardAutopayOffers={debitCardAutopayOffers}
                          checkedBillItems={checkedBillItems}bankitems={bankitems}isConfirmation={true}></PaymentSummary>
                        {/* {PaymentInformationItems.map((item, index) => (
                                <SingleRowInformation
                                    className={index > 0 ? "payment-mt-5" : ""}
                                    label={item.label}
                                    value={item.value}
                                    role="listitem"
                                />
                            ))} */}
                      </SummaryInformationHeading>
                    </div>
                    {isshowAutopaysuccess.show ?
                      isBankPaymentSelected && !offerRetainDebit() ? 
                        <div className="payment-mt-45">                                
                          <SummaryInformationHeading title={intl.formatMessage({ id: "AUTOPAY_CREDITS_TITLE" })} role="list">
                            {offerImpactReduce()? <>
                              <Text elementType="div" className="payment-flex payment-items-center payment-mt-5 payment-pb-15">
                                <Icon className="payment-text-yellow paymnet-text-15 payment-mr-5" iconClass="bi_brui" iconName="bi_error_bl_bg_cf"></Icon>
                                <p className="payment-text-gray payment-text-14">{reduce.label} </p>
                              </Text>
                            </>
                              :null}
                            {offerImpactGain()?<>
                              <Text elementType="div" className="payment-flex payment-items-center payment-mt-5 payment-pb-15">
                                <Icon className="paymnet-text-15 payment-text-blue payment-mr-5" iconClass="bi_brui" iconName="bi_tag_note-big"></Icon>
                                <p className="payment-text-gray payment-text-14">{gain.label} </p>
                              </Text>
                            </>
                              :null}
                            {offerImpactRemoved()?
                              <>
                                <Text elementType="div" className="payment-flex payment-items-center payment-mt-5 payment-pb-15">
                                  <Icon className="payment-text-yellow paymnet-text-15 payment-mr-5" iconClass="bi_brui" iconName="bi_error_bl_bg_cf"></Icon>
                                  <p className="payment-text-gray payment-text-14">{remove.label} </p>
                                </Text>
                              </>
                              :null}
                            {offerImpactIncreaseDebit()? <>
                              <Text elementType="div" className="payment-flex payment-items-center payment-mt-5 payment-pb-15">
                                <Icon className="paymnet-text-15 payment-text-blue payment-mr-5" iconClass="bi_brui" iconName="bi_tag_note-big"></Icon>
                                <p className="payment-text-gray payment-text-14">{increase.label} </p>
                              </Text>
                            </>
                              :null}
                            {offerImpactGain() ? (
                              <>
                                {autopayOffers?.debits?.map((debit: any) =>
                                  debit.AutopayEligibleSubscribers?.map((item: any) => {
                                    // Check if any offer in this group is not "RETAIN"
                                    const hasNonRetain = item?.autopayOffers?.some((offer: any) => offer.offerImpact !== "RETAIN");
                                    return (
                                      <React.Fragment key={item.subscriberTelephoneNumber}>
                                        {checkedBillItems && checkedBillItems.length > 1 && hasNonRetain ? (
                                          <p className="payment-text-14 payment-font-bold">
                                            {debit.banInfo && debit.banInfo.nickName}
                                          </p>
                                        ) : null}
                                        {item?.autopayOffers?.map((offer: any, idx: number) =>
                                          item.autopayOffers[0].offerImpact !== "RETAIN" ? (
                                            <SingleRowInformation
                                              key={idx}
                                              label={item.subscriberTelephoneNumber}
                                              value={
                                                language === "en"
                                                  ? "$" + offer.discountAmount.toFixed(2) + "/mo."
                                                  : offer.discountAmount.toFixed(2).replace('.', ',') + " $/mois"
                                              }
                                              needSRText
                                              // srText={
                                              //     language == "en"
                                              //         ? offer.discountAmount.toFixed(2) + " dollars per month"
                                              //         : offer.discountAmount.toFixed(2).replace('.', ',') + " dollars par mois"
                                              // }
                                              srText={getSrText(offer.discountAmount,language)}
                                              role="listitem"
                                              isMultiBan={checkedBillItems.length > 1}
                                              className={checkedBillItems.length > 1 ? "payment-text-gray" : "payment-text-black"}
                                            />
                                          ) : null
                                        )}
                                      </React.Fragment>
                                    );
                                  })
                                )}
                              </>
                            ) : null}
                            {offerImpactIncreaseDebit() ? (
                              <>
                                {debitOffers?.credits?.map((debit: any) =>
                                  debit.AutopayEligibleSubscribers?.map((item: any) => {
                                    // Check if any offer in this group is not "RETAIN"
                                    const hasNonRetain = item?.autopayOffers?.some((credit: any) => credit.offerImpact !== "RETAIN");
                                    return (
                                      <React.Fragment key={item.subscriberTelephoneNumber}>
                                        {bankitems && bankitems.length > 1 && hasNonRetain ? (
                                          <p className="payment-text-14 payment-text-gray">
                                            {debit.banInfo && debit.banInfo.nickName}:
                                          </p>
                                        ) : null}
                                        {item?.autopayOffers?.map((credit: any, idx: number) =>
                                          item.autopayOffers[0].offerImpact !== "RETAIN" ? (
                                            <SingleRowInformation
                                              key={idx}
                                              label={<strong>{item.subscriberTelephoneNumber}</strong>}
                                              value={
                                                <div className="payment-inline-block">
                                                  from{" "}
                                                  <Price
                                                    className="payment-text-gray payment-inline payment-lowercase !payment-text-14 payment-font-normal"
                                                    price={credit.currentdiscountAmount}
                                                    variant="defaultPrice"
                                                    suffixText="perMonth"
                                                    language={language}
                                                  />{" "}
                                                  to &nbsp;
                                                  <Price
                                                    className="payment-inline payment-lowercase !payment-text-14 payment-font-normal"
                                                    price={credit.discountAmount}
                                                    variant="defaultPrice"
                                                    suffixText="perMonth"
                                                    language={language}
                                                  />
                                                </div>
                                              }
                                              role="listitem"
                                              isMultiBan={checkedBillItems.length > 1}
                                              className="payment-text-black"
                                            />
                                          ) : null
                                        )}
                                      </React.Fragment>
                                    );
                                  })
                                )}
                              </>
                            ) : null}
                            {offerImpactReduce() ?(
                              <>
                                {debitOffers?.credits?.map((debit: any) =>
                                  debit.AutopayEligibleSubscribers?.map((item: any) => {
                                    // Check if any offer in this group is not "RETAIN"
                                    const hasNonRetain = item?.autopayOffers?.some((credit: any) => credit.offerImpact !== "RETAIN");
                                    return (
                                      <React.Fragment key={item.subscriberTelephoneNumber}>
                                        {bankitems && bankitems.length > 1 && hasNonRetain ? (
                                          <p className="payment-text-14 payment-text-gray">
                                            {debit.banInfo && debit.banInfo.nickName}:
                                          </p>
                                        ) : null}
                                        {item?.autopayOffers?.map((credit: any, idx: number) =>
                                          item.autopayOffers[0].offerImpact !== "RETAIN" ? (
                                            <SingleRowInformation
                                              key={idx}
                                              label={<strong>{item.subscriberTelephoneNumber}</strong>}
                                              value={
                                                <div className="payment-inline-block">
                                                  from{" "}
                                                  <Price
                                                    className="payment-text-gray payment-inline payment-lowercase !payment-text-14 payment-font-normal"
                                                    price={credit.currentdiscountAmount}
                                                    variant="defaultPrice"
                                                    suffixText="perMonth"
                                                    language={language}
                                                  />{" "}
                                                  to &nbsp;
                                                  <Price
                                                    className="payment-inline payment-lowercase !payment-text-14 payment-font-normal"
                                                    price={credit.discountAmount}
                                                    variant="defaultPrice"
                                                    suffixText="perMonth"
                                                    language={language}
                                                  />
                                                </div>
                                              }
                                              role="listitem"
                                              isMultiBan={checkedBillItems.length > 1}
                                              className="payment-text-black"
                                            />
                                          ) : null
                                        )}
                                      </React.Fragment>
                                    );
                                  })
                                )}
                              </>
                            ) : null} 
                            {offerImpactRemoved()?(
                              <>
                                {autopayOffers?.debits?.map((debit: any) =>
                                  debit.AutopayEligibleSubscribers?.map((item: any) => {
                                    // Check if any offer in this group is not "RETAIN"
                                    const hasNonRetain = item?.autopayOffers?.some((offer: any) => offer.offerImpact !== "RETAIN");
                                    return (
                                      <React.Fragment key={item.subscriberTelephoneNumber}>
                                        {checkedBillItems && checkedBillItems.length > 1 && hasNonRetain ? (
                                          <p className="payment-text-14 payment-font-bold">
                                            {debit.banInfo && debit.banInfo.nickName}
                                          </p>
                                        ) : null}
                                        {item?.autopayOffers?.map((offer: any, idx: number) =>
                                          item.autopayOffers[0].offerImpact !== "RETAIN" ? (
                                            <SingleRowInformation
                                              key={idx}
                                              label={item.subscriberTelephoneNumber}
                                              value={
                                                language === "en"
                                                  ? "$" + offer.discountAmount.toFixed(2) + "/mo."
                                                  : offer.discountAmount.toFixed(2).replace('.', ',') + " $/mois"
                                              }
                                              needSRText
                                              // srText={
                                              //     language == "en"
                                              //         ? offer.discountAmount.toFixed(2) + " dollars per month"
                                              //         : offer.discountAmount.toFixed(2).replace('.', ',') + " dollars par mois"
                                              // }
                                              srText={getSrText(offer.discountAmount,language)}
                                              role="listitem"
                                              isMultiBan={checkedBillItems.length > 1}
                                              className={checkedBillItems.length > 1 ? "payment-text-gray" : "payment-text-black"}
                                            />
                                          ) : null
                                        )}
                                      </React.Fragment>
                                    );
                                  })
                                )}
                              </>
                            ) : null}     

                          </SummaryInformationHeading>
                          <div className="brui-mt-15">
                            <p
                              className="brui-text-gray brui-text-12 brui-leading-14"
                            ><strong>Note: </strong>{intl.formatMessage({ id: "MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE" })}</p>

                          </div>
                        </div>
                        :
                        !offerRetainCredit() ?
                          <div className="payment-mt-45">
                            <SummaryInformationHeading title={intl.formatMessage({ id: "AUTOPAY_CREDITS_TITLE" })} role="list">
                              {offerImpactIncrease()? <>
                                <Text elementType="div" className="payment-flex payment-items-center payment-mt-5 payment-pb-15">
                                  <Icon className="payment-text-yellow paymnet-text-15 payment-mr-5" iconClass="bi_brui" iconName="bi_error_bl_bg_cf"></Icon>
                                  <p className="payment-text-gray payment-text-14">{increase.label} </p>
                                </Text>
                              </>
                                :null}
                              {offerImpactReduceCredit()?
                                <>
                                  <Text elementType="div" className="payment-flex payment-items-center payment-mt-5 payment-pb-15">
                                    <Icon className="payment-text-yellow paymnet-text-15 payment-mr-5" iconClass="bi_brui" iconName="bi_error_bl_bg_cf"></Icon>
                                    <p className="payment-text-gray payment-text-14">{reduce.label} </p>
                                  </Text>
                                </>
                                :null}
                              {offerImpactGainCredit()?<>
                                <Text elementType="div" className="payment-flex payment-items-center payment-mt-5 payment-pb-15">
                                  <Icon className="paymnet-text-15 payment-text-blue payment-mr-5" iconClass="bi_brui" iconName="bi_tag_note-big"></Icon>
                                  <p className="payment-text-gray payment-text-14">{gain.label} </p>
                                </Text>
                              </>
                                :null}
                              {offerImpactRemovedCredit()?
                                <>
                                  <Text elementType="div" className="payment-flex payment-items-center payment-mt-5 payment-pb-15">
                                    <Icon className="payment-text-yellow paymnet-text-15 payment-mr-5" iconClass="bi_brui" iconName="bi_error_bl_bg_cf"></Icon>
                                    <p className="payment-text-gray payment-text-14">{remove.label} </p>
                                  </Text>
                                </>
                                :null}
                              {offerImpactGainCredit() ? (
                                <>
                                  {autopayOffers?.credits?.map((debit: any) =>
                                    debit.AutopayEligibleSubscribers?.map((item: any) => {
                                      // Check if any offer in this group is not "RETAIN"
                                      const hasNonRetain = item?.autopayOffers?.some((offer: any) => offer.offerImpact !== "RETAIN");
                                      return (
                                        <React.Fragment key={item.subscriberTelephoneNumber}>
                                          {checkedBillItems && checkedBillItems.length > 1 && hasNonRetain ? (
                                            <p className="payment-text-14 payment-font-bold">
                                              {debit.banInfo && debit.banInfo.nickName}
                                            </p>
                                          ) : null}
                                          {item?.autopayOffers?.map((offer: any, idx: number) =>
                                            item.autopayOffers[0].offerImpact !== "RETAIN" ? (
                                              <SingleRowInformation
                                                key={idx}
                                                label={item.subscriberTelephoneNumber}
                                                value={
                                                  language === "en"
                                                    ? "$" + offer.discountAmount.toFixed(2) + "/mo."
                                                    : offer.discountAmount.toFixed(2).replace('.', ',') + " $/mois"
                                                }
                                                needSRText
                                                // srText={
                                                //     language == "en"
                                                //         ? offer.discountAmount.toFixed(2) + " dollars per month"
                                                //         : offer.discountAmount.toFixed(2).replace('.', ',') + " dollars par mois"
                                                // }
                                                srText={getSrText(offer.discountAmount,language)}
                                                role="listitem"
                                                isMultiBan={checkedBillItems.length > 1}
                                                className={checkedBillItems.length > 1 ? "payment-text-gray" : "payment-text-black"}
                                              />
                                            ) : null
                                          )}
                                        </React.Fragment>
                                      );
                                    })
                                  )}
                                </>
                              ) : null}
                              {offerImpactIncrease() ? (
                                <>
                                  {CreditOffers?.credits?.map((debit: any) =>
                                    debit.AutopayEligibleSubscribers?.map((item: any) => {
                                      // Check if any offer in this group is not "RETAIN"
                                      const hasNonRetain = item?.autopayOffers?.some((credit: any) => credit.offerImpact !== "RETAIN");
                                      return (
                                        <React.Fragment key={item.subscriberTelephoneNumber}>
                                          {bankitems && bankitems.length > 1 && hasNonRetain ? (
                                            <p className="payment-text-14 payment-text-gray">
                                              {debit.banInfo && debit.banInfo.nickName}:
                                            </p>
                                          ) : null}
                                          {item?.autopayOffers?.map((credit: any, idx: number) =>
                                            item.autopayOffers[0].offerImpact !== "RETAIN" ? (
                                              <SingleRowInformation
                                                key={idx}
                                                label={<strong>{item.subscriberTelephoneNumber}</strong>}
                                                value={
                                                  <div className="payment-inline-block">
                                                    from{" "}
                                                    <Price
                                                      className="payment-text-gray payment-inline payment-lowercase !payment-text-14 payment-font-normal"
                                                      price={credit.currentdiscountAmount}
                                                      variant="defaultPrice"
                                                      suffixText="perMonth"
                                                      language={language}
                                                    />{" "}
                                                    to &nbsp;
                                                    <Price
                                                      className="payment-inline payment-lowercase !payment-text-14 payment-font-normal"
                                                      price={credit.discountAmount}
                                                      variant="defaultPrice"
                                                      suffixText="perMonth"
                                                      language={language}
                                                    />
                                                  </div>
                                                }
                                                role="listitem"
                                                isMultiBan={checkedBillItems.length > 1}
                                                className="payment-text-black"
                                              />
                                            ) : null
                                          )}
                                        </React.Fragment>
                                      );
                                    })
                                  )}
                                </>
                              ) : null}
                              {offerImpactReduceCredit() ?(
                                <>
                                  {CreditOffers?.credits?.map((debit: any) =>
                                    debit.AutopayEligibleSubscribers?.map((item: any) => {
                                      // Check if any offer in this group is not "RETAIN"
                                      const hasNonRetain = item?.autopayOffers?.some((credit: any) => credit.offerImpact !== "RETAIN");
                                      return (
                                        <React.Fragment key={item.subscriberTelephoneNumber}>
                                          {bankitems && bankitems.length > 1 && hasNonRetain ? (
                                            <p className="payment-text-14 payment-text-gray">
                                              {debit.banInfo && debit.banInfo.nickName}:
                                            </p>
                                          ) : null}
                                          {item?.autopayOffers?.map((credit: any, idx: number) =>
                                            item.autopayOffers[0].offerImpact !== "RETAIN" ? (
                                              <SingleRowInformation
                                                key={idx}
                                                label={<strong>{item.subscriberTelephoneNumber}</strong>}
                                                value={
                                                  <div className="payment-inline-block">
                                                    from{" "}
                                                    <Price
                                                      className="payment-text-gray payment-inline payment-lowercase !payment-text-14 payment-font-normal"
                                                      price={credit.currentdiscountAmount}
                                                      variant="defaultPrice"
                                                      suffixText="perMonth"
                                                      language={language}
                                                    />{" "}
                                                    to &nbsp;
                                                    <Price
                                                      className="payment-inline payment-lowercase !payment-text-14 payment-font-normal"
                                                      price={credit.discountAmount}
                                                      variant="defaultPrice"
                                                      suffixText="perMonth"
                                                      language={language}
                                                    />
                                                  </div>
                                                }
                                                role="listitem"
                                                isMultiBan={checkedBillItems.length > 1}
                                                className="payment-text-black"
                                              />
                                            ) : null
                                          )}
                                        </React.Fragment>
                                      );
                                    })
                                  )}
                                </>
                              ) : null}
                              {offerImpactRemovedCredit()?(
                                <>
                                  {autopayOffers?.credits?.map((debit: any) =>
                                    debit.AutopayEligibleSubscribers?.map((item: any) => {
                                      // Check if any offer in this group is not "RETAIN"
                                      const hasNonRetain = item?.autopayOffers?.some((offer: any) => offer.offerImpact !== "RETAIN");
                                      return (
                                        <React.Fragment key={item.subscriberTelephoneNumber}>
                                          {checkedBillItems && checkedBillItems.length > 1 && hasNonRetain ? (
                                            <p className="payment-text-14 payment-font-bold">
                                              {debit.banInfo && debit.banInfo.nickName}
                                            </p>
                                          ) : null}
                                          {item?.autopayOffers?.map((offer: any, idx: number) =>
                                            item.autopayOffers[0].offerImpact !== "RETAIN" ? (
                                              <SingleRowInformation
                                                key={idx}
                                                label={item.subscriberTelephoneNumber}
                                                value={
                                                  language === "en"
                                                    ? "$" + offer.discountAmount.toFixed(2) + "/mo."
                                                    : offer.discountAmount.toFixed(2).replace('.', ',') + " $/mois"
                                                }
                                                needSRText
                                                // srText={
                                                //     language == "en"
                                                //         ? offer.discountAmount.toFixed(2) + " dollars per month"
                                                //         : offer.discountAmount.toFixed(2).replace('.', ',') + " dollars par mois"
                                                // }
                                                srText={getSrText(offer.discountAmount,language)}
                                                role="listitem"
                                                isMultiBan={checkedBillItems.length > 1}
                                                className={checkedBillItems.length > 1 ? "payment-text-gray" : "payment-text-black"}
                                              />
                                            ) : null
                                          )}
                                        </React.Fragment>
                                      );
                                    })
                                  )}
                                </>
                              ) : null}    

                                           


                            </SummaryInformationHeading>
                            <div className="brui-mt-15">
                              <p
                                className="brui-text-gray brui-text-12 brui-leading-14"
                              ><strong>Note: </strong>{intl.formatMessage({ id: "MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE" })}</p>
                            </div>
                          </div>:""
                      : ""}
                    {/* {showCurrentBalance &&
                        <div className="payment-mt-45">
                            <SummaryInformationHeading title={intl.formatMessage({ id: "CURRENT_BALANCE_TITLE" })}>
                                
                            {(!isBankPaymentSelected && Object.values(submitMultiOrderPayment).length > 0 && Object.values(submitMultiOrderPayment).some((item:any) =>  item?.otp && !item?.otp.isSuccess) && !hasSomeOtpErrors)  && (
                                <div className="payment-flex payment-items-start payment-mb-15">
                                    <Icon className="brui-text-15 brui-text-red brui-mr-10 payment-mt-2" iconClass="bi_brui" iconName="bi_error_bl_bg_cf"></Icon>
                                    <p className="brui-text-red brui-text-14">{intl.formatMessage({ id: "SUMMARY_CURRENT_BAL_OTP_FAILURE" })}</p>
                                </div>
                            )}
                                <div className="payment-text-gray payment-text-14 payment-mt-5">
                                    <p className="payment-leading-18" dangerouslySetInnerHTML={{ __html: currentBalancesisOpted() }}/>
                                </div>
                                <div>
                                    {paymentItem.length > 1 ?
                                        <>
                                            {checkedCurrentBalanceItems.map((item) => (
                                                <MultiBanInformation accountinfo={item.NickName} role="list" childrole="listitem" 
                                                className="first:payment-mt-15 payment-mb-15 last:payment-mb-0"
                                                isLabelOnError={(hasSomeOtpErrors && failedOtpItems.filter((x:any) => x.Ban == item.Ban).length > 0) ? true : false}
                                                >
                                                    <>
                                                        {(hasSomeOtpErrors && failedOtpItems.filter((x:any) => x.Ban == item.Ban).length > 0) && (
                                                            <div className="payment-flex payment-items-start payment-mb-5">
                                                                <Icon className="brui-text-15 brui-text-red brui-mr-10 payment-mt-2" iconClass="bi_brui" iconName="bi_error_bl_bg_cf"></Icon>
                                                                <p className="brui-text-red brui-text-14">{intl.formatMessage({ id: "SUMMARY_CURRENT_BAL_OTP_FAILURE" })}</p>
                                                            </div>
                                                        )}
                                                        <SingleRowInformation
                                                            label={failedOtpItems.filter((x:any) => x.Ban == item.Ban).length > 0
                                                                ? intl.formatMessage({ id: "UNPAID_BALANCE" })
                                                                : intl.formatMessage({ id: "PAYMENT_AMOUNT" })
                                                            }
                                                            value={
                                                                <Price
                                                                    language={language}
                                                                    price={item.Due}
                                                                    variant="ordinaryPrice"
                                                                    className="!brui-text-14 brui-leading-18"/>
                                                                }
                                                            needSRText
                                                            srText={`${item.Due} dollars`}
                                                            className="payment-text-black" 
                                                            isMultiBan={true}/>
                                                    </>
                                                </MultiBanInformation>
                                            ))} 
                                        </> :
                                        <>
                                            {checkedCurrentBalanceItems.map((item) => (
                                                <SingleRowInformation
                                                    label={confirmationPayAmountLabel()}
                                                    value={
                                                        <Price
                                                            language={language}
                                                            price={item.Due}
                                                            variant="ordinaryPrice"
                                                            className="!brui-text-14 brui-leading-18"/>
                                                    }
                                                    needSRText
                                                    srText={`${item.Due} dollars`}
                                                    className="payment-text-black first:payment-mt-15 payment-mb-15 last:payment-mb-0"
                                                />
                                            ))} 
                                        </>
                                    }
                                </div>
                            </SummaryInformationHeading>
                        </div>
                    } */}
                  </div>
                  <div className="payment-mt-30 payment-pb-45 sm:payment-mb-60 border-1 payment-border-b-1 payment-border-b-[#CDCFD5] sm:payment-border-none payment-mx-[-16px] payment-px-[16px] sm:payment-mx-0 sm:payment-px-0">
                    <Button size="regular" variant="secondary" onClick={handleBackClick} >
                      {intl.formatMessage({ id: "BACK_TO_MY_BELL" })}
                    </Button>
                  </div>
                </div>
      }
    </>
  );
};

const mapStateToProps = (state: State) => ({
  submitMultiOrderPayment: state.submitMultiOrderPayment,
  submitMultiOrderFormStatus: state.submitMultiOrderFormStatus,
});
const mapDispatchToProps = (dispatch: React.Dispatch<any>) => ({ 
  setOmnitureOnConfirmation: (data?: any) => dispatch(OmnitureOnConfirmation({data})),
  setApiConfirmationStatus: (type: any) => dispatch(apiConfirmationStatus({ type })),
  setOmnitureOnConfirmationFailure: (data? : any) => dispatch(OmnitureOnConfirmationFailure({data}))
});
 

export const Confirmation = connect(mapStateToProps, mapDispatchToProps)(injectIntl(ConfirmationComponent));


