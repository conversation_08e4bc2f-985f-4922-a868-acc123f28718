import * as React from "react";
import { forwardRef, useState } from "react";


import {
  RadioButton,
  Label
} from "@bell/bell-ui-library";
import { PaymentItem } from "../../models";

export interface FormFieldsBankPaymentRadioProps {
  intl?: any,
  isErrorCase?: boolean,
  legends?: string,
  isBankPayment?: boolean,
  isCreditCardPayment?: boolean,
  children?: React.ReactNode,
  srOnly: string,
}
interface ChildProps {
  defaultChecked?: boolean;
  showBankFieldsOnChange?: boolean;
}

export const FormFieldsBankRadioPayment = (
  {
    intl,isErrorCase, children, legends, isBankPayment, isCreditCardPayment,srOnly
  }:FormFieldsBankPaymentRadioProps) => {
  const getInitialState = () => {
    let initialState = false;
    React.Children.forEach(children, (child) => {
      if (React.isValidElement<ChildProps>(child) && child.props && child.props.defaultChecked && child.props.showBankFieldsOnChange) {
        initialState = true;
      }
    });
    return initialState;
  };

  const [ bankPaymentShown, setBankPaymentShown ] = useState(getInitialState);
  const handleRadioChange = (isShown: boolean) => {
    setBankPaymentShown(isShown); 
  };
  return( 
    <fieldset>
      <legend className="brui-sr-only">{srOnly}</legend>
      <div className="brui-flex brui-flex-col">
        <div className="brui-flex brui-flex-col sm:brui-flex-row">
          <div className="sm:payment-w-[174px] sm:payment-min-w-[174px] sm:payment-mr-30 sm:payment-text-right payment-pt-13 payment-pb-13 sm:payment-pb-0 payment-mb-10 sm:payment-mb-0">
            <Label className="brui-block" required>{legends}</Label>
          </div>
          <div className="brui-flex brui-flex-col payment-gap-15">
            {React.Children.map(children, (child, index) => React.cloneElement(child as React.ReactElement<any>, {
              showOnChange: handleRadioChange,
              // hasError: isErrorCase,
              childIndex: index
            }))}
          </div>
        </div>
      </div>
      {bankPaymentShown}
    </fieldset>
  );
};

interface BankPaymentRadioButtonProps {
  name?: string;
  value: string;
  hasError?: boolean;
  showBankFieldsOnChange?: boolean;
  showOnChange?: (showBankFieldsOnChange: boolean) => void;
  childIndex?: number;
  label: string;
  idPrefix?: string;
  defaultChecked?: boolean;
  className?: string;
  onChange?: React.ChangeEventHandler<HTMLInputElement> | undefined;
  getExistingBankPaymentDetails?: any;
  paymentDetails?: PaymentItem[];
}



export const BankPaymentRadioButton = forwardRef<HTMLInputElement,BankPaymentRadioButtonProps>(function BankPaymentRadioButton(
  {
    name = "bank-payment-radio",
    value,
    hasError,
    showOnChange,
    showBankFieldsOnChange = false,
    childIndex,
    defaultChecked,
    className,
    label,
    onChange,
    idPrefix = "",
    getExistingBankPaymentDetails,
    paymentDetails,
  },forwardedRef) {
  
  const handleClick = (details:any) => {
    if (typeof getExistingBankPaymentDetails === "function") {
      getExistingBankPaymentDetails(details || []);
    }
  };

  React.useEffect(() => {
    if (paymentDetails && defaultChecked) {
      getExistingBankPaymentDetails(paymentDetails || []);
    }
  }, []);

  return (
    <RadioButton
      className={[className,"brui-flex brui-items-center brui-absolute brui-size-full brui-opacity-0 enabled:brui-cursor-pointer disabled:brui-cursor-default brui-z-10"].join(" ").trim()}
      id={idPrefix + "bank-payment-radio-id-" + childIndex}
      name={name}
      value={value}
      variant="boxedInMobile"
      hasError={hasError}
      // onChange={() => showOnChange? showOnChange(showBankFieldsOnChange): {onChange}}
      defaultChecked={defaultChecked}
      ref={forwardedRef}
      onChange={onChange}
      onClick={() => handleClick(paymentDetails)}
    >
      <div className="brui-text-14 brui-leading-18 brui-mt-3" dangerouslySetInnerHTML={{__html: label}}></div>
    </RadioButton>
  );
});
