import { ComponentPropsWithRef } from 'react';
import { PaymentItem } from "./PaymentItem";
import { IBankInfoRes, IGetRedirectUrl, IBankInfoFailure } from './RedirectUrl';
import SubscriberOffersWithBan from './PreauthorizePayment';

export interface BankPaymentProps extends ComponentPropsWithRef<"input"> {
  intl: any;
  Checked: boolean;
  isInteracSelected: boolean;
  errorBankName?: boolean;
  errorBankTransit?: boolean;
  errorBankAccountNumber?: boolean;
  errorBankAccountHolderName?: boolean;
  inputRefs: {
    inputCreditCardNumber: React.RefObject<HTMLInputElement>,
    inputCreditCardHolderName: React.RefObject<HTMLInputElement>,
    inputCreditCardSecurityCode: React.RefObject<HTMLInputElement>,
    inputCreditCardExpiryMonth: React.RefObject<HTMLSelectElement>,
    inputCreditCardExpiryYear: React.RefObject<HTMLSelectElement>,
    inputBankName: React.RefObject<HTMLSelectElement>,
    inputBankAccountHolder: React.RefObject<HTMLInputElement>,
    inputTransitNumber: React.RefObject<HTMLInputElement>,
    inputBankAccountNumber: React.RefObject<HTMLInputElement>,
  };
  isBankChecked?: boolean;
  isBankManualEnterDetails?: boolean;
  handleBankRadioManualDetailsChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleBankRadioChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  hasBankAccountDetails?: PaymentItem;
  bankitems: any[];
  bankListInterac: any[];
  handleInteracSubmit?: () => void;
  radioRef: React.RefObject<HTMLInputElement>;
  radioCardRef: {
      interac: React.RefObject<HTMLInputElement>;
      manualDetails: React.RefObject<HTMLInputElement>;
  };
  isPreauth?: boolean;
  bankList: any[];
  redirectUrl: IGetRedirectUrl;
  interacBankInfo: IBankInfoRes;
  checkedBillItems: PaymentItem[];
  interactBankFailureInfo:IBankInfoFailure;
  creditCardAutopayOffers:SubscriberOffersWithBan[];
  debitCardAutopayOffers:SubscriberOffersWithBan[];
  language: "en" | "fr";
  managePreauth?: string;
  IsAutopayCreditEnabled:boolean;
  IsInteracEnabled:boolean;
}

