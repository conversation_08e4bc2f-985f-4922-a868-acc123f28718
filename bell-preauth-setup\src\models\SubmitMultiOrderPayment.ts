export interface CreditCard {
  IsValid: boolean;
  IsDTSValidated: boolean;
  IsDTSOutage: boolean;
  Token: string;
  OriginalToken: string | null;
  CreditCardNumberMasked: string;
  CreditCardNumber: string;
  CreditCardType: string;
  CardHolderName: string;
  ExpiryYear: string;
  ExpiryMonth: string;
  SecurityCodeMasked: string;
  SecurityCode: string;
  IsTokenizationValid: boolean;
  IsTokenEncrypted: boolean;
  ValidatedToken: string | null;
  PaymentStatus: string;
  AuthorizationCode: string | null;
  ErrorList: any | null;
  CreditCardErrorList: any | null;
  ValidatedCVV: string | null;
  ValidatedExpireMonth: string | null;
  ValidatedExpireYear: string | null;
  ValidationServiceSource: string;
  CardStatus: string;
  AmountBeingPaid: number | null;
  isUpdatingCreditCard: boolean;
  ExpirationDateDisplay: string;
  AccountNumber: string | null;
  BFFToken: string | null;
}

export interface OTP {
  isSuccess: boolean;
  ErrorCodeID: string | null;
  ErrorCodeDescription: string | null;
}

export interface Subscriber {
  AccountNumber: string | null;
  SubscriberNumber: string | null;
  AccountType: string;
  MobileDeviceNumber: string | null;
  Nickname: string | null;
  SubscriberName: string | null;
  FirstName: string | null;
  LastName: string | null;
  SingleSubscriber: boolean;
  SubscriberBalance: number;
  IsConectedCar: boolean;
  IsCDMAUser: boolean;
  HasTravelRestriced: boolean;
  HasLimitedTravelAccess: boolean;
  ShowNewDayPassBanner: boolean;
  IsFAQLinkAndSearch: boolean;
  RomeBetterBanner: boolean;
  NM1SOCFeatures: boolean;
  SyniverseSOCFeature: boolean;
  IsFeatureChanges: boolean;
  BillPeriod: string | null;
  PPUUsageDetails: string | null;
  PrepaidAccountBalance: number | null;
  InSufficientBalance: boolean;
  ContractType: number;
  ContractTerm: number;
  IsBussinessAccountUser: boolean;
  IsCorporateAccountUser: boolean;
  IsDataMandatory: boolean;
  ShowPreAuthorizedTopupLink: boolean;
  AnniversaryDay: number;
  SubscriberProvince: string | null;
}

export interface CurrentServiceAccountInfo {
  Subscriber: Subscriber;
  Device: Device;
  RatePlan: RatePlan;
}

export interface Device {
  DeviceName: string | null;
  TelephoneNumber: string | null;
  IMEINumber: string | null;
  DeviceMaskedSIMNumber: string | null;
  IMEIMasked: string | null;
  ModelNumber: string | null;
  DeviceType: string | null;
  StepByStepTutorialLink: string | null;
  ManufacturerGuideLink: string | null;
  DeviceImageLink: string | null;
  GenericImageLink: string | null;
  CommitmentPeriodEndDate: string;
  DeviceBalanceRemaining: number | null;
  DeviceBalanceEndDate: string | null;
  OutstandingBalance: boolean;
  DeviceOrderTrackingId: string | null;
  ThresholdLevel: number;
  CanUpgradeDevice: boolean;
  CanTransferServiceToOtherDevice: boolean;
  CanTransferServicetoOtherSIM: boolean;
  CanUnlockSIM: boolean;
  CanUnlockDevice: boolean;
  DevicePrice: number;
  IsDwbbAccount: boolean;
  IsUnlockSimEnable: boolean;
  IsUnlockDeviceEnable: boolean;
  IsWCoCSubscriber: boolean;
  CanViewAgreement: boolean;
  HasManufacturerGuide: boolean;
  RetrieveMyPuk: boolean;
  SIM: string | null;
  EID: string | null;
  VoiceMailPassword: string | null;
  SerialNumber: string | null;
  IsDeviceUnderWarranty: boolean;
  CanUserChangeSIM: boolean;
  CanUserTransferDevice: boolean;
  DeviceGroups: string[] | null;
  HasDeferredDiscount: boolean;
  DeviceReturnAmount: number;
  DepreciateDiscountAmount: number;
  MonthlyInstallment: number;
  MonthlyRebate: number;
  DownloadESIMGuideContent: string | null;
  DownloadESIMGuideUrl: string | null;
}

export interface RatePlan {
  PlanId: string | null;
  PlanName: string | null;
  PlanName_FR: string | null;
  Zone: string;
  CanChangeBasePlan: boolean;
  PlanPrice: {
    Amount: number;
    ChargeFrequency: string;
  };
  PlanFeatures: string[];
  EffectiveDate: string | null;
  ExpirationDate: string | null;
  OptionalSocs: any | null;
  AdditionalInfo: string | null;
  PromoGroup: string | null;
  EligibleHUGPromoContractTypes: any | null;
  Tier: string | null;
  IsUnlimitedPlan: boolean;
  IsSharedPlan: boolean;
  SharingGroupCodes: any | null;
  FriendlyName: string | null;
  RatePlanFlag: any | null;
  KeyFeatures: any | null;
  Tagline: string | null;
  PlanDiscountInfo: any | null;
  IsPlanDiscountOnProfile: boolean;
}

export interface SubmitMultiOrderPayment {
  BankAccountInfo: any;
  CreditCard: CreditCard;
  RegisterPADStatus: any;
  RegisterPACCStatus: number;
  IsPADPACValidated: boolean;
  selectedPaymentMethod: string;
  PrevPymType: number;
  LastUpdateDate: string;
  LastUpdateStamp: number;
  AccountNumberMaskedDisplayView: string;
  TransitCodeMaskedDisplayView: string;
  otp: OTP;
  OrderFormId: string;
  OrderFormStatus: string;
  CreationDate: string;
  CurrentServiceAccountInfo: CurrentServiceAccountInfo;
  Features: any;
  ConfirmationEmailAddress: string;
  IsPaymentRequired: boolean;
  HasMultiLineIncentivesAvailable: boolean;
  Actions: any[];
  OrderFormType: string;
  PaymentConfirmationNumber: string;
  ConfirmationNumber: string | null;
  ErrorCodeID: string;
  ErrorCodeDescription: string;
  Category: any[];
  ShareGroupSummary: any[];
  VMChangeStatus: number;
  NewVoiceMailPassword: string | null;
  SelectedPlan: any;
  IsEffectiveDateSkipped: boolean;
  ShareGroupNewAllocation: {
    Amount: number | null;
    UnitOfMeasure: string;
  };
  IsDeviceVerificationRequired: boolean;
  IsDeviceVerified: boolean;
  IsSIMVerified: boolean;
  IsOwnershipVerified: boolean;
  OmniturePrd: any | null;
  PaymentMethod: string;
  Notifications: any[];
  LastChoosenCountry: string | null;
  ImportantSection: any | null;
  IsSessionExpire: boolean;
  KnowYourCredit: any | null;
  KnowYourCredit_FR: any[];
  VoiceMailText: string | null;
  ConfirmationWarningMessagesToShow: any[];
  ConfirmationWarningMessagesToShowInEmail: any[];
  UserHadVMFeature: boolean;
  HasAddons: boolean;
  HasBussinessAcountBanner: boolean;
  MoreDetail: any | null;
  FormattedOrderDate: string | null;
  ReviewCMS: any[];
  hasInsufficientBalance: boolean;
  NewTranactionEmailAddress: string | null;
  UseNM1OrderIdForConfirmations: boolean;
  GetCurrentSolutionFeatureList: any[];
  GetNewSolutionFeatureList: any[];
  HasRatePlanChanged: boolean;
  IsEppCustomer: boolean;
  isAALEligible: boolean;
  ShowAALBanner: boolean;
  IsTradeInEnable: boolean;
  TradeInDROURL: string | null;
  TradeInDROURL_FR: string | null;
  TradeInCTA: string | null;
  DRODeferredAmount: number | null;
  DRODeviceReturnDate: string | null;
  IsUnlimitedSharedUsageEnable: boolean;
}

