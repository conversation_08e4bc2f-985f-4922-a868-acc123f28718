import * as React from "react";
import { Provider } from "react-redux";
import { Widget, ViewWidget, Lo<PERSON>, LoggerConfigKeys } from "bwtk";
import Store from "./store";
import { App } from "./App";
import Config from "./Config";
import { createMultiPaymentAction, getPassKey } from "./store/Actions";
import { AccountInputValues, PaymentItemAccountType } from "./models";
import { getBanSpecificTransactionId } from "./utils";
import { Root } from "react-dom/client";

@Widget({ namespace: "Preauth/Setup" })
export default class extends ViewWidget {
  constructor(
    private store: Store,
    private config: Config,
    logger: Logger
  ) {
    super();
    void logger;
  }

  init() {
    this.config.setConfig(LoggerConfigKeys.SeverityLevel, this.config.logLevel);

    // Validate payment items exist before processing
    if (!this.config.getPaymentItem || this.config.getPaymentItem.length === 0) {
      console.error('No payment items available for initialization');
      return;
    }

    if (this.config.getPaymentItem.length !== 1) {
      console.error('Expected exactly one payment item, got:', this.config.getPaymentItem.length);
      return;
    }

    const paymentItem = this.config.getPaymentItem[0];

    // Dispatch initial actions
    this.store.dispatch(getPassKey({
      ban: paymentItem.Ban,
      sub: paymentItem.subscriberId
    }));

    const accountInputValue: AccountInputValues[] = [{
      accountNumber: paymentItem.Ban,
      subNumber: paymentItem.subscriberId,
      transactionID: getBanSpecificTransactionId(paymentItem.Ban, this.config.transactionIdArray),
      payBalanceAmnt: 0,
    }];

    this.store.dispatch(createMultiPaymentAction({
      ban: paymentItem.Ban,
      type: (paymentItem.AccountType === PaymentItemAccountType.OneBill),
      details: accountInputValue,
      sub: paymentItem.subscriberId
    }));
  }

  destroy() {
    this.store.destroy();
  }

  render(root: Root) {
    const { store, config } = this;
    root.render(
      <Provider store={store}>
        <App Config={config} />
      </Provider>
    );
  }
}
