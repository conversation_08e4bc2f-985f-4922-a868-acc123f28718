import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>dalBody, <PERSON>dal<PERSON><PERSON><PERSON>, <PERSON><PERSON>Header } from "@bell/bell-ui-library";
import { injectIntl } from "react-intl";
import { OmnitureOnFindTransactionLightBox } from "../../store/Actions";
import { connect } from "react-redux";

interface FindYourTransactionProps {
  intl: any;
  setOmnitureOnFindTransactionLightBox: Function;
}
const FindYourTransaction = ({ intl , setOmnitureOnFindTransactionLightBox }:FindYourTransactionProps) => {    
  const [isModalOpen, setIsModalOpen] = useState(false);
  const onToggleModal = (isOpen: boolean) => {
    setIsModalOpen(isOpen);
  };
  const handleButtonClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault(); 
    onToggleModal(true); 
    setTimeout(() => {
      setOmnitureOnFindTransactionLightBox();
    }, 1000);
  };

  const MODAL_BANK_STATEMENT_DESC = intl.formatMessage({ id: "MODAL_BANK_STATEMENT_DESC" });
  const MODAL_TRANSIT_NUMBER_DESC = intl.formatMessage({ id: "MODAL_TRANSIT_NUMBER_DESC" });
  const MODAL_ACCOUNT_NUMBER_DESC = intl.formatMessage({ id: "MODAL_ACCOUNT_NUMBER_DESC" });

  return (
    <div>
      <Button 
        variant="textBlue"
        onClick={handleButtonClick}
        size="small"
        className="payment-text-14 payment-leading-18 payment-flex"
      >
        {intl.formatMessage({ id: "MODAL_TRANSIT_ACCOUNT_NUMBERS_TITLE"})}
      </Button>

      {isModalOpen && (
        <Modal
          id="transit-and-account"
          aria-labelledby="transit-and-account-title"
          onEscapeKeyPressed={() => onToggleModal(false)}
          onOverlayClick={() => onToggleModal(false)}>
          <ModalContent 
            useDefaultRadius={false}
            className="payment-rounded-10"
          >
            <ModalHeader
              variant="lightGrayBar"
              rightButtonIcon="default"
              title={intl.formatMessage({ id: "MODAL_TRANSIT_ACCOUNT_NUMBERS_TITLE"})}
              isDefaultPadding={false}
              className="payment-px-15 sm:payment-px-30 payment-py-25"
              onRightButtonClicked={() => onToggleModal(false)}
              rightButtonLabel={intl.formatMessage({id: "CTA_CLOSE"})}
            />
            <ModalBody 
              id="transit-and-account-body"
              isDefaultPadding={false}
              className="payment-px-15 sm:payment-px-30 payment-py-30"
            >
              <div className="payment-text-gray payment-text-14 payment-leading-18">
                <div className="payment-flex payment-flex-col payment-justify-between">
                  <div className="payment-flex payment-flex-col sm:payment-flex-row">
                    <div className="payment-w-full sm:payment-max-w-[293px]">
                      <Heading 
                        level="h3" 
                        variant="default"
                        className="payment-font-sans payment-text-black payment-font-bold"
                      >
                        {intl.formatMessage({ id: "MODAL_BANK_STATEMENT_TITLE"})}
                      </Heading>
                      <div className="payment-pt-15" dangerouslySetInnerHTML={{ __html: MODAL_BANK_STATEMENT_DESC }}></div>  
                      <div className="payment-pt-15">
                        <div dangerouslySetInnerHTML={{ __html: MODAL_TRANSIT_NUMBER_DESC }}></div>
                        <div dangerouslySetInnerHTML={{ __html: MODAL_ACCOUNT_NUMBER_DESC }}></div>     
                      </div>
                    </div>
                    <div className="payment-w-full payment-pt-30 sm:payment-pt-0 sm:payment-ml-30">
                      <img src={intl.formatMessage({ id: "TRANSIT_ACC_NO_PNG" })} alt="" className="payment-w-full" />
                    </div>
                  </div>
                </div>
              </div>
            </ModalBody>
          </ModalContent>
        </Modal>
      )}
    </div>
  );
};

const mapDispatchToProps = (dispatch: React.Dispatch<any>) => ({ 
  setOmnitureOnFindTransactionLightBox: (data?:any) => dispatch(OmnitureOnFindTransactionLightBox({data}))
});
export const LightBoxFindYourTransaction = connect(null ,mapDispatchToProps)(injectIntl(FindYourTransaction));
