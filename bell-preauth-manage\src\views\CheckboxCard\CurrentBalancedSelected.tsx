

import React from "react";
import { HeadingStep, IconLink, Icon, Price } from "@bell/bell-ui-library";
import { injectIntl } from "react-intl";
import {SingleRowInformation} from "../SingleRowInformation";
import { MultiBanInformation } from "../SummaryInformationHeading";
import { CurrentSection, PaymentItem } from "../../models";
// import { string } from "prop-types";

interface CurrentBalancedSelectedProps {
  intl: any;
  isActive?: boolean;
  onIconLinkClick?: (steps:any) => void;
  isCheckedBalanceItems: PaymentItem[];
  checkedBillItems: PaymentItem[];
  paymentItem: PaymentItem[];
  isBankPaymentSelected: boolean;
  currentSection: CurrentSection
  language: "en" | "fr";
}

let notPaidCurrentBalance:PaymentItem[];
const CurrentBalancedSelectedComponent = ({ 
  intl, 
  isActive,
  onIconLinkClick, 
  isCheckedBalanceItems, 
  checkedBillItems, 
  paymentItem, 
  isBankPaymentSelected,
  currentSection,
  language = "en" 
}:CurrentBalancedSelectedProps) => {

  const PAY_CURRENT_BALANCE_OPTED_IN = (isBankPaymentSelected ? intl.formatMessage(
    { id: "PAY_CURRENT_BALANCE_OPTED_IN" },
    { balance: new Intl.NumberFormat(language, { style: "currency", currency: "USD", currencyDisplay: "narrowSymbol", }).format(0.00) }) 
    :
    (currentSection === CurrentSection.Confirmation ? "" :intl.formatMessage(
      { id: "PAY_CURRENT_BALANCE_OPTED_IN_PACC" })
    )
  );
  const PAY_CURRENT_BALANCE_OPTED_OUT_SINGULAR = intl.formatMessage({ id: "PAY_CURRENT_BALANCE_OPTED_OUT_SINGULAR" });
  const PAY_CURRENT_BALANCE_OPTED_OUT_PLURAL = intl.formatMessage({ id: "PAY_CURRENT_BALANCE_OPTED_OUT_PLURAL" });
    
  const currentBalancesisOpted  = () => {
    if (isCheckedBalanceItems.length === 0) {
      if (checkedBillItems.length - isCheckedBalanceItems.length > 1){ // none opted
        return PAY_CURRENT_BALANCE_OPTED_OUT_PLURAL;
      } else {
        return PAY_CURRENT_BALANCE_OPTED_OUT_SINGULAR;
      }
    }
    else if(isCheckedBalanceItems.every(item => checkedBillItems.includes(item))){ // all opted in
      return PAY_CURRENT_BALANCE_OPTED_IN;
    } else if (isCheckedBalanceItems.some(item => checkedBillItems.includes(item))) { // some opted in
      return PAY_CURRENT_BALANCE_OPTED_IN; // update erd for some opted in
    }
  };
    
  if (checkedBillItems.length > 0) {
    notPaidCurrentBalance = checkedBillItems.filter(
      (billItem) => !isCheckedBalanceItems.some(
        (CurrentBalanceItem) => CurrentBalanceItem.BanID === billItem.BanID
      )
    );
  }

  const filteraccount = notPaidCurrentBalance.filter(account => account.Due > 0);

  return (
    <div>
      <div className={isActive ? "payment-block payment-border-gray-4 payment-border-b payment-border-t payment-mt-45" : "sm:payment-mb-15 payment-border-b payment-border-gray-4 payment-hidden"}>
        <div>
          <div className="payment-flex payment-items-center payment-justify-between payment-mt-0">
            <HeadingStep
              disableSrOnlyText={true}
              status="complete"
              subtitle=""
              hideSubtitle
              variant="leftAlignNoStep"
              title={intl.formatMessage({ id: "PAY_CURRENT_BALANCE_HEADING"})}
              id="payment-manage-heading"
            />
            <div className="payment-pt-45">
              <IconLink
                icon={<Icon iconClass="bi_brui" iconName="bi_edit_pencil" className="brui-text-16"></Icon>}
                text={intl.formatMessage({id: "CTA_EDIT"})}
                variant="textBlue"
                size="regular"
                href="javascript:void(0);"
                position="right"
                className="payment-flex payment-items-center !payment-text-14 !payment-leading-18"
                onClick={() => onIconLinkClick && onIconLinkClick(1)} // Pass the click event
                // </div>aria-describedby="pre-auth-pay_curr_bal"
                aria-label={intl.formatMessage({ id: "PAY_CURRENT_BALANCE_HEADING"})}>
              </IconLink>
            </div>
          </div>
          <div className="payment-pb-45">
            <div className="payment-text-gray payment-text-14 payment-mt-5">
              <p className="payment-leading-18" dangerouslySetInnerHTML={{ __html: currentBalancesisOpted() }} />
            </div>
            {isCheckedBalanceItems.length > 0 &&
                            <div className="payment-pt-15">
                              {paymentItem.length > 1 ?
                                <>
                                  {isCheckedBalanceItems.map((item) => (
                                    <MultiBanInformation accountinfo={item.NickName} role="list" childrole="listitem" className="payment-mb-15 last:payment-mb-0">
                                      <SingleRowInformation
                                        label={intl.formatMessage({ id: "PAYMENT_AMOUNT" })}
                                        value={
                                          <Price
                                            language={language}
                                            price={item.Due}
                                            variant="ordinaryPrice"
                                            className="!brui-text-14 brui-leading-18"/>
                                        }
                                        needSRText
                                        srText={`${item.Due} dollars`}
                                        className="payment-text-black" 
                                        isMultiBan={true}
                                      />
                                                
                                    </MultiBanInformation>
                                        
                                  ))}
                                  {notPaidCurrentBalance.length > 0 && checkedBillItems.length !== notPaidCurrentBalance.length && (
                                    <>
                                      <div className="payment-text-gray payment-text-14 payment-mt-5">
                                        <p className="payment-leading-18">
                                          {
                                            notPaidCurrentBalance.some(account => account.Due > 0) && (
                                              <>
                                                {filteraccount.length > 1 && (
                                                // Some Bans - Multiple Opted Out
                                                  intl.formatMessage({ id: "PAY_CURRENT_BALANCE_SOME_BANS_OPTED_OUT_PLURAL" })
                                                )}
                                                {filteraccount.length === 1 && (
                                                // Some Bans - Single Opted Out
                                                  intl.formatMessage({ id: "PAY_CURRENT_BALANCE_SOME_BANS_OPTED_OUT_SINGULAR" })
                                                )}
                                              </>
                                            )}
                                        </p>
                                      </div>
                                    </>
                                  )}
                                </> :
                                <>
                                  {isCheckedBalanceItems.map((item) => (
                                    <SingleRowInformation
                                      label={intl.formatMessage({ id: "PAYMENT_AMOUNT" })}
                                      value={
                                        <Price
                                          language={language}
                                          price={item.Due}
                                          variant="ordinaryPrice"
                                          className="!brui-text-14 brui-leading-18"/>
                                      }
                                      needSRText
                                      srText={`${item.Due} dollars`}
                                      className="payment-text-black"
                                    />
                                  ))} 
                                </>
                              }
                            </div>
            }
          </div>
        </div>
      </div>
    </div>      
  );
};

export const CurrentBalancedSelected = injectIntl(CurrentBalancedSelectedComponent);
