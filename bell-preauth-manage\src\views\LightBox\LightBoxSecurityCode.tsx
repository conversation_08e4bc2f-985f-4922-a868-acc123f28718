import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, <PERSON>dal<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader } from "@bell/bell-ui-library";
import { injectIntl } from "react-intl";
import { connect } from "react-redux";
import { OmnitureOnSecurityCodeLightBox } from "../../store/Actions";


interface LightboxSecurityCodeProps {
  intl: any;
  setOmnitureOnSecurityCodeLightBox: Function;
}

const LightBoxSecurityCodeComponent = ({intl , setOmnitureOnSecurityCodeLightBox}:LightboxSecurityCodeProps) => {    
  const [isModalOpen, setIsModalOpen] = useState(false);
  const onToggleModal = (isOpen: boolean) => {
    setIsModalOpen(isOpen);
  };
  const handleButtonClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault(); 
    onToggleModal(true); 
    setTimeout(() => {
      setOmnitureOnSecurityCodeLightBox();
    }, 1000);
  };
  return (
    <div>
      <Button
        variant="textBlue"
        onClick={handleButtonClick}
        size="small"
        className="payment-text-14 payment-leading-18"
      >
        {intl.formatMessage({id: "MODAL_SECURITY_CODE"})}
      </Button>

      {isModalOpen && (
        <Modal
          id="what-is-security-code"
          aria-labelledby="what-is-security-code-title"
          onEscapeKeyPressed={() => onToggleModal(false)}
          onOverlayClick={() => onToggleModal(false)}
        >
          <ModalContent useDefaultRadius={false} className="payment-rounded-10">
            <ModalHeader
              variant="lightGrayBar"
              rightButtonIcon="default"
              title={intl.formatMessage({id: "MODAL_SECURITY_CODE_TITLE"})}
              isDefaultPadding={false}
              className="payment-px-15 sm:payment-px-30 payment-py-25"
              onRightButtonClicked={() => onToggleModal(false)}
              rightButtonLabel={intl.formatMessage({id: "CTA_CLOSE"})}
            />
            <ModalBody id ="what-is-security-code-body" isDefaultPadding={false} className="payment-px-15 sm:payment-px-30 payment-py-30">
              <div className="payment-text-gray payment-text-14 payment-leading-18">
                <p>
                  {intl.formatMessage({id: "MODAL_SECURITY_CODE_DESC"})}
                </p>
                <div className="payment-flex payment-flex-col sm:payment-flex-row payment-justify-between payment-pt-15">
                  <div className="payment-w-full sm:payment-max-w-[262.5px]">
                    <Heading level="h3" variant="default" className="payment-font-sans payment-text-black payment-font-bold">
                      {intl.formatMessage({id: "CARD_TYPE_VISA_MASTERCARD"})}
                    </Heading>
                    <div className="payment-pt-15">
                      {intl.formatMessage({id: "CARD_TYPE_VISA_MASTERCARD_DESC"})}
                    </div>
                    <div className="payment-pt-15">
                      <img src={intl.formatMessage({ id: "BACK_CC_PNG" })} alt="" className="payment-h-[132px]" />
                    </div>
                  </div>
                  <Divider direction="vertical" width={1} className="payment-mx-30 payment-hidden sm:payment-block" />
                  <Divider direction="horizontal" width={1} className="payment-my-30 payment-block sm:payment-hidden" />
                  <div className="payment-w-full sm:payment-max-w-[262.5px]">
                    <Heading level="h3" variant="default" className="payment-font-sans payment-text-black payment-font-bold">
                      {intl.formatMessage({id: "CARD_TYPE_AMERICAN_EXP"})}
                    </Heading>
                    <div className="payment-pt-15">
                      {intl.formatMessage({id: "CARD_TYPE_AMERICAN_EXP_DESC"})}
                    </div>
                    <div className="payment-pt-15">
                      <img src={intl.formatMessage({ id: "FRONT_CC_PNG" })} alt="" className="payment-h-[130px]" />
                    </div>
                  </div>
                </div>
              </div>
            </ModalBody>
          </ModalContent>
        </Modal>
      ) }
    </div>
  );
};

const mapDispatchToProps = (dispatch: React.Dispatch<any>) => ({ 
  setOmnitureOnSecurityCodeLightBox: (data?:any) => dispatch(OmnitureOnSecurityCodeLightBox({data}))
});

export const LightBoxSecurityCode = connect(null , mapDispatchToProps)(injectIntl(LightBoxSecurityCodeComponent));
