import { Injectable, Logger as BwtkLogger} from "bwtk";

/**
 * This class removes the need to use console.log() directly,
 * which is also forbidden by linting rules.
 */
@Injectable
export class Logger {
  constructor(private bwtkLogger: BwtkLogger) {}

  log(...p: any[]) {
    this.bwtkLogger.log(...p);
  }

  warn(...p: any[]) {
    this.bwtkLogger.warn(...p);
  }

  debug(...p: any[]) {
    this.bwtkLogger.debug(...p);
  }

  info(...p: any[]) {
    this.bwtkLogger.info(...p);
  }

  error(...p: any[]) {
    this.bwtkLogger.error(...p);
  }
}
