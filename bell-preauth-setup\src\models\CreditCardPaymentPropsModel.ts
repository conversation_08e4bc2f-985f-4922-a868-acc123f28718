import { ComponentPropsWithRef } from 'react';
import { PaymentItem } from "./PaymentItem";
import SubscriberOffersWithBan from './PreauthorizePayment';

export interface CreditCardPaymentProps extends ComponentPropsWithRef<"input"> {
  intl: any;
  Checked?: boolean;
  isPreauth?: boolean;
  errorCardNumber?: boolean;
  hasCreditCardDetails?: PaymentItem;
  errorCardName?: boolean;
  errorExpiryDate?: boolean;
  errorSecurityCode?: boolean;
  cardNumber?: string;
  cardType?: string;
  cardIcons: { [key: string]: string };
  bankitems?: any;
  radioRef?: React.RefObject<HTMLInputElement>;
  inputRefs: {
      inputCreditCardNumber: React.RefObject<HTMLInputElement>;
      inputCreditCardHolderName: React.RefObject<HTMLInputElement>;
      inputCreditCardExpiryMonth: React.RefObject<HTMLSelectElement>;
      inputCreditCardExpiryYear: React.RefObject<HTMLSelectElement>;
      inputCreditCardSecurityCode: React.RefObject<HTMLInputElement>;
  };
  handleBankRadioChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleCreditCardChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  isBankChecked?: boolean;
  handleMaskCVV?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  CVV?: string;
  creditCardAutopayOffers:SubscriberOffersWithBan[];
  debitCardAutopayOffers:SubscriberOffersWithBan[];
  checkedBillItems:PaymentItem[];
  language: "en" | "fr";
  IsAutopayCreditEnabled:boolean;
}

