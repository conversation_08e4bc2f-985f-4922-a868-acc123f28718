import * as React from "react";
import { Alert, Heading, Text } from "@bell/bell-ui-library";
import { injectIntl } from "react-intl";


export const AlertConfirmationSuccessCancelComponent = ({intl}: any) => {

  const CancelFailedSuccessScenarioHeading = intl.formatMessage({id: "CANCEL_FAILED_SUCCESS_SCENARIO_HEADING"});
    
  return (
    <Alert
      variant="success"
      className="payment-border payment-relative sm:payment-py-30 sm:payment-px-30 payment-rounded-20 payment-p-16 payment-px-15 payment-py-30 sm:payment-flex sm:payment-items-center payment-block"
      iconSize="36"
      id="alert-3">
      <Text elementType="div" className="payment-pl-0 payment-pt-15 sm:payment-pl-16 sm:payment-pt-0">
        <Heading level="h2" variant="xs" className="payment-font-sans payment-leading-22">
          <span dangerouslySetInnerHTML={{ __html: CancelFailedSuccessScenarioHeading}}></span>
        </Heading>
      </Text>
    </Alert>
  );
};

export const AlertConfirmationSuccessCancel = injectIntl(AlertConfirmationSuccessCancelComponent);
