import * as React from "react";
import {Alert, Heading, Text} from "@bell/bell-ui-library";
import AlertNotificationListItem from "./AlertNotificationListItem";
import AlertNotificationList from "./AlertNotificationList";
import { injectIntl } from "react-intl";

interface InputRefs {
  inputCreditCardNumber: React.RefObject<HTMLInputElement>;
  inputCreditCardHolderName: React.RefObject<HTMLInputElement>;
  inputCreditCardSecurityCode: React.RefObject<HTMLInputElement>;
  inputCreditCardExpiryYear: React.RefObject<HTMLSelectElement>;
  inputCreditCardExpiryMonth: React.RefObject<HTMLSelectElement>;
    
  inputBankAccountNumber: React.RefObject<HTMLInputElement>;
  inputTransitNumber: React.RefObject<HTMLInputElement>;
  inputBankAccountHolder: React.RefObject<HTMLInputElement>;
  inputBankName: React.RefObject<HTMLSelectElement>;
}

interface ErrorFormListProps {
  intl: any;
  isErrorCardNumber?: boolean;
  isErrorCardName?: boolean;
  isErrorExpiryDate?: boolean;
  isErrorSecurityCode?: boolean;
  isErrorBankAccountHolderName?: boolean; 
  isErrorBankAccountNumber ?: boolean;
  isErrorBankName?: boolean; 
  iserrorBankTransit?: boolean;
  inputRefs?: InputRefs;
}

const AlertCreditCardErrorFormListComponent = ({intl, isErrorCardNumber, isErrorCardName, isErrorExpiryDate, isErrorSecurityCode, isErrorBankAccountHolderName, isErrorBankAccountNumber, isErrorBankName, iserrorBankTransit, inputRefs}: ErrorFormListProps) => (
  <Alert 
    variant="error" 
    className="payment-block sm:payment-flex brui-px-0 sm:payment-px-30 payment-py-30 brui-border-x-0 sm:payment-border-x-1 payment-border-y-1 payment-relative brui-rounded-none sm:payment-rounded-20" 
    iconSize="36"
    id="alert-3">
    <Text elementType="div" className="payment-pl-0 payment-pt-10 sm:payment-pl-15 sm:payment-pt-0">
      <Heading level="h2" variant="xs" className=" sm:payment-mt-7 brui-mb-15 brui-font-sans brui-leading-22">
        <span  id ="error-1" aria-hidden="true">{intl.formatMessage({id: "ALERT_ERROR_HEADING"})}</span>
        <span className="payment-sr-only">{intl.formatMessage({id: "ALERT_ERROR_HEADING_SR"})}</span>
      </Heading>
      <div>
        <AlertNotificationList>
          {isErrorCardNumber && (
            <AlertNotificationListItem 
              id="error-2"
              label={intl.formatMessage({id: "CREDIT_CARD_NUMBER_LABEL"})} 
              labelDescription={intl.formatMessage({id: "ALERT_ERROR_GENERAL_DESC"})} 
              variant="errorList"
              inputRef={inputRefs?.inputCreditCardNumber}/>
          )}
              
          {isErrorCardName && (
            <AlertNotificationListItem 
              id="error-3"
              label={intl.formatMessage({id: "CREDIT_CARD_NAME_LABEL"})} 
              labelDescription={intl.formatMessage({id: "ALERT_ERROR_GENERAL_DESC"})} 
              variant="errorList"
              inputRef={inputRefs?.inputCreditCardHolderName}/>
          )}

          {isErrorExpiryDate && (
            <AlertNotificationListItem 
              id="error-4"
              label={intl.formatMessage({id: "CREDIT_CARD_EXPIRY_LABEL"})} 
              labelDescription={intl.formatMessage({id: "ALERT_ERROR_GENERAL_DESC"})} 
              variant="errorList"
              inputRef={inputRefs?.inputCreditCardExpiryMonth}/>
          )}

          {isErrorSecurityCode && (
            <AlertNotificationListItem 
              id= "error-5"
              label={intl.formatMessage({id: "CREDIT_CARD_SECURITY_CODE_INPUT_LABEL"})} 
              labelDescription={intl.formatMessage({id: "ALERT_ERROR_GENERAL_DESC"})} 
              variant="errorList"
              inputRef={inputRefs?.inputCreditCardSecurityCode}/>
          )}

          {isErrorBankName && (
            <AlertNotificationListItem
              id="error-2"
              label={intl.formatMessage({ id: "BANK_NAME_LABEL" })}
              labelDescription={intl.formatMessage({ id: "ALERT_ERROR_GENERAL_DESC" })}
              variant="errorList"
              inputRef={inputRefs?.inputBankName} />
          )}

          {isErrorBankAccountHolderName && (
            <AlertNotificationListItem
              id="error-3"
              label={intl.formatMessage({ id: "BANK_HOLDER_NAME_LABEL" })}
              labelDescription={intl.formatMessage({ id: "ALERT_ERROR_GENERAL_DESC" })}
              variant="errorList"
              inputRef={inputRefs?.inputBankAccountHolder} />
          )}

          {iserrorBankTransit && (
            <AlertNotificationListItem
              id="error-4"
              label={intl.formatMessage({ id: "BANK_TRANSIT_NUMBER_LABEL" })}
              labelDescription={intl.formatMessage({ id: "ALERT_ERROR_GENERAL_DESC" })}
              variant="errorList"
              inputRef={inputRefs?.inputTransitNumber} />
          )}

          {isErrorBankAccountNumber && (
            <AlertNotificationListItem
              id="error-5"
              label={intl.formatMessage({ id: "BANK_ACCOUNT_NUMBER_LABEL" })}
              labelDescription={intl.formatMessage({ id: "ALERT_ERROR_GENERAL_DESC" })}
              variant="errorList"
              inputRef={inputRefs?.inputBankAccountNumber} />
          )}
              
        </AlertNotificationList>
      </div>
    </Text>
  </Alert>
);


export const AlertCreditCardErrorFormList = injectIntl(AlertCreditCardErrorFormListComponent);
