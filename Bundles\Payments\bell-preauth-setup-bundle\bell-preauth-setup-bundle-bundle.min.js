/*! bell-preauth-setup-bundle (bundle) 1.0.0 | bwtk 6.1.0 | 2025-07-22T13:00:12.375Z */
var factory;factory=function(e,t,n,r,a,i,o,l,u){return function(){function s(e){var t,n=m[e];return void 0!==n?n.exports:(t=m[e]={exports:{}},d[e](t,t.exports,s),t.exports)}var c,d=[,function(t){"use strict";t.exports=e},function(e,t,n){var r,a,i,o,l,u,s,c,d;self,e.exports=(r=n(1),a=n(3),i=n(4),o=n(5),l=n(6),u=n(7),s=n(8),c=n(9),d=n(10),function(){function e(t){var r,a=m[t];return void 0!==a?a.exports:(r=m[t]={exports:{}},n[t].call(r.exports,r,r.exports,e),r.exports)}var t,n=[,function(e){"use strict";e.exports=r},function(e){"use strict";e.exports=a},function(e){"use strict";e.exports=i},function(e){"use strict";e.exports=o},function(e){"use strict";e.exports=l},function(e){"use strict";e.exports=JSON.parse('{"en":{"ALERT_CONFIRMATION_INFO_HEADING":"Avoid late fees by paying your current balance.","ALERT_CONFIRMATION_INFO_DESC":"Pre-authorized payments will only begin on your next billing period. Any existing balance on your account must be paid separately with a final one-time payment, or risk late fees.","ALERT_CONFIRMATION_INFO_BUTTON_DESC":"Make a payment","ALERT_CONFIRMATION_INFO_DESC_1":"Pre-authorized payments will only begin on your next billing period. You must pay your ","ALERT_CONFIRMATION_INFO_DESC_2":"balance in a separate and final one-time payment, or risk late fees. ","EXISTING_CC_TITLE":"Select a credit Card","AUTOPAY_TRANSACTION_NOTE_DESC":"If you have any upcoming changes to your account, they may impact the Autopay credits above. Any changes to the credits will take effect within 1 to 2 billing periods.","AUTOPAY_TRANSACTION_NOTE_HEADING":"Note:","EXISTING_BANK_TITLE":"Select a bank account","EXISTING_BANK_TITLE_SR":"Requried, Select a bank account","EXISTING_BANK_ACCOUNT_MANUAL_DETAILS_LABEL":"Choose a bank account already on file or add a new one manually","SELECT_BILLS_HEADING":"Select bills","SELECT_BILLS_HEADING_SINGULAR":"Select bill","SELECT_BILLS_ACCOUNT_TITLE":"{accounttype} account number","SELECT_BILLS_HEADING_DESC":"For which bill(s) do you want to update the payment information?","SELECT_BANK_TITLE":"Select a bank account","ACCOUNT_TYPE_ONE_BILL":"One Bill","ACCOUNT_TYPE_MYBILL":"One Bill","ACCOUNT_TYPENAME_ONEBILL":"One Bill","ACCOUNT_TYPENAME_MYBILL":"My bill","ACCOUNT_TYPENAME_MOBILITY":"Mobility","ACCOUNT_TYPENAME_TV":"TV","ACCOUNT_TYPENAME_INTERNET":"Internet","ACCOUNT_TYPENAME_HOMEPHONE":"Home Phone","ACCOUNT_TYPENAME_MOBILITY_ONEBILL":"Mobility and One Bill","ACCOUNT_TYPENAME_SINGLEBAN":"Single Ban","ACCOUNT_BALANCE":"Your current balance is","CHECKBOX_BALANCE":"${balance}","CHECKBOX_BALANCE_SR":"Your current balance is {balance} dollars","BACK_TO_MY_BELL":"Back to MyBell","CTA_NEXT":"Next","CTA_EDIT":"Edit","CTA_CLOSE":"Close dialog box","CTA_EXPAND_TERMS":"Expand terms of service","CTA_CONFIRM":"Confirm and submit","CTA_CANCEL":"Cancel","CTA_COLLAPSE_TERMS":"Collapse terms of service","CTA_INTERAC":"Sign in with Interac®","CTA_INTERAC_SR":"Sign in with Interac registered trademark verification service","CREDIT_CARD_LABEL":"Credit card","CREDIT_CARD_TYPE_LABEL":"Card type","CREDIT_CARD_NAME_LABEL":"Cardholder name","CREDIT_CARD_NAME_DESC_INPUT_LABEL":"As it appears on the card","CREDIT_CARD_NUMBER_LABEL":"Credit card number","CREDIT_CARD_NUMBER_DESC_INPUT_LABEL":"15 or 16 digits","CREDIT_CARD_EXPIRY_LABEL":"Expiration date","CREDIT_CARD_EXPIRY_SR_MONTH_LABEL":"Required, Expiration date month","CREDIT_CARD_EXPIRY_SR_YEAR_LABEL":"Required, Expiration date year","CREDIT_CARD_SECURITY_CODE_INPUT_LABEL":"Card security code","CREDIT_CARD_NAME_SR_LABEL":"Required, Cardholder name","CREDIT_CARD_NUMBER_SR_LABEL":"Required, Credit card number","CREDIT_CARD_EXPIRY_DATE_LABEL":"Required, Expiration date","CREDIT_CARD_EXPIRY_DATE_SR_LABEL":" {month} / {year}","CREDIT_CARD_MONTH_TEXT":"month","CREDIT_CARD_YEAR_TEXT":"year","CREDIT_CARD_SECURITY_CODE_INPUT_SR_LABEL":"Required, Card security code","ERROR_CREDIT_CARD_NAME_INPUT_LABEL":"Enter a valid cardholder name","ERROR_CREDIT_CARD_NUMBER_INPUT_LABEL":"Enter a valid credit card number","ERROR_CREDIT_CARD_EXPIRY_INPUT_LABEL":"Enter a valid expiration date","ERROR_CREDIT_CARD_SECURITY_CODE_INPUT_LABEL":"Enter a valid card security code","CREDIT_CARD_VALID":"Valid Until","CREDIT_CARD_MONTH_PLACEHOLDER":"MM","CREDIT_CARD_YEAR_PLACEHOLDER":"YY","REQUIRED_LABEL":"*Required information","MODAL_NO_NAME":"No name on card?","MODAL_SECURITY_CODE":"What is a card security code?","MODAL_NO_NAME_TITLE":"No name on the credit card?","MODAL_NO_NAME_DESC":"If you’re using a prepaid credit card that doesn’t have a cardholder name, enter the name of the Bell account holder instead.","MODAL_SECURITY_CODE_TITLE":"What is a card security code?","MODAL_SECURITY_CODE_DESC":"The card security code (CSC) is a fraud prevention feature used to verify that the credit card is in your possession. It’s sometimes called a card verification code (CVC) or card verification value (CVV). See where to find your security code below:","CARD_TYPE_VISA_MASTERCARD":"Visa and Mastercard","CARD_TYPE_VISA_MASTERCARD_DESC":"A 3-digit number on the back of your credit card.","CARD_TYPE_AMERICAN_EXP":"American Express","CARD_TYPE_AMERICAN_EXP_DESC":"A 4-digit number on the front of your credit card.","MODAL_TRANSIT_ACCOUNT_NUMBERS_TITLE":"Find your transit and account numbers","MODAL_BANK_STATEMENT_TITLE":"Look at a bank statement or cheque","MODAL_BANK_STATEMENT_DESC":"You can find your bank account details on a bank statement or cheque. You can also find your account information in your banking app or online account.","MODAL_TRANSIT_NUMBER_DESC":"The <strong>transit number</strong> is 5 digits.","MODAL_ACCOUNT_NUMBER_DESC":"Your <strong>account number</strong> is 7 to 12 digits.","ALERT_ERROR_HEADING":"An error occurred while processing your request.","ALERT_ERROR_HEADING_SR":"Warning, An error occured while processing your request.","ALERT_ERROR_INFO_REQUIRED":"- This information is required.","ALERT_ERROR_SELECT_BILL_INFO_REQUIRED":"Select a bill – This information is required.","ALERT_ERROR_SELECT_BILL_INFO":"Select a bill","ALERT_ERROR_SELECT_BILL_DESC":"This information is required.","ALERT_ERROR_ONE_SELECT_BILL":"Select at least one bill","ALERT_ERROR_HEADING_SOME_BALANCE":"<strong>Some of your current balances</strong> were not paid due to an error processing your request.","ALERT_ERROR_OTP_ALL_BALANCE":"<strong>Your current balance(s) were not paid</strong> due to an error processing your request.","ALERT_ERROR_OTP_BALANCE":"<strong>Your balance of ${balance} was not paid.</strong>","ALERT_ERROR_OTP_BALANCE_SR":"Your balance of {balance} dollars was not paid","ALERT_ERROR_OTP_BALANCE_DESC":"There was an error processing your one-time payment. Please pay your current account balance to avoid late payment charges.","ALERT_ERROR_OPTED_NOT_TO_PAY_SINGULAR":"Though you opted not to pay the following balance, they will also require a separate payment:","ALERT_ERROR_OPTED_NOT_TO_PAY_PLURAL":"Though you opted not to pay the following balance(s), they will also require a separate payment:","SELECT_PAYMENT_METHOD_HEADING":"Choose a payment method","PAY_CURRENT_BALANCE_HEADING":"Pay your current balance","PAY_CURRENT_BALANCE_DESC":"Pre-authorized payments will  <strong class=\'brui-text-black\'>begin on your next bill.</strong> You can pay your current account balance separately with a one-time payment to avoid late payment charges.","PAY_CURRENT_BALANCE_OPTED_IN":"Your one-time payment will be processed in <strong>3 to 5 business days</strong>. After that, you’ll have a balance of {balance} on your account.","PAY_CURRENT_BALANCE_OPTED_IN_PACC":"Your one-time payment will be processed <strong>as soon as you submit this transaction</strong>.","PAY_CURRENT_BALANCE_OPTED_OUT_SINGULAR":"<p>There’s an outstanding balance on your account.</p><p class=\'brui-mt-10\'>Pre-authorized payments will begin on your next bill. You can pay your current account balance separately with a one-time payment to avoid late payment charges.</p>","PAY_CURRENT_BALANCE_OPTED_OUT_PLURAL":"<p>There are outstanding balances on your accounts.</p><p class=\'brui-mt-10\'>Pre-authorized payments will begin on your next bill. You can pay your current account balances separately with a one-time payment to avoid late payment charges.</p>","PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_IN":"Your one-time payment will be processed in <strong>3 to 5 business days</strong>. After that, you’ll have a balance of {balance} on your account.","PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_OUT_SINGULAR":"You chose not to pay your current account balance.","PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_OUT_PLURAL":"You chose not to pay your current account balances.","PAY_CURRENT_BALANCE_SOME_BANS_OPTED_OUT_SINGULAR":"There’s an outstanding balance on your other account.","PAY_CURRENT_BALANCE_SOME_BANS_OPTED_OUT_PLURAL":"There are outstanding balances on your other accounts.","PAYMENT_AMOUNT":"Payment amount","UNPAID_BALANCE":"Unpaid balance","PAY_MY_BALANCE":"Pay my balance of","PAY_MY_BALANCE_SR":"Pay my balance of {balance} dollars","PAY_CURRENT_BALANCE_NOTE_1":"We’ll use the bank account information provided above to make the payment.","PAY_CURRENT_BALANCE_NOTE_2":"Note: If you made a payment in the last 3 to 5 business days, your balance owing may not be updated here yet.","TERMS_AND_CONDITION_HEADING":"Pre-authorized Payment Authorization","TERMS_AND_CONDITION_DISCLAIMER":"By clicking confirm and submit, I am confirming that I have read and agree to the Bell Terms of Service and the pricing details of my selected service(s).","TRANSACTION_SUBMITTED_HEADING":"Transaction submitted","CONFIRMATION_HEADING":"Confirmation","ALERT_CONFIRMATION_SUCCESS_HEADING":"Here’s what’s happening:","ALERT_CONFIRMATION_SUCCESS_MESSAGE":"Pre-authorized payments have been updated and will begin on your <strong>next bill.</strong>","ALERT_CONFIRMATION_SUCCESS_MESSAGE_V2":"Pre-authorized payments have been updated and will begin on your <strong>next billing cycle.</strong>","ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PACC":"Your one-time payment for <strong> {account} </strong> has been processed.","ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD":"Your one-time payment for <strong> {account} </strong> will be processed in <strong>3 to 5 business days.</strong>","ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS":"Your Autopay credit will start within 1 to 2 billing periods.","ALERT_CONFIRMATION_SUCCESS_NUMBER":"Confirmation number:","ALERT_CONFIRMATION_SUCCESS_DESC":"We’ve sent a confirmation to {email}. If this isn’t your correct email address, please","ALERT_CONFIRMATION_SUCCESS_DESC_LINK":"update your profile","ALERT_CONFIRMATION_SUCCESS_CONFIRM_NO":"Confirmation Number:","ALERT_CONFIRMATION_CURRENT_BALANCE":"The current balance for {account} has been paid.","PAYMENT_SUMMARY_TITLE":"Payment summary","PAYMENT_INFORMATION_TITLE":"Payment information","BILL_INFORMATION_TITLE":"Bill information","BILL_INFORMATION_ACCOUNT_NUMBER":"Account","PAYMENT_METHOD_TITLE":"Payment method","CURRENT_BALANCE_TITLE":"Current balance","SUMMARY_CURRENT_BAL_OTP_FAILURE":"Payment for your current balance was not processed. Please make a separate one-time payment.","TERMS_AND_CON_TITLE":"Pre-authorized Payment Authorization","TERMS_AND_CON_DESC_1":"You authorize us, Bell, to set up pre-authorized payment using the payment information you provided, as follows","TERMS_AND_CON_DESC_LIST_1":"For <strong>monthly payments:</strong>","TERMS_AND_CON_DESC_LIST_1_ITEM1":"We will debit your bank account or charge your credit card the total amount due, on the same date or close to that date each month (for example, the date may be different if it falls on a Sunday or a holiday);","TERMS_AND_CON_DESC_LIST_1_ITEM2":"You will be notified at least 10 days in advance on your bill of the amount due, which may vary due to charges you incurred;","TERMS_AND_CON_DESC_LIST_1_ITEM3":"For services for which a bill is not provided, you waive the requirement to be notified of the amount before the payment when the amount remains the same or is as previously agreed.","TERMS_AND_CON_DESC_LIST_2":"To <strong>add funds</strong> to your account for prepaid services:","TERMS_AND_CON_DESC_LIST_2_ITEM1":"We will debit your bank account or charge your credit card the amount set according to the criteria you selected;","TERMS_AND_CON_DESC_LIST_2_ITEM2":"<strong>You waive the requirement to be notified of the amount before the payment when the amount is the same as you selected or lower.</strong>","TERMS_AND_CON_DESC_2":"For <strong>other types of payments</strong>, we will obtain your authorization before the payment. In some circumstances, we may also use your pre-authorized payment method to make refunds.","TERMS_AND_CON_DESC_3":"If any payment is not compliant, you may have certain rights such as requesting its refund. For more information or to cancel this authorization, call us or go to MyBell (mybell.ca). When you cancel your authorization, you must notify us at least 30 days before the next pre-authorized payment date. For pre-authorized debits made with a bank account, to obtain a sample cancellation form, or for more information on your right to cancel this authorization, contact your financial institution or <a class=\\"focus-visible:payment-outline-blue focus-visible:payment-outline focus-visible:payment-outline-2 focus-visible:payment-outline-offset-3 focus-visible:payment-rounded-6 payment-underline-offset-2 payment-text-14 payment-text-blue payment-underline payment-leading-18 hover:payment-text-blue-1 hover:payment-no-underline\\" href=\\"#\\">visit payments.ca.</a>","TERMS_AND_CON_DESC_4":"Bell ","TERMS_AND_CON_ZIP_CODE":"P.O Box 9000","TERMS_AND_CON_REGION":"North York, Ontario M3C 2X7","TERMS_AND_CON_TEL":"**************","TERMS_AND_CONDITION_HEADING_QC":"Terms and conditions","TERMS_AND_CON_TITLE_QC":"Autorisation pour prélèvement automatique","TERMS_AND_CON_DESC_1_QC":"Vous nous autorisez, Bell, à mettre en place le paiement par prélèvement automatique en utilisant les renseignements de paiement que vous avez fournis, comme suit.","TERMS_AND_CON_DESC_LIST_1_QC":"Pour les paiements mensuels:","TERMS_AND_CON_DESC_LIST_1_ITEM1_QC":"Nous débiterons votre compte bancaire ou porterons à votre carte de crédit le montant total dû, à la même date ou à une date près de cette date chaque mois (par exemple, la date peut être différente si elle tombe un dimanche ou un jour férié);","TERMS_AND_CON_DESC_LIST_1_ITEM2_QC":"Vous serez avisé au moins 10 jours à l’avance sur votre facture du montant dû, qui peut varier en fonction des frais que vous avez engagés;","TERMS_AND_CON_DESC_LIST_1_ITEM3_QC":"Pour les services pour lesquels aucune facture n’est fournie, vous renoncez à l’exigence d’être avisé du montant avant le prélèvement lorsque le montant demeure le même ou correspond à celui convenu précédemment.","TERMS_AND_CON_DESC_LIST_2_QC":"Pour <strong>ajouter des fonds</strong> à votre compte pour les services prépayés :","TERMS_AND_CON_DESC_LIST_2_ITEM1_QC":"Nous débiterons votre compte bancaire ou porterons à votre carte de crédit, le montant établi selon les critères que vous avez sélectionnés;","TERMS_AND_CON_DESC_LIST_2_ITEM2_QC":"<strong>Vous renoncez à l’exigence d’être avisé du montant avant le prélèvement lorsque le montant est le même que celui que vous avez sélectionné ou inférieur.</strong>","TERMS_AND_CON_DESC_2_QC":"Pour les <strong>autres types de paiements</strong>,  nous obtiendrons votre autorisation avant le prélèvement. Dans certaines circonstances, nous pouvons également utiliser votre mode de paiement par prélèvement automatique pour effectuer des remboursements.","TERMS_AND_CON_DESC_3_QC":"Si un prélèvement n’est pas conforme à l’autorisation, vous pouvez avoir certains droits, comme demander son remboursement. Pour en savoir plus ou pour annuler cette autorisation, appelez-nous ou rendez-vous sur MonBell (monbell.ca). Lorsque vous annulez votre autorisation, vous devez nous en aviser au moins 30 jours avant la date du prochain prélèvement. Pour les prélèvements automatiques effectués avec un compte bancaire, pour obtenir un exemple de formulaire d’annulation ou pour en savoir plus sur votre droit d’annuler cette autorisation, communiquez avec votre institution financière ou visitez paiements.ca.","TERMS_AND_CON_DESC_4_QC":"Bell Canada ","TERMS_AND_CON_ZIP_CODE_QC":"C.P. 8712, succ. Centre-Ville","TERMS_AND_CON_REGION_QC":"Montréal (Québec) H3C 3P6","TERMS_AND_CON_TEL_QC":"Tél.: **************","BANK_ACCOUNT_LABEL":"Bank Account","TRANSIT_ACC_NO_PNG":"/Styles/BRF3/content/img/transit-account-number.png","INTERACT_VERIFICATION_V2_PNG":"/Styles/BRF3/content/img/interac-logos/interac-verification-v2-en.png","MASTER_CC_PNG":"/Styles/BRF3/content/img/creditCardIcons/mastercard.png","VISA_CC_PNG":"/Styles/BRF3/content/img/creditCardIcons/visa.png","AMEX_CC_PNG":"/Styles/BRF3/content/img/creditCardIcons/amex.png","FRONT_CC_PNG":"/Styles/BRF3/content/img/Front.png","BACK_CC_PNG":"/Styles/BRF3/content/img/Back.png","SELECT_BILLS_CC_DESC":"{CreditCardType} ending in {CCFourDigits}, expiring {ExpiryDate}","NEW_CREDIT_ACCOUNT_LABEL":"New credit account","SELECT_BILLS_BANK_DESC":"{BankName} account {BankMaskedDigits}","ALERT_GREAT_NEWS":"Great news","ALERT_GREAT_NEWS_DESC":" - by setting up pre-authorized debit payments, the following services will be eligible for an Autopay credit:","ALERT_GREAT_NEWS_DESC_1":" - you’ll receive an Autopay credit.","ALERT_GREAT_NEWS_NOTE":"Note: ","ALERT_GREAT_NEWS_NOTE_DESC":"If you have any upcoming changes to your account, they may impact the Autopay credit above. Any changes to the credit will take effect within 1 to 2 billing periods.","BANK_ACCOUNT_AUTOMATIC_LABEL":"Automatically retrieve account details with Interac® verification service","BANK_ACCOUNT_AUTOMATIC_DESCRIPTION":"Simply use the Interac® verification service to securely sign in to your bank’s website. Available for the following banks:","BANK_ACCOUNT_MANUAL_DETAILS_LABEL":"Enter your bank account details manually","BANK_ACCOUNT_MANUAL_DETAILS_DESCRIPTION":"Bell accepts payments from most banks and credit unions in Canada.","BANK_NAME_LABEL":"Bank","BANK_HOLDER_NAME_LABEL":"Account holder name","BANK_TRANSIT_NUMBER_LABEL":"Transit number","BANK_TRANSIT_NUMBER_DESCRIPTION":"5 digits","BANK_ACCOUNT_NUMBER_LABEL":"Account number","BANK_ACCOUNT_NUMBER_DESCRIPTION":"7 to 12 digits","BANK_ACCOUNT_FETCHED_LABEL":"Account number filled automatically Please confirm this is the correct account.","BANK_NEED_HELP":"Need help?","BANK_NEW_BANK_ACCOUNT_LABEL":"New bank account","SELECT_REQUIRED_LEGEND":"Required","CC_IMAGE_SR_LABEL":"Accepted credit cards: MasterCard Visa AMEX","LOAD_MORE":"Load More","PAYMENT_METHOD":"Payment method","ACCOUNT_HOLDER":"Account holder name ","BANK_NAME":"Bank ","TRANSIT_NUMER":"Transit number ","ACCOUNT_NUMBER":"Account number ","BANK_HOLDER_NAME_ERROR_LABEL":"Enter a valid name","BANK_NAME_ERROR_LABEL":"Select a bank","BANK_TRANSIT_ERROR_LABEL":"Enter a valid, 5-digit transit number","BANK_ACCOUNT_HOLDER_NUMBER_ERROR_LABEL":"Enter a valid bank account number of 7 to 12 digits","ALERT_ERROR_GENERAL_DESC":"This information is required.","PAGE_TITLE_CONFIRMATON":"Confirmation: Set up pre-authorized payments document","INTERAC_BOX_LOGO":"/Styles/BRF3/content/img/interac-logos/Interac_box_logo.png","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_SINGLE":"Avoid late payment charges by paying your current account balance.","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_SINGLE_1":"Pre-authorized payments will begin on your next bill. You can pay your current account balance of ","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_SINGLE_2":" separately with a one-time payment to avoid late payment charges.","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_MULTI":"Avoid late payment charges by paying your current account balance.","ALERT_CONFIRMATION_OPTED_SOME_BALANCES_MULTI":"Avoid late payment charges by paying your current account balances.","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_MULTI":"Pre-authorized payments will begin on your next bill. You can pay your current account balance separately with a one-time payment to avoid late payment charges.","ALERT_CONFIRMATION_OPTED_SOME_BALANCES_DESC_MULTI":"Pre-authorized payments will begin on your next bill. You can pay your current account balances separately with a one-time payment to avoid late payment charges.","CTA_MAKE_PAYMENT_LINK":"/Payment/MakePayment","LOADER":"Loading data. Please wait…","INTERAC_FETCHED_LABEL":"Account number filled automatically","INTERAC_FETCHED_SUBTITLE":"Please confirm this is the correct account.","ALERT_ERROR_HEADING_INTERAC":"We experienced a technical issue","ALERT_ERROR_HEADING_INTERAC_SR":"Error: We experienced a technical issue","ALERT_ERROR_HEADING_INTERAC_DESC":"Sorry, we were unable to process your request due to a technical issue. You can enter your bank account details manually instead.","FAILURE_API_BAN_HEADING":"Something went wrong","FAILURE_API_BAN_HEADING_SR":"Error, Something went wrong","FAILURE_API_BAN_MAIN_DESC":"Sorry, we were unable to process your request.","FAILURE_API_BAN_MAIN_DESC_2":"Please try again.","FAILURE_API_BAN_SUB_DESC":"If the issue persists:","FAILURE_API_BAN_SUB_DESC_LISTITEM_1":"Use a different credit card or set up pre-authorized debit payments instead.","FAILURE_API_BAN_SUB_DESC_LISTITEM_2":"Wait a little while and try again later.","FAILURE_API_BAN_BUTTON":"Try again","LOADER_SUBMIT":"Submitting payment information","LOADER_SUBMIT_DESC":"Please wait","LABEL_LOADED_OFFERS_DEBIT_TITLE":"-by setting up pre-authorized debit payments, the following services will be eligible for an Autopay credit:","LABEL_LOADED_OFFER_DEBIT_TITLE":"-by setting up pre-authorized debit payments, the following service will be eligible for an Autopay credit:","LABEL_LOADED_OFFERS_TITLE_TRUEAUTOPAY":"-by setting up pre-authorized payments, the following services will be eligible for an Autopay credit","LABEL_LOADED_OFFER_TITLE_TRUEAUTOPAY":"-by setting up pre-authorized payments, the following service will be eligible for an Autopay credit","GREAT_NEWS":"Great news","MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE_HEADING":"Note:","MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE":"If you have any upcoming changes to your account, they may impact the Autopay credits above. Any changes to the credits will take effect within 1 to 2 billing periods.","MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE":"If you have any upcoming changes to your account, they may impact the Autopay credit above. Any changes to the credit will take effect within 1 to 2 billing periods.","SORRY_MESSAGE":"Sorry, the Autopay credit isn’t available for this payment method.","SORRY_MESSAGE_CREDIT":"Sorry, the Autopay credit isn’t available for this payment method. To receive the credit, set up pre-authorized debit payments instead.","LABEL_LOADED_OFFERS_CREDIT_TITLE":"– by setting up pre-authorized credit card payments, the following services will be eligible for an Autopay credit:","LABEL_LOADED_OFFER_CREDIT_TITLE":"– by setting up pre-authorized credit card payments, the following service will be eligible for an Autopay credit:","REVIEW_PAGE_AUTOPAY_CREDIT":"Great news – you’ll receive an Autopay credit.","AUTOPAY_ALERT":"Your Autopay credit will start within 1 to 2 billing periods.","AUTOPAY_CREDITS_TITLE":"Autopay credits","InteracSupportedFinancialInstitutions":"BMO,CIBC,Desjardins,RBC,Scotiabank,TD","BANK_ACCOUNT_SR_TEXT":"Account number ending in {Account}","CREDIT_CARD_SR_TEXT":"Card number ending in {Account}","PAYMENT_METHOD_DEBIT":"Debit","SELECT_BANK_PLACEHOLDER":"Select an option","CREDIT_CARD_NAME_LABEL_V2":"Cardholder","SELECT_ALL_BAN":"Select all bills","NOT_ON_PREAUTH":"Not on pre-authorized payments","CREDIT_CARD_NUMBER_LABEL_V2":"Card number"},"fr":{"CREDIT_CARD_NUMBER_LABEL_V2":"Numéro de carte","NOT_ON_PREAUTH":"Prélèvements automatique non configurés","SELECT_ALL_BAN":"Sélectionnez toutes les factures","ALERT_CONFIRMATION_INFO_HEADING":"Évitez des frais de retard en payant le solde actuel de votre compte.","ALERT_CONFIRMATION_INFO_DESC":"Les paiements par prélèvement automatique débuteront à partir de votre prochaine facture. Vous pouvez payer séparément votre solde actuel au moyen d’un paiement unique, afin d’éviter des frais de retard.","ALERT_CONFIRMATION_INFO_BUTTON_DESC":"Effectuer un paiement","ALERT_CONFIRMATION_INFO_DESC_1":"Pre-authorized payments will only begin on your next billing period. You must pay your ","ALERT_CONFIRMATION_INFO_DESC_2":"balance in a separate and final one-time payment, or risk late fees. ","EXISTING_CC_TITLE":"Sélectionnez une carte de crédit","EXISTING_BANK_TITLE":"Sélectionner un compte bancaire","EXISTING_BANK_TITLE_SR":"Requis, Sélectionner un compte bancaire","EXISTING_BANK_ACCOUNT_MANUAL_DETAILS_LABEL":"Choisir un compte bancaire déjà associé à votre compte Bell, ou en ajouter un autre manuellement","SELECT_BILLS_HEADING":"Sélectionner les factures","SELECT_BILLS_HEADING_SINGULAR":"Sélectionner les factures","SELECT_BILLS_ACCOUNT_TITLE":"Numéro de compte {accounttype}","SELECT_BILLS_HEADING_DESC":"Pour quelle(s) facture(s) souhaitez-vous mettre à jour les renseignements de paiement?","SELECT_BANK_TITLE":"Requis, Sélectionner un compte bancaire","ACCOUNT_TYPE_ONE_BILL":"Facture unique","ACCOUNT_TYPE_MYBILL":"One Bill","ACCOUNT_TYPENAME_ONEBILL":"Facture unique","ACCOUNT_TYPENAME_MYBILL":"Ma facture","ACCOUNT_TYPENAME_MOBILITY":"Mobilité","ACCOUNT_TYPENAME_TV":"TV","ACCOUNT_TYPENAME_INTERNET":"Internet","ACCOUNT_TYPENAME_HOMEPHONE":"Home Phone","ACCOUNT_TYPENAME_MOBILITY_ONEBILL":"Mobility and One Bill","ACCOUNT_TYPENAME_SINGLEBAN":"Single Ban","ACCOUNT_BALANCE":"Votre solde actuel est de","CHECKBOX_BALANCE":"{balance}$","CHECKBOX_BALANCE_SR":"Votre solde actuel est de {balance} dollars","BACK_TO_MY_BELL":"Retour à MonBell","CTA_NEXT":"Suivant","CTA_EDIT":"Modifier","CTA_CLOSE":"Fermer la boîte de dialogue","CTA_EXPAND_TERMS":"Afficher les modalités de service","CTA_COLLAPSE_TERMS":"Masquer les modalités de service","CTA_CONFIRM":"Confirmer et envoyer","CTA_CANCEL":"Annuler","CTA_INTERAC":"Se connecter avec Interac®","CTA_INTERAC_SR":"Se connecter avec Interac marque déposée","CREDIT_CARD_LABEL":"Carte de crédit","CREDIT_CARD_TYPE_LABEL":"Type de carte","CREDIT_CARD_NAME_LABEL":"Nom du titulaire de la carte","CREDIT_CARD_NAME_DESC_INPUT_LABEL":"Tel qu’il apparaît sur la carte","CREDIT_CARD_NUMBER_LABEL":"Numéro de carte de crédit","CREDIT_CARD_NUMBER_DESC_INPUT_LABEL":"15 ou 16 chiffres","CREDIT_CARD_EXPIRY_LABEL":"Date d’expiration","CREDIT_CARD_EXPIRY_SR_MONTH_LABEL":"Requis, Date d’expiration mois","CREDIT_CARD_EXPIRY_SR_YEAR_LABEL":"Requis, Date d’expiration année","CREDIT_CARD_SECURITY_CODE_INPUT_LABEL":"Code de sécurité","CREDIT_CARD_NAME_SR_LABEL":"Requis, Nom du titulaire de la carte ","CREDIT_CARD_NUMBER_SR_LABEL":"Requis, Numéro de carte de crédit","CREDIT_CARD_EXPIRY_DATE_LABEL":"Requis, Date d’expiration","CREDIT_CARD_EXPIRY_DATE_SR_LABEL":" {{month} / {year}}","CREDIT_CARD_MONTH_TEXT":"mois","CREDIT_CARD_YEAR_TEXT":"année","CREDIT_CARD_SECURITY_CODE_INPUT_SR_LABEL":"Requis, Code de sécurité","ERROR_CREDIT_CARD_NAME_INPUT_LABEL":"Entrer un nom de titulaire de carte valide ","ERROR_CREDIT_CARD_NUMBER_INPUT_LABEL":"Entrer un numéro de carte de crédit valide ","ERROR_CREDIT_CARD_EXPIRY_INPUT_LABEL":"Entrer une date d’expiration valide","ERROR_CREDIT_CARD_SECURITY_CODE_INPUT_LABEL":"Entrer un code de sécurité valide ","CREDIT_CARD_VALID":"Valable jusqu\'au","CREDIT_CARD_MONTH_PLACEHOLDER":"MM","CREDIT_CARD_YEAR_PLACEHOLDER":"AA","REQUIRED_LABEL":"*Renseignement requis","MODAL_NO_NAME":"Il n’y a pas de nom sur la carte?","MODAL_NO_NAME_TITLE":"Pas de nom sur la carte de crédit?","MODAL_NO_NAME_DESC":"Si vous utilisez une carte de crédit prépayée sur laquelle ne figure aucun nom, entrez le nom du titulaire de compte Bell. ","MODAL_SECURITY_CODE":"Qu’est-ce que le code de sécurité de la carte?","MODAL_SECURITY_CODE_TITLE":"Qu’est-ce que le code de sécurité?","MODAL_SECURITY_CODE_DESC":"Le code de sécurité de la carte de crédit (CSC) est une mesure de prévention contre la fraude. Il permet de vérifier que la carte de crédit est en votre possession. Ce code est parfois appelé code de vérification de carte (CVC) ou valeur de vérification de carte (CVV). Voici où trouver votre code de sécurité :","CARD_TYPE_VISA_MASTERCARD":"Visa et Mastercard","CARD_TYPE_VISA_MASTERCARD_DESC":"Un numéro de 3 chiffres au verso de la carte de crédit.","CARD_TYPE_AMERICAN_EXP":"American Express","CARD_TYPE_AMERICAN_EXP_DESC":"Un numéro de 4 chiffres au recto de la carte de crédit.","MODAL_TRANSIT_ACCOUNT_NUMBERS_TITLE":"Trouver votre numéro de succursale et votre numéro de compte","MODAL_BANK_STATEMENT_TITLE":"Sur un relevé bancaire ou un chèque","MODAL_BANK_STATEMENT_DESC":"Vous trouverez vos renseignements bancaires sur un relevé bancaire ou un chèque. Vous pouvez aussi trouver ces renseignements dans votre application bancaire ou en accédant à votre compte bancaire en ligne.","MODAL_TRANSIT_NUMBER_DESC":"Le numéro de succursale comporte 5 chiffres.","MODAL_ACCOUNT_NUMBER_DESC":"Votre numéro de compte comporte de 7 à 12 chiffres.","ALERT_ERROR_HEADING":"Une erreur est survenue pendant le traitement de votre demande.","ALERT_ERROR_HEADING_SR":"Une erreur est survenue pendant le traitement de votre demande.","ALERT_ERROR_INFO_REQUIRED":"– Ce renseignement est requis.","ALERT_ERROR_SELECT_BILL_INFO":"Sélectionner une facture","ALERT_ERROR_SELECT_BILL_DESC":"Ce renseignement est requis.","ALERT_ERROR_SELECT_BILL_INFO_REQUIRED":"Select a bill – This information is required.","ALERT_ERROR_ONE_SELECT_BILL":"Sélectionnez au moins une facture","ALERT_ERROR_HEADING_SOME_BALANCE":"<strong>Certains de vos soldes actuels</strong> n’ont pas été payés en raison d’une erreur lors du traitement de votre demande.","ALERT_ERROR_OTP_ALL_BALANCE":"<strong>Votre ou vos soldes actuels</strong> n’ont pas été payés en raison d’une erreur lors du traitement de votre demande.","ALERT_ERROR_OTP_BALANCE":"<strong>Votre solde de {balance} $ n\'a pas été payé.</strong>","ALERT_ERROR_OTP_BALANCE_SR":"Votre solde de {balance} dollars n\'a pas été payé.","ALERT_ERROR_OTP_BALANCE_DESC":"Une erreur s’est produite lors du traitement de votre paiement unique. Veuillez payer le solde actuel de votre compte afin d’éviter des frais de retard.","ALERT_ERROR_OPTED_NOT_TO_PAY_SINGULAR":"Même si vous avez choisi de ne pas payer le solde suivant, un paiement séparé sera également requis:","ALERT_ERROR_OPTED_NOT_TO_PAY_PLURAL":"Même si vous avez choisi de ne pas payer le(s) solde(s) suivant(s), ils nécessiteront également un paiement séparé:","SELECT_PAYMENT_METHOD_HEADING":"Choisir le mode de paiement","PAY_CURRENT_BALANCE_HEADING":"Payer votre solde actuel","PAY_CURRENT_BALANCE_DESC":"Les paiements par prélèvement automatique débuteront <strong class=\'brui-text-black\'>à partir de votre prochaine facture.</strong> Vous pouvez payer séparément votre solde actuel au moyen d’un paiement unique, afin d’éviter des frais de retard.","PAY_CURRENT_BALANCE_OPTED_IN":"Votre paiement unique sera traité dans un délai de <strong>trois à cinq jours ouvrables</strong>. Par la suite, il restera un solde de {balance} dans votre compte.","PAY_CURRENT_BALANCE_OPTED_IN_PACC":"Votre paiement unique sera traité <strong>dès que vous enverrez cette transaction</strong>.","PAY_CURRENT_BALANCE_OPTED_OUT_SINGULAR":"<p>Votre compte comporte un solde à payer.</p><p class=\'brui-mt-10\'>Les paiements par prélèvement automatique débuteront à partir de votre prochaine facture. Vous pouvez payer séparément votre solde actuel au moyen d’un paiement unique, afin d’éviter des frais de retard.</p>","PAY_CURRENT_BALANCE_OPTED_OUT_PLURAL":"<p>Vos comptes comportent des soldes à payer.</p><p class=\'brui-mt-10\'>Les paiements par prélèvement automatique débuteront à partir de votre prochaine facture. Vous pouvez payer séparément vos soldes actuels au moyen d’un paiement unique, afin d’éviter des frais de retard.</p>","PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_IN":"Votre paiement unique sera traité dans un délai de <strong>trois à cinq jours ouvrables</strong>. Par la suite, il restera un solde de {balance} dans votre compte.","PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_OUT_SINGULAR":"Vous avez choisi de ne pas payer le solde actuel de votre compte.","PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_OUT_PLURAL":"Vous avez choisi de ne pas payer le solde actuel de votre compte.","PAY_CURRENT_BALANCE_SOME_BANS_OPTED_OUT_SINGULAR":"Votre autre compte comporte un solde à payer.","PAY_CURRENT_BALANCE_SOME_BANS_OPTED_OUT_PLURAL":"Vos autres comptes comportent des soldes à payer.","PAYMENT_AMOUNT":"Montant du paiement","UNPAID_BALANCE":"Solde impayé","PAY_MY_BALANCE":"Payer mon solde de","PAY_MY_BALANCE_SR":"Payer mon solde de {balance} dollars","PAY_CURRENT_BALANCE_NOTE_1":"Nous utiliserons les renseignements relatifs au compte bancaire ci-dessus pour le paiement.","PAY_CURRENT_BALANCE_NOTE_2":"Remarque : Si vous avez effectué un paiement au cours des trois à cinq derniers jours ouvrables, il se peut que votre solde ne soit pas encore mis à jour.","TERMS_AND_CONDITION_HEADING":"Modalités et conditions","TERMS_AND_CONDITION_DISCLAIMER":"En cliquant sur Confirmer et envoyer, je confirme avoir lu et accepté les modalités de service Bell et les tarifs du ou des services sélectionnés","TRANSACTION_SUBMITTED_HEADING":"Transaction envoyée","CONFIRMATION_HEADING":"Confirmation","ALERT_CONFIRMATION_SUCCESS_HEADING":"Voici ce qui se passe :","ALERT_CONFIRMATION_SUCCESS_MESSAGE":"Les paiements par prélèvement automatique ont été mis à jour et commenceront sur votre <strong>prochaine facture.</strong>","ALERT_CONFIRMATION_SUCCESS_MESSAGE_V2":"Pre-authorized payments have been set up and will begin on your <strong>next billing cycle.</strong>","ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PACC":"Votre paiement unique pour le compte <strong> {account} </strong> a bien été traité.","ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD":"Votre paiement unique porté au compte <strong> {account} </strong> sera traité dans un délai de <strong>trois à cinq jours ouvrables.</strong>","ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD_FR":"Votre paiement unique porté au compte no <strong> {account} </strong> sera traité dans un délai de <strong>trois à cinq jours ouvrables.</strong>","ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS":"Votre crédit pour autopaiement débutera d’ici une à deux périodes de facturation.","ALERT_CONFIRMATION_SUCCESS_NUMBER":"Numéro de confirmation :","ALERT_CONFIRMATION_SUCCESS_DESC":"Nous avons envoyé un courriel de confirmation à l’adresse {email}. S’il ne s’agit pas de la bonne adresse de courriel, veuillez","ALERT_CONFIRMATION_SUCCESS_DESC_LINK":"mettre votre profil à jour","ALERT_CONFIRMATION_SUCCESS_CONFIRM_NO":"Confirmation Number:","ALERT_CONFIRMATION_CURRENT_BALANCE":"The current balance for {account} has been paid.","PAYMENT_SUMMARY_TITLE":"Sommaire des prélèvements","PAYMENT_INFORMATION_TITLE":"Renseignements de paiement","BILL_INFORMATION_TITLE":"Renseignements sur la facture","BILL_INFORMATION_ACCOUNT_NUMBER":"Compte","PAYMENT_METHOD_TITLE":"Mode de paiement","CURRENT_BALANCE_TITLE":"Solde actuel","SUMMARY_CURRENT_BAL_OTP_FAILURE":"Le paiement de votre solde actuel n’a pas été traité. Veuillez effectuer un paiement unique distinct.","TERMS_AND_CON_TITLE":"Autorisation pour prélèvement automatique","TERMS_AND_CON_DESC_1":"Vous nous autorisez, Bell, à mettre en place le paiement par prélèvement automatique en utilisant les renseignements de paiement que vous avez fournis, comme suit.","TERMS_AND_CON_DESC_LIST_1":"Pour les paiements mensuels:","TERMS_AND_CON_DESC_LIST_1_ITEM1":"Nous débiterons votre compte bancaire ou porterons à votre carte de crédit le montant total dû, à la même date ou à une date près de cette date chaque mois (par exemple, la date peut être différente si elle tombe un dimanche ou un jour férié);","TERMS_AND_CON_DESC_LIST_1_ITEM2":"Vous serez avisé au moins 10 jours à l’avance sur votre facture du montant dû, qui peut varier en fonction des frais que vous avez engagés;","TERMS_AND_CON_DESC_LIST_1_ITEM3":"Pour les services pour lesquels aucune facture n’est fournie, vous renoncez à l’exigence d’être avisé du montant avant le prélèvement lorsque le montant demeure le même ou correspond à celui convenu précédemment.","TERMS_AND_CON_DESC_LIST_2":"Pour <strong>ajouter des fonds</strong> à votre compte pour les services prépayés :","TERMS_AND_CON_DESC_LIST_2_ITEM1":"Nous débiterons votre compte bancaire ou porterons à votre carte de crédit, le montant établi selon les critères que vous avez sélectionnés;","TERMS_AND_CON_DESC_LIST_2_ITEM2":"<strong>Vous renoncez à l’exigence d’être avisé du montant avant le prélèvement lorsque le montant est le même que celui que vous avez sélectionné ou inférieur.</strong>","TERMS_AND_CON_DESC_2":"Pour les <strong>autres types de paiements</strong>,  nous obtiendrons votre autorisation avant le prélèvement. Dans certaines circonstances, nous pouvons également utiliser votre mode de paiement par prélèvement automatique pour effectuer des remboursements.","TERMS_AND_CON_DESC_3":"Si un prélèvement n’est pas conforme à l’autorisation, vous pouvez avoir certains droits, comme demander son remboursement. Pour en savoir plus ou pour annuler cette autorisation, appelez-nous ou rendez-vous sur MonBell (monbell.ca). Lorsque vous annulez votre autorisation, vous devez nous en aviser au moins 30 jours avant la date du prochain prélèvement. Pour les prélèvements automatiques effectués avec un compte bancaire, pour obtenir un exemple de formulaire d’annulation ou pour en savoir plus sur votre droit d’annuler cette autorisation, communiquez avec votre institution financière ou visitez paiements.ca.","TERMS_AND_CON_DESC_4":"Bell Canada ","TERMS_AND_CON_ZIP_CODE":"C.P. 8712, succ. Centre-Ville","TERMS_AND_CON_REGION":"Montréal (Québec) H3C 3P6","TERMS_AND_CON_TEL":"Tél.: **************","TERMS_AND_CONDITION_HEADING_QC":"Modalités et conditions","TERMS_AND_CON_TITLE_QC":"Autorisation pour prélèvement automatique","TERMS_AND_CON_DESC_1_QC":"Vous nous autorisez, Bell, à mettre en place le paiement par prélèvement automatique en utilisant les renseignements de paiement que vous avez fournis, comme suit.","TERMS_AND_CON_DESC_LIST_1_QC":"Pour les paiements mensuels:","TERMS_AND_CON_DESC_LIST_1_ITEM1_QC":"Nous débiterons votre compte bancaire ou porterons à votre carte de crédit le montant total dû, à la même date ou à une date près de cette date chaque mois (par exemple, la date peut être différente si elle tombe un dimanche ou un jour férié);","TERMS_AND_CON_DESC_LIST_1_ITEM2_QC":"Vous serez avisé au moins 10 jours à l’avance sur votre facture du montant dû, qui peut varier en fonction des frais que vous avez engagés;","TERMS_AND_CON_DESC_LIST_1_ITEM3_QC":"Pour les services pour lesquels aucune facture n’est fournie, vous renoncez à l’exigence d’être avisé du montant avant le prélèvement lorsque le montant demeure le même ou correspond à celui convenu précédemment.","TERMS_AND_CON_DESC_LIST_2_QC":"Pour <strong>ajouter des fonds</strong> à votre compte pour les services prépayés :","TERMS_AND_CON_DESC_LIST_2_ITEM1_QC":"Nous débiterons votre compte bancaire ou porterons à votre carte de crédit, le montant établi selon les critères que vous avez sélectionnés;","TERMS_AND_CON_DESC_LIST_2_ITEM2_QC":"<strong>Vous renoncez à l’exigence d’être avisé du montant avant le prélèvement lorsque le montant est le même que celui que vous avez sélectionné ou inférieur.</strong>","TERMS_AND_CON_DESC_2_QC":"Pour les <strong>autres types de paiements</strong>,  nous obtiendrons votre autorisation avant le prélèvement. Dans certaines circonstances, nous pouvons également utiliser votre mode de paiement par prélèvement automatique pour effectuer des remboursements.","TERMS_AND_CON_DESC_3_QC":"Si un prélèvement n’est pas conforme à l’autorisation, vous pouvez avoir certains droits, comme demander son remboursement. Pour en savoir plus ou pour annuler cette autorisation, appelez-nous ou rendez-vous sur MonBell (monbell.ca). Lorsque vous annulez votre autorisation, vous devez nous en aviser au moins 30 jours avant la date du prochain prélèvement. Pour les prélèvements automatiques effectués avec un compte bancaire, pour obtenir un exemple de formulaire d’annulation ou pour en savoir plus sur votre droit d’annuler cette autorisation, communiquez avec votre institution financière ou visitez paiements.ca.","TERMS_AND_CON_DESC_4_QC":"Bell Canada ","TERMS_AND_CON_ZIP_CODE_QC":"C.P. 8712, succ. Centre-Ville","TERMS_AND_CON_REGION_QC":"Montréal (Québec) H3C 3P6","TERMS_AND_CON_TEL_QC":"Tél.: **************","BANK_ACCOUNT_LABEL":"Du compte bancaire","TRANSIT_ACC_NO_PNG":"/Styles/BRF3/content/img/transit-account-number.png","INTERACT_VERIFICATION_V2_PNG":"/Styles/BRF3/content/img/interac-logos/interac-verification-v2-en.png","MASTER_CC_PNG":"/Styles/BRF3/content/img/creditCardIcons/mastercard.png","VISA_CC_PNG":"/Styles/BRF3/content/img/creditCardIcons/visa.png","AMEX_CC_PNG":"/Styles/BRF3/content/img/creditCardIcons/amex.png","FRONT_CC_PNG":"/Styles/BRF3/content/img/Front.png","BACK_CC_PNG":"/Styles/BRF3/content/img/Back.png","INTERAC_BOX_LOGO":"/Styles/BRF3/content/img/interac-logos/Interac_box_logo.png","SELECT_BILLS_CC_DESC":"{CreditCardType} se terminant par {CCFourDigits}, expirant le {ExpiryDate}","NEW_CREDIT_ACCOUNT_LABEL":"Nouvelle carte de crédit","SELECT_BILLS_BANK_DESC":"{BankName} account {BankMaskedDigits}","ALERT_GREAT_NEWS":"Bonne nouvelle","ALERT_GREAT_NEWS_DESC":" : avec des prélèvements automatiques par débit, les services suivants donneront droit à un crédit pour autopaiement :","ALERT_GREAT_NEWS_DESC_1":" : vous aurez droit à un crédit pour autopaiement.","ALERT_GREAT_NEWS_NOTE":"Remarque : ","ALERT_GREAT_NEWS_NOTE_DESC":"S’il y a des changements à venir à votre compte, ils pourraient avoir une incidence sur le crédit pour autopaiement ci-dessus. Tout changement apporté au crédit entrera en vigueur d’ici une à deux périodes de facturation.","BANK_ACCOUNT_AUTOMATIC_LABEL":"Entrer automatiquement les renseignements relatifs à votre compte","BANK_ACCOUNT_AUTOMATIC_DESCRIPTION":"Utilisez le service de vérification InteracMD pour vous connecter en toute sécurité au site Web de votre institution financière. Offert pour les institutions suivantes :","BANK_ACCOUNT_MANUAL_DETAILS_LABEL":"Entrer manuellement les renseignements relatifs à votre compte","BANK_ACCOUNT_MANUAL_DETAILS_DESCRIPTION":"Bell accepte les paiements de la plupart des banques et coopératives de crédit au Canada.","BANK_NAME_LABEL":"Institution financière","BANK_HOLDER_NAME_LABEL":"Nom du titulaire du compte","BANK_TRANSIT_NUMBER_LABEL":"Numéro de succursale","BANK_TRANSIT_NUMBER_DESCRIPTION":"5 chiffres","BANK_ACCOUNT_NUMBER_LABEL":"Numéro de compte","BANK_ACCOUNT_NUMBER_DESCRIPTION":"7 à 12 chiffres","BANK_ACCOUNT_FETCHED_LABEL":"Numéro de compte entré automatiquement Veuillez confirmer qu’il s’agit du bon compte.","BANK_NEED_HELP":"Besoin d’aide?","BANK_NEW_BANK_ACCOUNT_LABEL":"Nouvelle carte bancaire","SELECT_REQUIRED_LEGEND":"Requis","CC_IMAGE_SR_LABEL":"Cartes de crédit acceptées: Visa MasterCard American Express","LOAD_MORE":"Charger Plus","PAYMENT_METHOD":"Mode de paiement","ACCOUNT_HOLDER":"Nom du titulaire du compte ","BANK_NAME":"Institution financière  ","TRANSIT_NUMER":"Numéro de succursale ","ACCOUNT_NUMBER":"Numéro de compte ","BANK_HOLDER_NAME_ERROR_LABEL":"Entrer un nom valide","BANK_NAME_ERROR_LABEL":"Sélectionner votre institution financière","BANK_TRANSIT_ERROR_LABEL":"Entrer un numéro de succursale de 5 chiffres valide","BANK_ACCOUNT_HOLDER_NUMBER_ERROR_LABEL":"Entrer un numéro de compte bancaire de 7 à 12 chiffres valide","ALERT_ERROR_GENERAL_DESC":"Ce renseignement est requis.","PAGE_TITLE_CONFIRMATON":"Confirmation: Établir un prélèvement automatique document","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_SINGLE":"Évitez des frais de retard en payant le solde actuel de votre compte.","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_SINGLE_1":"Les paiements par prélèvement automatique débuteront à partir de votre prochaine facture. Vous pouvez payer séparément votre solde actuel de ","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_SINGLE_2":" $ au moyen d’un paiement unique, afin d’éviter des frais de retard.","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_MULTI":"Évitez des frais de retard en payant les solde actuel de vos comptes.","ALERT_CONFIRMATION_OPTED_SOME_BALANCES_MULTI":"Évitez des frais de retard en payant les solde actuel de vos comptes.","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_MULTI":"Les paiements par prélèvement automatique débuteront à partir de votre prochaine facture. Vous pouvez payer séparément vos solde actuel au moyen d’un paiement unique, afin d’éviter des frais de retard.","ALERT_CONFIRMATION_OPTED_SOME_BALANCES_DESC_MULTI":"Les paiements par prélèvement automatique débuteront à partir de votre prochaine facture. Vous pouvez payer séparément vos solde actuel au moyen d’un paiement unique, afin d’éviter des frais de retard.","CTA_MAKE_PAYMENT_LINK":"/Payment/MakePayment","AUTOPAY_TRANSACTION_NOTE_DESC":"S’il y a des changements à venir à votre compte, ils pourraient avoir une incidence sur le crédit pour autopaiement ci-dessus. Tout changement apporté au crédit entrera en vigueur d’ici une à deux périodes de facturation.","AUTOPAY_TRANSACTION_NOTE_HEADING":"Remarque :","LOADER":"Chargement des données. Veuillez patienter…","INTERAC_FETCHED_LABEL":"Numéro de compte entré automatiquement","INTERAC_FETCHED_SUBTITLE":"Veuillez confirmer qu’il s’agit du bon compte.","ALERT_ERROR_HEADING_INTERAC":"Un problème technique est survenu","ALERT_ERROR_HEADING_INTERAC_SR":"Erreur: Un problème technique est survenu","ALERT_ERROR_HEADING_INTERAC_DESC":"Il nous est malheureusement impossible de traiter votre demande en raison d’un problème technique. Vous pouvez entrer vos renseignements bancaires manuellement.","FAILURE_API_BAN_HEADING":"Un problème est survenu","FAILURE_API_BAN_HEADING_SR":"Erreur, Un problème est survenu","FAILURE_API_BAN_MAIN_DESC":"Il nous est malheureusement impossible de traiter votre demande.","FAILURE_API_BAN_MAIN_DESC_2":"Veuillez réessayer.","FAILURE_API_BAN_SUB_DESC":"Si le problème persiste :","FAILURE_API_BAN_SUB_DESC_LISTITEM_1":"Essayez d’utiliser une autre carte de crédit ou encore de configurer des prélèvements automatiques par débit.","FAILURE_API_BAN_SUB_DESC_LISTITEM_2":"Attendez un moment, puis réessayez.","FAILURE_API_BAN_BUTTON":"Réessayer","LOADER_SUBMIT":"Transmission des renseignements de paiement en cours","LOADER_SUBMIT_DESC":"Veuillez patienter","LABEL_LOADED_OFFERS_TITLE":" – avec des paiements par prélèvement automatique, les services suivants donneront droit à un crédit pour autopaiement :","LABEL_LOADED_OFFER_TITLE":" – avec des paiements par prélèvement automatique, le service suivant donnera droit à un crédit pour autopaiement :","GREAT_NEWS":"Bonne nouvelle","MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE_HEADING":"Remarque :","MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE":" S’il y a des changements à venir à votre compte, ils pourraient avoir une incidence sur les crédits pour autopaiement ci-dessus. Tout changement apporté aux crédits entrera en vigueur d’ici une à deux périodes de facturation.","MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE":" S’il y a des changements à venir à votre compte, ils pourraient avoir une incidence sur le crédit pour autopaiement ci-dessus. Tout changement apporté au crédit entrera en vigueur d’ici une à deux périodes de facturation.","SORRY_MESSAGE":"Désolé, aucun crédit pour autopaiement n’est offert pour ce mode de paiement.","SORRY_MESSAGE_CREDIT":"Désolé, aucun crédit pour autopaiement n’est offert pour ce mode de paiement. Pour avoir droit au crédit, configurez les prélèvements automatiques par débit.","LABEL_LOADED_OFFERS_CREDIT_TITLE":" : avec des paiements par prélèvement automatique sur carte de crédit, les services suivants donneront droit à un crédit pour autopaiement :","LABEL_LOADED_OFFER_CREDIT_TITLE":" : avec des paiements par prélèvement automatique sur carte de crédit, le service suivant donnera droit à un crédit pour autopaiement :","LABEL_LOADED_OFFERS_DEBIT_TITLE":"avec des prélèvements automatiques par débit, les services suivants donneront droit à un crédit pour autopaiement :","LABEL_LOADED_OFFER_DEBIT_TITLE":"avec des paiements par prélèvement automatique sur carte de crédit, le service suivant donnera droit à un crédit pour autopaiement:","LABEL_LOADED_OFFERS_TITLE_TRUEAUTOPAY":"avec des paiements par prélèvement automatique, les services suivants donneront droit à un crédit pour autopaiement","LABEL_LOADED_OFFER_TITLE_TRUEAUTOPAY":"avec des paiements par prélèvement automatique, le service suivant donneront droit à un crédit pour autopaiement","REVIEW_PAGE_AUTOPAY_CREDIT":"Bonne nouvelle : vous aurez droit à un crédit pour autopaiement.","AUTOPAY_ALERT":"Votre crédit pour autopaiement débutera d’ici une à deux périodes de facturation.","AUTOPAY_CREDITS_TITLE":"Crédits pour autopaiement","InteracSupportedFinancialInstitutions":"BMO,CIBC,Desjardins,RBC,Scotiabank,TD","BANK_ACCOUNT_SR_TEXT":"Numéro de compte se terminant par {Account}","CREDIT_CARD_SR_TEXT":"Numéro de carte se terminant par {Account}","PAYMENT_METHOD_DEBIT":"Débit","SELECT_BANK_PLACEHOLDER":"Sélectionner votre institution financière","CREDIT_CARD_NAME_LABEL_V2":"Nom du détenteur du compte"}}')},function(e){"use strict";e.exports=u},function(e){"use strict";e.exports=s},function(e){"use strict";e.exports=c},function(e,t,n){var r;r=(e,t)=>(()=>{"use strict";function n(e){var t,r=ao[e];return void 0!==r?r.exports:(t=ao[e]={exports:{}},ro[e](t,t.exports,n),t.exports)}function r(e){e.length=0}function a(e,t,n){return Array.prototype.slice.call(e,t,n)}function i(e){return e.bind.apply(e,[null].concat(a(arguments,1)))}function o(e){return requestAnimationFrame(e)}function l(e,t){return typeof t===e}function u(e){return!s(e)&&l("object",e)}function s(e){return null===e}function c(e){try{return e instanceof(e.ownerDocument.defaultView||window).HTMLElement}catch(e){return!1}}function d(e){return ln(e)?e:[e]}function m(e,t){d(e).forEach(t)}function p(e,t){return e.indexOf(t)>-1}function b(e,t){return e.push.apply(e,d(t)),e}function f(e,t,n){e&&m(t,function(t){t&&e.classList[n?"add":"remove"](t)})}function E(e,t){f(e,sn(t)?t.split(" "):t,!0)}function y(e,t){m(t,e.appendChild.bind(e))}function _(e,t){m(e,function(e){var n=(t||e).parentNode;n&&n.insertBefore(e,t)})}function g(e,t){return c(e)&&(e.msMatchesSelector||e.matches).call(e,t)}function v(e,t){var n=e?a(e.children):[];return t?n.filter(function(e){return g(e,t)}):n}function N(e,t){return t?v(e,t)[0]:e.firstElementChild}function A(e,t,n){return e&&(n?dn(e).reverse():dn(e)).forEach(function(n){"__proto__"!==n&&t(e[n],n)}),e}function C(e){return a(arguments,1).forEach(function(t){A(t,function(n,r){e[r]=t[r]})}),e}function h(e){return a(arguments,1).forEach(function(t){A(t,function(t,n){ln(t)?e[n]=t.slice():u(t)?e[n]=h({},u(e[n])?e[n]:{},t):e[n]=t})}),e}function T(e,t){m(t||dn(e),function(t){delete e[t]})}function I(e,t){m(e,function(e){m(t,function(t){e&&e.removeAttribute(t)})})}function R(e,t,n){u(t)?A(t,function(t,n){R(e,n,t)}):m(e,function(e){s(n)||""===n?I(e,t):e.setAttribute(t,String(n))})}function x(e,t,n){var r=document.createElement(e);return t&&(sn(t)?E(r,t):R(r,t)),n&&y(n,r),r}function S(e,t,n){if(cn(n))return getComputedStyle(e)[t];s(n)||(e.style[t]=""+n)}function O(e,t){S(e,"display",t)}function M(e){e.setActive&&e.setActive()||e.focus({preventScroll:!0})}function L(e,t){return e.getAttribute(t)}function D(e,t){return e&&e.classList.contains(t)}function B(e){return e.getBoundingClientRect()}function P(e){m(e,function(e){e&&e.parentNode&&e.parentNode.removeChild(e)})}function k(e){return N((new DOMParser).parseFromString(e,"text/html").body)}function w(e,t){e.preventDefault(),t&&(e.stopPropagation(),e.stopImmediatePropagation())}function U(e,t){return e&&e.querySelector(t)}function F(e,t){return t?a(e.querySelectorAll(t)):[]}function H(e,t){f(e,t,!1)}function Y(e){return e.timeStamp}function j(e){return sn(e)?e:e?e+"px":""}function G(e,t){if(!e)throw new Error("["+mn+"] "+(t||""))}function V(e,t,n){return _n(e-t)<n}function z(e,t,n,r){var a=bn(t,n),i=fn(t,n);return r?a<e&&e<i:a<=e&&e<=i}function q(e,t,n){var r=bn(t,n),a=fn(t,n);return bn(fn(r,e),a)}function K(e){return+(e>0)-+(e<0)}function W(e,t){return m(t,function(t){e=e.replace("%s",""+t)}),e}function X(e){return e<10?"0"+e:""+e}function Q(){function e(e,t,n){m(e,function(e){e&&m(t,function(t){t.split(" ").forEach(function(t){var r=t.split(".");n(e,r[0],r[1])})})})}var t=[];return{bind:function(n,r,a,i){e(n,r,function(e,n,r){var o="addEventListener"in e,l=o?e.removeEventListener.bind(e,n,a,i):e.removeListener.bind(e,a);o?e.addEventListener(n,a,i):e.addListener(a),t.push([e,n,r,a,l])})},unbind:function(n,r,a){e(n,r,function(e,n,r){t=t.filter(function(t){return!!(t[0]!==e||t[1]!==n||t[2]!==r||a&&t[3]!==a)||(t[4](),!1)})})},dispatch:function(e,t,n){var r,a=!0;return"function"==typeof CustomEvent?r=new CustomEvent(t,{bubbles:a,detail:n}):(r=document.createEvent("CustomEvent")).initCustomEvent(t,a,!1,n),e.dispatchEvent(r),r},destroy:function(){t.forEach(function(e){e[4]()}),r(t)}}}function $(e){var t=e?e.event.bus:document.createDocumentFragment(),n=Q();return e&&e.event.on(Un,n.destroy),C(n,{bus:t,on:function(e,r){n.bind(t,d(e).join(" "),function(e){r.apply(r,ln(e.detail)?e.detail:[])})},off:i(n.unbind,t),emit:function(e){n.dispatch(t,e,a(arguments,1))}})}function Z(e,t,n,r){function a(){if(!m){if(d=e?bn((c()-u)/e,1):1,n&&n(d),d>=1&&(t(),u=c(),r&&++p>=r))return i();s=o(a)}}function i(){m=!0}function l(){s&&cancelAnimationFrame(s),d=0,s=0,m=!0}var u,s,c=Date.now,d=0,m=!0,p=0;return{start:function(t){t||l(),u=c()-(t?d*e:0),m=!1,s=o(a)},rewind:function(){u=c(),d=0,n&&n(d)},pause:i,cancel:l,set:function(t){e=t},isPaused:function(){return m}}}function J(e){return e=sn(e)?e:e.key,Zr[e]||e}function ee(e,t,n){function r(){a.forEach(function(e){e.style("transform","translateX(-"+100*e.index+"%)")})}var a=t.Slides;return{mount:function(){$(e).on([vn,Sn],r)},start:function(e,t){a.style("transition","opacity "+n.speed+"ms "+n.easing),an(t)},cancel:on}}function te(e,t,n){function r(){c(""),u.cancel()}var a,o=t.Move,l=t.Controller,u=t.Scroll,s=t.Elements.list,c=i(S,s,"transition");return{mount:function(){$(e).bind(s,"transitionend",function(e){e.target===s&&a&&(r(),a())})},start:function(t,r){var i=o.toPosition(t,!0),s=o.getPosition(),d=function(t){var r,a,i=n.rewindSpeed;return e.is(Kr)&&i&&(r=l.getIndex(!0),a=l.getEnd(),0===r&&t>=a||r>=a&&0===t)?i:n.speed}(t);_n(i-s)>=1&&d>=1?n.useScroll?u.scroll(i,d,!1,r):(c("transform "+d+"ms "+n.easing),o.translate(i,!0),a=r):(o.jump(t),r())},cancel:r}}function ne(...e){return e.filter(Boolean).join(" ")}function re(e){return null!==e&&"object"==typeof e}function ae(e,t){if(Array.isArray(e)&&Array.isArray(t))return e.length===t.length&&!e.some((e,n)=>!ae(e,t[n]));if(re(e)&&re(t)){const n=Object.keys(e),r=Object.keys(t);return n.length===r.length&&!n.some(n=>!Object.prototype.hasOwnProperty.call(t,n)||!ae(e[n],t[n]))}return e===t}function ie(e,t){const n=e;return function(e,t){if(e){const n=Object.keys(e);for(let r=0;r<n.length;r++){const a=n[r];if("__proto__"!==a&&!1===t(e[a],a))break}}}(t,(e,t)=>{Array.isArray(e)?n[t]=e.slice():re(e)?n[t]=ie(re(n[t])?n[t]:{},e):n[t]=e}),n}function oe(){return oe=Object.assign?Object.assign.bind():function(e){var t,n,r;for(t=1;t<arguments.length;t++)for(r in n=arguments[t])({}).hasOwnProperty.call(n,r)&&(e[r]=n[r]);return e},oe.apply(null,arguments)}function le(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}function ue(e){return e}function se(e,t){void 0===t&&(t=ue);var n=[],r=!1;return{read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(e){var a=t(e,r);return n.push(a),function(){n=n.filter(function(e){return e!==a})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){var t,a,i,o;r=!0,t=[],n.length&&(a=n,n=[],a.forEach(e),t=n),i=function(){var n=t;t=[],n.forEach(e)},(o=function(){return Promise.resolve().then(i)})(),n={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),n}}}}}function ce(e,t){return void 0===t&&(t=ue),se(e,t)}function de(e,t){return de=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},de(e,t)}function me(e){return me="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},me(e)}function pe(e){if(!e)return null;if("undefined"==typeof WeakRef)return function(){return e||null};var t=e?new WeakRef(e):null;return function(){return(null==t?void 0:t.deref())||null}}function be(e){setTimeout(e,1)}function fe(e,t,n,r){var a,i=null,o=e;do{if((a=r[o]).guard)a.node.dataset.focusAutoGuard&&(i=a);else{if(!a.lockItem)break;if(o!==e)return;i=null}}while((o+=n)!==t);i&&(i.node.tabIndex=0)}function Ee(){return"undefined"!=typeof window}function ye(e){return ve(e)?(e.nodeName||"").toLowerCase():"#document"}function _e(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function ge(e){var t;return null==(t=(ve(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function ve(e){return!!Ee()&&(e instanceof Node||e instanceof _e(e).Node)}function Ne(e){return!!Ee()&&(e instanceof Element||e instanceof _e(e).Element)}function Ae(e){return!!Ee()&&(e instanceof HTMLElement||e instanceof _e(e).HTMLElement)}function Ce(e){return!(!Ee()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof _e(e).ShadowRoot)}function he(e){const{overflow:t,overflowX:n,overflowY:r,display:a}=Oe(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!au.has(a)}function Te(e){return iu.has(ye(e))}function Ie(e){return ou.some(t=>{try{return e.matches(t)}catch(e){return!1}})}function Re(e){const t=xe(),n=Ne(e)?Oe(e):e;return lu.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||uu.some(e=>(n.willChange||"").includes(e))||su.some(e=>(n.contain||"").includes(e))}function xe(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function Se(e){return cu.has(ye(e))}function Oe(e){return _e(e).getComputedStyle(e)}function Me(e){return Ne(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Le(e){if("html"===ye(e))return e;const t=e.assignedSlot||e.parentNode||Ce(e)&&e.host||ge(e);return Ce(t)?t.host:t}function De(e){const t=Le(e);return Se(t)?e.ownerDocument?e.ownerDocument.body:e.body:Ae(t)&&he(t)?t:De(t)}function Be(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const a=De(e),i=a===(null==(r=e.ownerDocument)?void 0:r.body),o=_e(a);if(i){const e=Pe(o);return t.concat(o,o.visualViewport||[],he(a)?a:[],e&&n?Be(e):[])}return t.concat(a,Be(a,[],n))}function Pe(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ke(e){let t=e.activeElement;for(;null!=(null==(n=t)||null==(n=n.shadowRoot)?void 0:n.activeElement);){var n;t=t.shadowRoot.activeElement}return t}function we(e,t){if(!e||!t)return!1;const n=null==t.getRootNode?void 0:t.getRootNode();if(e.contains(t))return!0;if(n&&Ce(n)){let n=t;for(;n;){if(e===n)return!0;n=n.parentNode||n.host}}return!1}function Ue(){const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map(e=>{let{brand:t,version:n}=e;return t+"/"+n}).join(" "):navigator.userAgent}function Fe(){const e=/android/i;return e.test(function(){const e=navigator.userAgentData;return null!=e&&e.platform?e.platform:navigator.platform}())||e.test(Ue())}function He(e,t){const n=["mouse","pen"];return t||n.push("",void 0),n.includes(e)}function Ye(e){return(null==e?void 0:e.ownerDocument)||document}function je(e,t){if(null==t)return!1;if("composedPath"in e)return e.composedPath().includes(t);const n=e;return null!=n.target&&t.contains(n.target)}function Ge(e){return"composedPath"in e?e.composedPath()[0]:e.target}function Ve(e){return Ae(e)&&e.matches("input:not([type='hidden']):not([disabled]),[contenteditable]:not([contenteditable='false']),textarea:not([disabled])")}function ze(e){e.preventDefault(),e.stopPropagation()}function qe(e,t,n){return mu(e,du(t,n))}function Ke(e,t){return"function"==typeof e?e(t):e}function We(e){return e.split("-")[0]}function Xe(e){return e.split("-")[1]}function Qe(e){return"x"===e?"y":"x"}function $e(e){return"y"===e?"height":"width"}function Ze(e){return _u.has(We(e))?"y":"x"}function Je(e){return Qe(Ze(e))}function et(e){return e.replace(/start|end/g,e=>yu[e])}function tt(e){return e.replace(/left|right|bottom|top/g,e=>Eu[e])}function nt(e){return"number"!=typeof e?function(e){return{top:0,right:0,bottom:0,left:0,...e}}(e):{top:e,right:e,bottom:e,left:e}}function rt(e){const{x:t,y:n,width:r,height:a}=e;return{width:r,height:a,top:n,left:t,right:t+r,bottom:n+a,x:t,y:n}}function at(e,t,n){let{reference:r,floating:a}=e;const i=Ze(t),o=Je(t),l=$e(o),u=We(t),s="y"===i,c=r.x+r.width/2-a.width/2,d=r.y+r.height/2-a.height/2,m=r[l]/2-a[l]/2;let p;switch(u){case"top":p={x:c,y:r.y-a.height};break;case"bottom":p={x:c,y:r.y+r.height};break;case"right":p={x:r.x+r.width,y:d};break;case"left":p={x:r.x-a.width,y:d};break;default:p={x:r.x,y:r.y}}switch(Xe(t)){case"start":p[o]-=m*(n&&s?-1:1);break;case"end":p[o]+=m*(n&&s?-1:1)}return p}async function it(e,t){var n;void 0===t&&(t={});const{x:r,y:a,platform:i,rects:o,elements:l,strategy:u}=e,{boundary:s="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:m=!1,padding:p=0}=Ke(t,e),b=nt(p),f=l[m?"floating"===d?"reference":"floating":d],E=rt(await i.getClippingRect({element:null==(n=await(null==i.isElement?void 0:i.isElement(f)))||n?f:f.contextElement||await(null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:s,rootBoundary:c,strategy:u})),y="floating"===d?{x:r,y:a,width:o.floating.width,height:o.floating.height}:o.reference,_=await(null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),g=await(null==i.isElement?void 0:i.isElement(_))&&await(null==i.getScale?void 0:i.getScale(_))||{x:1,y:1},v=rt(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:_,strategy:u}):y);return{top:(E.top-v.top+b.top)/g.y,bottom:(v.bottom-E.bottom+b.bottom)/g.y,left:(E.left-v.left+b.left)/g.x,right:(v.right-E.right+b.right)/g.x}}function ot(e){const t=Oe(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const a=Ae(e),i=a?e.offsetWidth:n,o=a?e.offsetHeight:r,l=pu(n)!==i||pu(r)!==o;return l&&(n=i,r=o),{width:n,height:r,$:l}}function lt(e){return Ne(e)?e:e.contextElement}function ut(e){const t=lt(e);if(!Ae(t))return fu(1);const n=t.getBoundingClientRect(),{width:r,height:a,$:i}=ot(t);let o=(i?pu(n.width):n.width)/r,l=(i?pu(n.height):n.height)/a;return o&&Number.isFinite(o)||(o=1),l&&Number.isFinite(l)||(l=1),{x:o,y:l}}function st(e){const t=_e(e);return xe()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:hu}function ct(e,t,n,r){void 0===t&&(t=!1),void 0===n&&(n=!1);const a=e.getBoundingClientRect(),i=lt(e);let o=fu(1);t&&(r?Ne(r)&&(o=ut(r)):o=ut(e));const l=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==_e(e))&&t}(i,n,r)?st(i):fu(0);let u=(a.left+l.x)/o.x,s=(a.top+l.y)/o.y,c=a.width/o.x,d=a.height/o.y;if(i){const e=_e(i),t=r&&Ne(r)?_e(r):r;let n=e,a=Pe(n);for(;a&&r&&t!==n;){const e=ut(a),t=a.getBoundingClientRect(),r=Oe(a),i=t.left+(a.clientLeft+parseFloat(r.paddingLeft))*e.x,o=t.top+(a.clientTop+parseFloat(r.paddingTop))*e.y;u*=e.x,s*=e.y,c*=e.x,d*=e.y,u+=i,s+=o,n=_e(a),a=Pe(n)}}return rt({width:c,height:d,x:u,y:s})}function dt(e,t){const n=Me(e).scrollLeft;return t?t.left+n:ct(ge(e)).left+n}function mt(e,t,n){void 0===n&&(n=!1);const r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:dt(e,r)),y:r.top+t.scrollTop}}function pt(e,t,n){let r;if("viewport"===t)r=function(e,t){const n=_e(e),r=ge(e),a=n.visualViewport;let i=r.clientWidth,o=r.clientHeight,l=0,u=0;if(a){i=a.width,o=a.height;const e=xe();(!e||e&&"fixed"===t)&&(l=a.offsetLeft,u=a.offsetTop)}return{width:i,height:o,x:l,y:u}}(e,n);else if("document"===t)r=function(e){const t=ge(e),n=Me(e),r=e.ownerDocument.body,a=mu(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=mu(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let o=-n.scrollLeft+dt(e);const l=-n.scrollTop;return"rtl"===Oe(r).direction&&(o+=mu(t.clientWidth,r.clientWidth)-a),{width:a,height:i,x:o,y:l}}(ge(e));else if(Ne(t))r=function(e,t){const n=ct(e,!0,"fixed"===t),r=n.top+e.clientTop,a=n.left+e.clientLeft,i=Ae(e)?ut(e):fu(1);return{width:e.clientWidth*i.x,height:e.clientHeight*i.y,x:a*i.x,y:r*i.y}}(t,n);else{const n=st(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return rt(r)}function bt(e,t){const n=Le(e);return!(n===t||!Ne(n)||Se(n))&&("fixed"===Oe(n).position||bt(n,t))}function ft(e,t,n){function r(){s.x=dt(i)}const a=Ae(t),i=ge(t),o="fixed"===n,l=ct(e,!0,o,t);let u={scrollLeft:0,scrollTop:0};const s=fu(0);if(a||!a&&!o)if(("body"!==ye(t)||he(i))&&(u=Me(t)),a){const e=ct(t,!0,o,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else i&&r();o&&!a&&i&&r();const c=!i||a||o?fu(0):mt(i,u);return{x:l.left+u.scrollLeft-s.x-c.x,y:l.top+u.scrollTop-s.y-c.y,width:l.width,height:l.height}}function Et(e){return"static"===Oe(e).position}function yt(e,t){if(!Ae(e)||"fixed"===Oe(e).position)return null;if(t)return t(e);let n=e.offsetParent;return ge(e)===n&&(n=n.ownerDocument.body),n}function _t(e,t){const n=_e(e);if(Ie(e))return n;if(!Ae(e)){let t=Le(e);for(;t&&!Se(t);){if(Ne(t)&&!Et(t))return t;t=Le(t)}return n}let r=yt(e,t);for(;r&&Te(r)&&Et(r);)r=yt(r,t);return r&&Se(r)&&Et(r)&&!Re(r)?n:r||function(e){let t=Le(e);for(;Ae(t)&&!Se(t);){if(Re(t))return t;if(Ie(t))return null;t=Le(t)}return null}(e)||n}function gt(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function vt(e,t,n,r){void 0===r&&(r={});const{ancestorScroll:a=!0,ancestorResize:i=!0,elementResize:o="function"==typeof ResizeObserver,layoutShift:l="function"==typeof IntersectionObserver,animationFrame:u=!1}=r,s=lt(e),c=a||i?[...s?Be(s):[],...Be(t)]:[];c.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),i&&e.addEventListener("resize",n)});const d=s&&l?function(e,t){function n(){var e;clearTimeout(r),null==(e=a)||e.disconnect(),a=null}let r,a=null;const i=ge(e);return function o(l,u){function s(t){const n=t[0].intersectionRatio;if(n!==u){if(!E)return o();n?o(!1,n):r=setTimeout(()=>{o(!1,1e-7)},1e3)}1!==n||gt(c,e.getBoundingClientRect())||o(),E=!1}void 0===l&&(l=!1),void 0===u&&(u=1),n();const c=e.getBoundingClientRect(),{left:d,top:m,width:p,height:b}=c;if(l||t(),!p||!b)return;const f={rootMargin:-bu(m)+"px "+-bu(i.clientWidth-(d+p))+"px "+-bu(i.clientHeight-(m+b))+"px "+-bu(d)+"px",threshold:mu(0,du(1,u))||1};let E=!0;try{a=new IntersectionObserver(s,{...f,root:i.ownerDocument})}catch(e){a=new IntersectionObserver(s,f)}a.observe(e)}(!0),n}(s,n):null;let m,p=-1,b=null;o&&(b=new ResizeObserver(e=>{let[r]=e;r&&r.target===s&&b&&(b.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=b)||e.observe(t)})),n()}),s&&!u&&b.observe(s),b.observe(t));let f=u?ct(e):null;return u&&function t(){const r=ct(e);f&&!gt(f,r)&&n(),f=r,m=requestAnimationFrame(t)}(),n(),()=>{var e;c.forEach(e=>{a&&e.removeEventListener("scroll",n),i&&e.removeEventListener("resize",n)}),null==d||d(),null==(e=b)||e.disconnect(),b=null,u&&cancelAnimationFrame(m)}}function Nt(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;let n,r,a;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;0!==r--;)if(!Nt(e[r],t[r]))return!1;return!0}if(a=Object.keys(e),n=a.length,n!==Object.keys(t).length)return!1;for(r=n;0!==r--;)if(!{}.hasOwnProperty.call(t,a[r]))return!1;for(r=n;0!==r--;){const n=a[r];if(!("_owner"===n&&e.$$typeof||Nt(e[n],t[n])))return!1}return!0}return e!=e&&t!=t}function At(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Ct(e,t){const n=At(e);return Math.round(t*n)/n}function ht(e){const t=Wt.useRef(e);return to(()=>{t.current=e}),t}function Tt(e){return Wt.useMemo(()=>e.every(e=>null==e)?null:t=>{e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})},e)}function It(e){const t=Wt.useRef(()=>{});return ku(()=>{t.current=e}),Wt.useCallback(function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)},[])}function Rt(){return Rt=Object.assign?Object.assign.bind():function(e){var t,n,r;for(t=1;t<arguments.length;t++)for(r in n=arguments[t])Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r]);return e},Rt.apply(this,arguments)}function xt(e){return"data-floating-ui-"+e}function St(e){const t=(0,Wt.useRef)(e);return no(()=>{t.current=e}),t}function Ot(e,t,n){return n&&!He(n)?0:"number"==typeof e?e:null==e?void 0:e[t]}function Mt(e,t){void 0===t&&(t={});const{preventScroll:n=!1,cancelPrevious:r=!0,sync:a=!1}=t;r&&cancelAnimationFrame(Ku);const i=()=>null==e?void 0:e.focus({preventScroll:n});a?i():Ku=requestAnimationFrame(i)}function Lt(e,t){let n=e.filter(e=>{var n;return e.parentId===t&&(null==(n=e.context)?void 0:n.open)}),r=n;for(;r.length;)r=e.filter(e=>{var t;return null==(t=r)?void 0:t.some(t=>{var n;return e.parentId===t.id&&(null==(n=e.context)?void 0:n.open)})}),n=n.concat(r);return n}function Dt(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);const r=Ye(e[0]).body;return function(e,t,n,r){const a="data-floating-ui-inert",i=r?"inert":n?"aria-hidden":null,o=(l=t,e.map(e=>{if(l.contains(e))return e;const t=Zu(e);return l.contains(t)?t:null}).filter(e=>null!=e));var l;const u=new Set,s=new Set(o),c=[];Qu[a]||(Qu[a]=new WeakMap);const d=Qu[a];return o.forEach(function e(t){t&&!u.has(t)&&(u.add(t),t.parentNode&&e(t.parentNode))}),function e(t){t&&!s.has(t)&&[].forEach.call(t.children,t=>{if("script"!==ye(t))if(u.has(t))e(t);else{const e=i?t.getAttribute(i):null,n=null!==e&&"false"!==e,r=(Wu.get(t)||0)+1,o=(d.get(t)||0)+1;Wu.set(t,r),d.set(t,o),c.push(t),1===r&&n&&Xu.add(t),1===o&&t.setAttribute(a,""),!n&&i&&t.setAttribute(i,"true")}})}(t),u.clear(),$u++,()=>{c.forEach(e=>{const t=(Wu.get(e)||0)-1,n=(d.get(e)||0)-1;Wu.set(e,t),d.set(e,n),t||(!Xu.has(e)&&i&&e.removeAttribute(i),Xu.delete(e)),n||e.removeAttribute(a)}),$u--,$u||(Wu=new WeakMap,Wu=new WeakMap,Xu=new WeakSet,Qu={})}}(e.concat(Array.from(r.querySelectorAll("[aria-live]"))),r,t,n)}function Bt(e,t){const n=eo(e,Ju());"prev"===t&&n.reverse();const r=n.indexOf(ke(Ye(e)));return n.slice(r+1)[0]}function Pt(e,t){const n=t||e.currentTarget,r=e.relatedTarget;return!r||!we(n,r)}function kt(e){"Tab"===e.key&&(e.target,clearTimeout(void 0))}function wt(e){as=as.filter(e=>e.isConnected);let t=e;if(t&&"body"!==ye(t)){if(!function(e,t){if(t=t||{},!e)throw new Error("No node provided");return!1!==Yi.call(e,Fi)&&$i(t,e)}(t,Ju())){const e=eo(t,Ju())[0];e&&(t=e)}as.push(t),as.length>20&&(as=as.slice(-20))}}function Ut(){return as.slice().reverse().find(e=>e.isConnected)}function Ft(e){function t(e){return!o&&p&&m?Wt.createElement(is,{ref:"start"===e?L:D,onClick:e=>_(!1,e.nativeEvent)},"string"==typeof p?p:"Dismiss"):null}var n,r;const{context:a,children:i,disabled:o=!1,order:l=["content"],guards:u=!0,initialFocus:s=0,returnFocus:c=!0,restoreFocus:d=!1,modal:m=!0,visuallyHiddenDismiss:p=!1,closeOnFocusOut:b=!0}=e,{open:f,refs:E,nodeId:y,onOpenChange:_,events:g,dataRef:v,floatingId:N,elements:{domReference:A,floating:C}}=a,h="number"==typeof s&&s<0,T=!!(n=A)&&"combobox"===n.getAttribute("role")&&Ve(n)&&h,I="undefined"==typeof HTMLElement||!("inert"in HTMLElement.prototype)||u,R=St(l),x=St(s),S=St(c),O=zu(),M=Wt.useContext(ns),L=Wt.useRef(null),D=Wt.useRef(null),B=Wt.useRef(!1),P=Wt.useRef(!1),k=Wt.useRef(-1),w=null!=M,U=(r=C)?r.hasAttribute(rs)?r:r.querySelector("["+rs+"]")||r:null,F=It(function(e){return void 0===e&&(e=U),e?eo(e,Ju()):[]}),H=It(e=>{const t=F(e);return R.current.map(e=>A&&"reference"===e?A:U&&"floating"===e?U:t).filter(Boolean).flat()});Wt.useEffect(()=>{function e(e){if("Tab"===e.key){we(U,ke(Ye(U)))&&0===F().length&&!T&&ze(e);const t=H(),n=Ge(e);"reference"===R.current[0]&&n===A&&(ze(e),e.shiftKey?Mt(t[t.length-1]):Mt(t[1])),"floating"===R.current[1]&&n===U&&e.shiftKey&&(ze(e),Mt(t[0]))}}if(o)return;if(!m)return;const t=Ye(U);return t.addEventListener("keydown",e),()=>{t.removeEventListener("keydown",e)}},[o,A,U,m,R,T,F,H]),Wt.useEffect(()=>{function e(e){const t=Ge(e),n=F().indexOf(t);-1!==n&&(k.current=n)}if(!o&&C)return C.addEventListener("focusin",e),()=>{C.removeEventListener("focusin",e)}},[o,C,F]),Wt.useEffect(()=>{function e(){P.current=!0,setTimeout(()=>{P.current=!1})}function t(e){const t=e.relatedTarget;queueMicrotask(()=>{const n=!(we(A,t)||we(C,t)||we(t,C)||we(null==M?void 0:M.portalNode,t)||null!=t&&t.hasAttribute(xt("focus-guard"))||O&&(Lt(O.nodesRef.current,y).find(e=>{var n,r;return we(null==(n=e.context)?void 0:n.elements.floating,t)||we(null==(r=e.context)?void 0:r.elements.domReference,t)})||function(e,t){var n;let r=[],a=null==(n=e.find(e=>e.id===t))?void 0:n.parentId;for(;a;){const t=e.find(e=>e.id===a);a=null==t?void 0:t.parentId,t&&(r=r.concat(t))}return r}(O.nodesRef.current,y).find(e=>{var n,r;return(null==(n=e.context)?void 0:n.elements.floating)===t||(null==(r=e.context)?void 0:r.elements.domReference)===t})));if(d&&n&&ke(Ye(U))===Ye(U).body){Ae(U)&&U.focus();const e=k.current,t=F(),n=t[e]||t[t.length-1]||U;Ae(n)&&n.focus()}!T&&m||!t||!n||P.current||t===Ut()||(B.current=!0,_(!1,e,"focus-out"))})}if(!o&&b)return C&&Ae(A)?(A.addEventListener("focusout",t),A.addEventListener("pointerdown",e),C.addEventListener("focusout",t),()=>{A.removeEventListener("focusout",t),A.removeEventListener("pointerdown",e),C.removeEventListener("focusout",t)}):void 0},[o,A,C,U,m,y,O,M,_,b,d,F,T]),Wt.useEffect(()=>{var e;if(o)return;const t=Array.from((null==M||null==(e=M.portalNode)?void 0:e.querySelectorAll("["+xt("portal")+"]"))||[]);if(C){const e=[C,...t,L.current,D.current,R.current.includes("reference")||T?A:null].filter(e=>null!=e),n=m||T?Dt(e,I,!I):Dt(e);return()=>{n()}}},[o,A,C,m,R,M,T,I]),no(()=>{if(o||!Ae(U))return;const e=ke(Ye(U));queueMicrotask(()=>{const t=H(U),n=x.current,r=("number"==typeof n?t[n]:n.current)||U,a=we(U,e);h||a||!f||Mt(r,{preventScroll:r===U})})},[o,f,U,h,H,x]),no(()=>{function e(e){let{open:n,reason:r,event:i,nested:o}=e;n&&(a=i),"escape-key"===r&&E.domReference.current&&wt(E.domReference.current),"hover"===r&&"mouseleave"===i.type&&(B.current=!0),"outside-press"===r&&(o?(B.current=!1,t=!0):B.current=!(function(e){return!(0!==e.mozInputSource||!e.isTrusted)||(Fe()&&e.pointerType?"click"===e.type&&1===e.buttons:0===e.detail&&!e.pointerType)}(i)||function(e){return!Ue().includes("jsdom/")&&(!Fe()&&0===e.width&&0===e.height||Fe()&&1===e.width&&1===e.height&&0===e.pressure&&0===e.detail&&"mouse"===e.pointerType||e.width<1&&e.height<1&&0===e.pressure&&0===e.detail&&"touch"===e.pointerType)}(i)))}if(o||!U)return;let t=!1;const n=Ye(U),r=ke(n);let a=v.current.openEvent;wt(r),g.on("openchange",e);const i=n.createElement("span");return i.setAttribute("tabindex","-1"),i.setAttribute("aria-hidden","true"),Object.assign(i.style,es),w&&A&&A.insertAdjacentElement("afterend",i),()=>{g.off("openchange",e);const r=ke(n),o=we(C,r)||O&&Lt(O.nodesRef.current,y).some(e=>{var t;return we(null==(t=e.context)?void 0:t.elements.floating,r)});(o||a&&["click","mousedown"].includes(a.type))&&E.domReference.current&&wt(E.domReference.current);const l="boolean"==typeof S.current?Ut()||i:S.current.current||i;queueMicrotask(()=>{S.current&&!B.current&&Ae(l)&&(l===r||r===n.body||o)&&l.focus({preventScroll:t}),i.remove()})}},[o,C,U,S,v,E,g,O,y,w,A]),Wt.useEffect(()=>{queueMicrotask(()=>{B.current=!1})},[o]),no(()=>{if(!o&&M)return M.setFocusManagerState({modal:m,closeOnFocusOut:b,open:f,onOpenChange:_,refs:E}),()=>{M.setFocusManagerState(null)}},[o,M,m,f,_,E,b]),no(()=>{if(o)return;if(!U)return;if("function"!=typeof MutationObserver)return;if(h)return;const e=()=>{const e=U.getAttribute("tabindex"),t=F(),n=ke(Ye(C)),r=t.indexOf(n);-1!==r&&(k.current=r),R.current.includes("floating")||n!==E.domReference.current&&0===t.length?"0"!==e&&U.setAttribute("tabindex","0"):"-1"!==e&&U.setAttribute("tabindex","-1")};e();const t=new MutationObserver(e);return t.observe(U,{childList:!0,subtree:!0,attributes:!0}),()=>{t.disconnect()}},[o,C,U,E,R,F,h]);const Y=!o&&I&&(!m||!T)&&(w||m);return Wt.createElement(Wt.Fragment,null,Y&&Wt.createElement(ts,{"data-type":"inside",ref:null==M?void 0:M.beforeInsideRef,onFocus:e=>{if(m){const e=H();Mt("reference"===l[0]?e[0]:e[e.length-1])}else if(null!=M&&M.preserveTabOrder&&M.portalNode)if(B.current=!1,Pt(e,M.portalNode)){const e=Bt(document.body,"next")||A;null==e||e.focus()}else{var t;null==(t=M.beforeOutsideRef.current)||t.focus()}}}),!T&&t("start"),i,t("end"),Y&&Wt.createElement(ts,{"data-type":"inside",ref:null==M?void 0:M.afterInsideRef,onFocus:e=>{if(m)Mt(H()[0]);else if(null!=M&&M.preserveTabOrder&&M.portalNode)if(b&&(B.current=!0),Pt(e,M.portalNode)){const e=Bt(document.body,"prev")||A;null==e||e.focus()}else{var t;null==(t=M.afterOutsideRef.current)||t.focus()}}}))}function Ht(e){return Ae(e.target)&&"BUTTON"===e.target.tagName}function Yt(e){return Ve(e)}function jt(e,t,n){const r=new Map,a="item"===n;let i=e;if(a&&e){const{[ss]:t,[cs]:n,...r}=e;i=r}return{..."floating"===n&&{tabIndex:-1,[rs]:""},...i,...t.map(t=>{const r=t?t[n]:null;return"function"==typeof r?e?r(e):null:r}).concat(e).reduce((e,t)=>t?(Object.entries(t).forEach(t=>{let[n,i]=t;var o;a&&[ss,cs].includes(n)||(0===n.indexOf("on")?(r.has(n)||r.set(n,[]),"function"==typeof i&&(null==(o=r.get(n))||o.push(i),e[n]=function(){for(var e,t=arguments.length,a=new Array(t),i=0;i<t;i++)a[i]=arguments[i];return null==(e=r.get(n))?void 0:e.map(e=>e(...a)).find(e=>void 0!==e)})):e[n]=i)}),e):e,{})}}function Gt(e,t){const[n,r]=e;let a=!1;const i=t.length;for(let o=0,l=i-1;o<i;l=o++){const[e,i]=t[o]||[0,0],[u,s]=t[l]||[0,0];i>=r!=s>=r&&n<=(u-e)*(r-i)/(s-i)+e&&(a=!a)}return a}function Vt(e){void 0===e&&(e={});const{buffer:t=.5,blockPointerEvents:n=!1,requireIntent:r=!0}=e;let a,i=!1,o=null,l=null,u=performance.now();const s=e=>{let{x:n,y:s,placement:c,elements:d,onClose:m,nodeId:p,tree:b}=e;return function(e){function f(){clearTimeout(a),m()}if(clearTimeout(a),!d.domReference||!d.floating||null==c||null==n||null==s)return;const{clientX:E,clientY:y}=e,_=[E,y],g=Ge(e),v="mouseleave"===e.type,N=we(d.floating,g),A=we(d.domReference,g),C=d.domReference.getBoundingClientRect(),h=d.floating.getBoundingClientRect(),T=c.split("-")[0],I=n>h.right-h.width/2,R=s>h.bottom-h.height/2,x=function(e,t){return e[0]>=t.x&&e[0]<=t.x+t.width&&e[1]>=t.y&&e[1]<=t.y+t.height}(_,C),S=h.width>C.width,O=h.height>C.height,M=(S?C:h).left,L=(S?C:h).right,D=(O?C:h).top,B=(O?C:h).bottom;if(N&&(i=!0,!v))return;if(A&&(i=!1),A&&!v)return void(i=!0);if(v&&Ne(e.relatedTarget)&&we(d.floating,e.relatedTarget))return;if(b&&Lt(b.nodesRef.current,p).some(e=>{let{context:t}=e;return null==t?void 0:t.open}))return;if("top"===T&&s>=C.bottom-1||"bottom"===T&&s<=C.top+1||"left"===T&&n>=C.right-1||"right"===T&&n<=C.left+1)return f();let P=[];switch(T){case"top":P=[[M,C.top+1],[M,h.bottom-1],[L,h.bottom-1],[L,C.top+1]];break;case"bottom":P=[[M,h.top+1],[M,C.bottom-1],[L,C.bottom-1],[L,h.top+1]];break;case"left":P=[[h.right-1,B],[h.right-1,D],[C.left+1,D],[C.left+1,B]];break;case"right":P=[[C.right-1,B],[C.right-1,D],[h.left+1,D],[h.left+1,B]]}if(!Gt([E,y],P)){if(i&&!x)return f();if(!v&&r){const t=function(e,t){const n=performance.now(),r=n-u;if(null===o||null===l||0===r)return o=e,l=t,u=n,null;const a=e-o,i=t-l,s=Math.sqrt(a*a+i*i);return o=e,l=t,u=n,s/r}(e.clientX,e.clientY);if(null!==t&&t<.1)return f()}Gt([E,y],function(e){let[n,r]=e;switch(T){case"top":return[[S?n+t/2:I?n+4*t:n-4*t,r+t+1],[S?n-t/2:I?n+4*t:n-4*t,r+t+1],[h.left,I||S?h.bottom-t:h.top],[h.right,I?S?h.bottom-t:h.top:h.bottom-t]];case"bottom":return[[S?n+t/2:I?n+4*t:n-4*t,r-t],[S?n-t/2:I?n+4*t:n-4*t,r-t],[h.left,I||S?h.top+t:h.bottom],[h.right,I?S?h.top+t:h.bottom:h.top+t]];case"left":{const e=[n+t+1,O?r+t/2:R?r+4*t:r-4*t],a=[n+t+1,O?r-t/2:R?r+4*t:r-4*t];return[[R||O?h.right-t:h.left,h.top],[R?O?h.right-t:h.left:h.right-t,h.bottom],e,a]}case"right":return[[n-t,O?r+t/2:R?r+4*t:r-4*t],[n-t,O?r-t/2:R?r+4*t:r-4*t],[R||O?h.left+t:h.right,h.top],[R?O?h.left+t:h.right:h.left+t,h.bottom]]}}([n,s]))?!i&&r&&(a=window.setTimeout(f,40)):f()}}};return s.__options={blockPointerEvents:n},s}var zt,qt,Kt,Wt,Xt,Qt,$t,Zt,Jt,en,tn,nn,rn,an,on,ln,un,sn,cn,dn,mn,pn,bn,fn,En,yn,_n,gn,vn,Nn,An,Cn,hn,Tn,In,Rn,xn,Sn,On,Mn,Ln,Dn,Bn,Pn,kn,wn,Un,Fn,Hn,Yn,jn,Gn,Vn,zn,qn,Kn,Wn,Xn,Qn,$n,Zn,Jn,er,tr,nr,rr,ar,ir,or,lr,ur,sr,cr,dr,mr,pr,br,fr,Er,yr,_r,gr,vr,Nr,Ar,Cr,hr,Tr,Ir,Rr,xr,Sr,Or,Mr,Lr,Dr,Br,Pr,kr,wr,Ur,Fr,Hr,Yr,jr,Gr,Vr,zr,qr,Kr,Wr,Xr,Qr,$r,Zr,Jr,ea,ta,na,ra,aa,ia,oa,la,ua,sa,ca,da,ma,pa,ba,fa,Ea,ya,_a,ga,va,Na,Aa,Ca,ha,Ta,Ia,Ra,xa,Sa,Oa,Ma,La,Da,Ba,Pa,ka,wa,Ua,Fa,Ha,Ya,ja,Ga,Va,za,qa,Ka,Wa,Xa,Qa,$a,Za,Ja,ei,ti,ni,ri,ai,ii,oi,li,ui,si,ci,di,mi,pi,bi,fi,Ei,yi,_i,gi,vi,Ni,Ai,Ci,hi,Ti,Ii,Ri,xi,Si,Oi,Mi,Li,Di,Bi,Pi,ki,wi,Ui,Fi,Hi,Yi,ji,Gi,Vi,zi,qi,Ki,Wi,Xi,Qi,$i,Zi,Ji,eo,to,no,ro={155:t=>{t.exports=e},514:e=>{e.exports=t}},ao={};n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},qt=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,n.t=function(e,t){var r,a,i;if(1&t&&(e=this(e)),8&t)return e;if("object"==typeof e&&e){if(4&t&&e.__esModule)return e;if(16&t&&"function"==typeof e.then)return e}for(r=Object.create(null),n.r(r),a={},zt=zt||[null,qt({}),qt([]),qt(qt)],i=2&t&&e;"object"==typeof i&&!~zt.indexOf(i);i=qt(i))Object.getOwnPropertyNames(i).forEach(t=>a[t]=()=>e[t]);return a.default=()=>e,n.d(r,a),r},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},Kt={},n.r(Kt),n.d(Kt,{Accordion:()=>oo,AccordionContent:()=>fo,AccordionIcon:()=>yo,AccordionItem:()=>uo,AccordionToggleTitle:()=>_o,AccordionTrigger:()=>go,Alert:()=>ho,Button:()=>xo,Card:()=>No,Carousel:()=>Oo,CarouselArrows:()=>Mo,CarouselContent:()=>Lo,CarouselItem:()=>Do,CarouselPagination:()=>Bo,Checkbox:()=>Ho,CheckboxCard:()=>zo,CheckboxCardBody:()=>qo,CheckboxCardInput:()=>jo,CheckboxCardPrice:()=>Ko,Container:()=>Wo,Divider:()=>Xo,DockBar:()=>$o,DynamicRadioContent:()=>Xl,Fixed:()=>Zo,FooterLegal:()=>nl,FormControl:()=>al,FormGroup:()=>ol,Heading:()=>ul,HeadingStep:()=>sl,Icon:()=>Eo,IconLink:()=>bl,InputError:()=>Uo,InputText:()=>yl,Label:()=>gl,Link:()=>pl,ListItem:()=>el,Modal:()=>Tl,ModalBody:()=>Rl,ModalContent:()=>Ml,ModalFooter:()=>Dl,ModalHeader:()=>Pl,Popover:()=>Os,PopoverContent:()=>Ps,PopoverTrigger:()=>Ds,Portal:()=>Cl,Price:()=>Fl,RadioButton:()=>Gl,RadioCard:()=>Wl,RadioCardBody:()=>Ql,RadioCardInput:()=>zl,RadioCardPrice:()=>$l,SameHeightGroup:()=>Jl,SameHeightItem:()=>eu,Select:()=>_s,SelectContext:()=>ru,SelectCustom:()=>bs,SelectDropdown:()=>Es,SelectNativeHidden:()=>ms,SelectOption:()=>gs,SimpleFooter:()=>Ns,SimpleHeader:()=>vs,SrOnly:()=>Ao,Static:()=>Jo,Tab:()=>Is,TabList:()=>Rs,TabPanel:()=>xs,Tabs:()=>hs,Tag:()=>As,Text:()=>wo,default:()=>ks,useBodyHeightObserver:()=>bo,useHeightResizeObserver:()=>mo,useKeyboardListener:()=>so,useResponsiveHeight:()=>po,useWindowResize:()=>co}),Wt=n(155),Xt=n.t(Wt,2),Qt=n.n(Wt);const io=(0,Wt.createContext)({activeItems:null,toggleAccordionItems:null}),oo=({mode:e,children:t,onActive:n,onInactive:r})=>{const[a,i]=(0,Wt.useState)([]);return(0,Wt.useEffect)(()=>{0===a.length?r&&r():n&&n()},[a]),Qt().createElement(io.Provider,{value:{activeItems:a,toggleAccordionItems:t=>{"multiple"===e?a.includes(t)?i(e=>e.filter(e=>e!==t)):i(e=>[...e,t]):a.includes(t)?i([]):(i([]),i(e=>[...e,t]))}}},t)},lo=(0,Wt.createContext)(null),uo=({children:e,index:t,activeByDefault:n})=>{const{toggleAccordionItems:r}=(0,Wt.useContext)(io);return(0,Wt.useEffect)(()=>{n&&r&&r(t)},[n]),Qt().createElement(lo.Provider,{value:t},e)},so=(e,t="keydown")=>{(0,Wt.useEffect)(()=>(document.addEventListener(t,e),()=>{document.removeEventListener(t,e)}),[])},co=(e=300)=>{const[t,n]=(0,Wt.useState)({width:window.innerWidth,height:window.innerHeight});return(0,Wt.useEffect)(()=>{const t=()=>{let t=null;t&&clearTimeout(t),t=setTimeout(()=>{n({width:window.innerWidth,height:window.innerHeight})},e)};return window.addEventListener("resize",t),()=>{window.removeEventListener("resize",t)}},[e]),t};$t=new WeakMap,Zt=new WeakMap,Jt={},en=0,tn=function(e){return e&&(e.host||tn(e.parentNode))},nn=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),a=t||function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body}(e);return a?(r.push.apply(r,Array.from(a.querySelectorAll("[aria-live], script"))),function(e,t,n,r){var a,i,o,l,u,s,c=function(e,t){return t.map(function(t){if(e.contains(t))return t;var n=tn(t);return n&&e.contains(n)?n:null}).filter(function(e){return Boolean(e)})}(t,Array.isArray(e)?e:[e]);return Jt[n]||(Jt[n]=new WeakMap),a=Jt[n],i=[],o=new Set,l=new Set(c),u=function(e){e&&!o.has(e)&&(o.add(e),u(e.parentNode))},c.forEach(u),s=function(e){e&&!l.has(e)&&Array.prototype.forEach.call(e.children,function(e){if(o.has(e))s(e);else try{var t=e.getAttribute(r),l=null!==t&&"false"!==t,u=($t.get(e)||0)+1,c=(a.get(e)||0)+1;$t.set(e,u),a.set(e,c),i.push(e),1===u&&l&&Zt.set(e,!0),1===c&&e.setAttribute(n,"true"),l||e.setAttribute(r,"true")}catch(t){}})},s(t),o.clear(),en++,function(){i.forEach(function(e){var t=$t.get(e)-1,i=a.get(e)-1;$t.set(e,t),a.set(e,i),t||(Zt.has(e)||e.removeAttribute(r),Zt.delete(e)),i||e.removeAttribute(n)}),--en||($t=new WeakMap,$t=new WeakMap,Zt=new WeakMap,Jt={})}}(r,a,n,"aria-hidden")):function(){return null}};const mo=(e,t,n)=>{(0,Wt.useEffect)(()=>{const r=new ResizeObserver(r=>{for(const a of r)if(a.target===e.current){const e=a.contentRect.height;e!==t&&n(e)}});return e.current&&r.observe(e.current),()=>{r.disconnect()}},[])},po=(e,t)=>(0,Wt.useMemo)(()=>e<768?t.mobile:e>=768&&e<992?t.tablet:t.desktop,[e,t]),bo=(e=300)=>{const[t,n]=(0,Wt.useState)(0);return(0,Wt.useEffect)(()=>{const t=new ResizeObserver(t=>{const r=t[0].target;let a=null;a&&clearTimeout(a),a=setTimeout(()=>{n(r.scrollHeight)},e)});return t.observe(document.body),()=>{t.disconnect()}},[e]),t},fo=({children:e,id:t,className:n,collapseHeight:r,expandHeight:a,"aria-labelledby":i,...o})=>{const{activeItems:l}=(0,Wt.useContext)(io),u=(0,Wt.useContext)(lo),s=(0,Wt.useRef)(null),c=l?.includes(u),[d,m]=(0,Wt.useState)(s.current?.scrollHeight||0),p=a?parseInt(a,10):d,b=c?`${Math.min(d,p)}px`:r;return mo(s,d,m),Qt().createElement("div",{role:"region",className:["brui-overflow-hidden brui-transition-height brui-ease-in-out brui-duration-200 brui-h-0 brui-group brui-max-h-full",r?.trim()?"brui-scrollbar":"",a?.trim()?"brui-scrollbar":"",l?.includes(u)||r?"":"brui-collapse",n].join(" ").trim(),id:t,"aria-labelledby":i,...o,style:{height:b}},Qt().createElement("div",{className:c||a?"":"brui-hidden",ref:s},e))},Eo=({iconClass:e,iconName:t,className:n,...r})=>Qt().createElement("span",{className:[t,e,n].join(" ").trim(),role:"img","aria-hidden":"true","aria-label":" ",...r}),yo=({iconCollapse:e,iconExpand:t,iconClass:n,className:r})=>{const{activeItems:a}=(0,Wt.useContext)(io),i=(0,Wt.useContext)(lo);return Qt().createElement(Eo,{className:r,iconClass:a?.includes(i)?t:e,iconName:n})},_o=({titleCollapse:e,titleExpand:t,className:n})=>{const{activeItems:r}=(0,Wt.useContext)(io),a=(0,Wt.useContext)(lo);return Qt().createElement("div",{className:n},r?.includes(a)?e:t)},go=({children:e,id:t,className:n,"aria-controls":r,...a})=>{const{activeItems:i,toggleAccordionItems:o}=(0,Wt.useContext)(io),l=(0,Wt.useContext)(lo);return Qt().createElement("button",{"aria-expanded":!!i?.includes(l),className:["focus-visible:brui-outline-blue focus-visible:brui-outline focus-visible:brui-outline-2 focus-visible:brui-outline-offset-3 focus-visible:brui-rounded-6 brui-underline-offset-2",n].join(" ").trim(),onClick:()=>{o&&o(l)},id:t,"aria-controls":r,...a},e)},vo={gray:"brui-bg-gray-3",yellow:"brui-bg-yellow brui-border brui-border-yellow-1",solidGray:"brui-bg-gray-1",solidBlue:"brui-bg-blue",solidRed:"brui-bg-red-2",solidYellow:"brui-bg-yellow brui-border brui-border-yellow-1",solidWhite:"brui-bg-white",dropShadow:"brui-shadow-xl brui-border brui-border-gray-4",default:""},No=({variant:e,radius:t,children:n,className:r,defaultPadding:a=!1,...i})=>Qt().createElement("div",{className:[vo[e],t?"brui-rounded-10":"",a?"brui-p-30":"",r].join(" ").trim(),...i},n),Ao=({children:e,className:t})=>Qt().createElement("span",{className:["brui-sr-only",t].join(" ").trim()},e),Co={error:{alertCss:"",iconCss:"brui-text-red",srText:"Error",iconName:"bi_exclamation_c",cardVariant:"default"},info:{alertCss:"brui-border",iconCss:"brui-text-blue",iconName:"bi_big_info_c",srText:"Information",cardVariant:"default"},success:{alertCss:"brui-border",iconCss:"brui-text-green",iconName:"bi_checkmark_c",srText:"Success",cardVariant:"default"},warning:{alertCss:"",iconCss:"brui-text-yellow",srText:"Warning",iconName:"bi_exclamation_c",cardVariant:"default"}},ho=({variant:e,children:t,className:n,iconSize:r="36",...a})=>Qt().createElement(No,{className:[Co[e].alertCss,n].join(" ").trim(),variant:Co[e].cardVariant,role:"alert",...a},Qt().createElement("div",null,Qt().createElement(Eo,{iconName:Co[e].iconName,iconClass:"bi_brui",className:Co[e].iconCss,role:"img","aria-hidden":"true",style:{fontSize:`${r}px`}}),Qt().createElement(Ao,null,Co[e].srText)),t),To={textBlue:"brui-inline-block brui-rounded-4 brui-bg-transparent brui-text-blue brui-underline brui-underline-offset-2 enabled:hover:brui-text-blue-1 enabled:hover:brui-no-underline focus:brui-text-blue-1 focus:brui-no-underline focus:brui-outline-blue focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 !brui-p-0 disabled:brui-opacity-40",icon:"focus:!brui-outline-blue focus:!brui-outline focus:!brui-outline-2 focus:!brui-outline-offset-3",default:"",primary:"brui-inline-block brui-rounded-30 brui-bg-blue-1 brui-text-white brui-border-blue-1 brui-border-2 enabled:hover:brui-bg-blue enabled:hover:brui-border-blue focus:brui-outline-blue-2 focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 disabled:brui-bg-gray-2 disabled:brui-text-white disabled:brui-border-gray-2",secondary:"brui-inline-block brui-rounded-30 brui-bg-white brui-text-blue-1 brui-border-blue-1 brui-border-2 enabled:hover:brui-bg-blue-4 focus:brui-outline-blue-2 focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 disabled:brui-bg-gray-2 disabled:brui-text-white",primaryReverse:"brui-inline-block brui-rounded-30 brui-bg-white brui-text-blue-1 brui-border-white brui-border-2 enabled:hover:brui-bg-blue-4 enabled:hover:brui-border-blue-4 focus:brui-outline-white focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 disabled:brui-text-white",secondaryReverse:"brui-inline-block brui-rounded-30 brui-bg-blue-1 brui-text-white brui-border-white brui-border-2 enabled:hover:brui-bg-blue-3 focus:brui-outline-white focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 disabled:brui-text-white"},Io={regular:"brui-text-15 brui-leading-17 brui-py-7 brui-px-30",small:"brui-text-14 brui-leading-18 brui-py-5 brui-px-14",default:""},Ro=(0,Wt.forwardRef)(function({variant:e="primary",size:t="regular",className:n,...r},a){const i=(0,Wt.useMemo)(()=>({buttonStyle:[Io[t],To[e],n].join(" ").trim()}),[e,t,n]);return Qt().createElement("button",{className:i.buttonStyle,...r,ref:a})}),xo=Ro;rn="(prefers-reduced-motion: reduce)",an=setTimeout,on=function(){},ln=Array.isArray,un=i(l,"function"),sn=i(l,"string"),cn=i(l,"undefined"),dn=Object.keys,pn="data-"+(mn="splide"),bn=Math.min,fn=Math.max,En=Math.floor,yn=Math.ceil,_n=Math.abs,gn={},vn="mounted",Nn="ready",An="move",Cn="moved",hn="click",Tn="active",In="inactive",Rn="visible",xn="hidden",Sn="refresh",On="updated",Mn="resize",Ln="resized",Dn="drag",Bn="dragging",Pn="dragged",kn="scroll",wn="scrolled",Un="destroy",Fn="arrows:mounted",Hn="arrows:updated",Yn="pagination:mounted",jn="pagination:updated",Gn="navigation:mounted",Vn="autoplay:play",zn="autoplay:playing",qn="autoplay:pause",Kn="lazyload:loaded",Wn="ei",er="ttb",tr={width:["height"],left:["top","right"],right:["bottom","left"],x:["y"],X:["Y"],Y:["X"],ArrowLeft:[Zn=(Xn="Arrow")+"Up",$n=Xn+"Right"],ArrowRight:[Jn=Xn+"Down",Qn=Xn+"Left"]},lr=(ar="aria-")+"selected",pr=ar+"live",br=ar+"busy",fr=ar+"atomic",Er=[nr="role",rr="tabindex","disabled",ir=ar+"controls",or=ar+"current",ur=ar+"label",sr=ar+"labelledby",cr=ar+"hidden",dr=ar+"orientation",mr=ar+"roledescription"],gr=mn,vr=(yr=mn+"__")+"track",Nr=yr+"list",hr=(Ar=yr+"slide")+"__container",Mr=yr+"progress__bar",Lr=yr+"toggle",Dr=yr+"sr",Br=(_r="is-")+"initialized",jr=[Pr=_r+"active",Ur=_r+"visible",kr=_r+"prev",wr=_r+"next",Fr=_r+"loading",Hr=_r+"focus-in",Yr=_r+"overflow"],Gr={slide:Ar,clone:Cr=Ar+"--clone",arrows:Tr=yr+"arrows",arrow:Ir=yr+"arrow",prev:Rr=Ir+"--prev",next:xr=Ir+"--next",pagination:Sr=yr+"pagination",page:Or=Sr+"__page",spinner:yr+"spinner"},Vr="touchstart mousedown",zr="touchmove mousemove",qr="touchend touchcancel mouseup click",Kr="slide",Wr="loop",Xr="fade",Qr=pn+"-interval",$r={passive:!1,capture:!0},Zr={Spacebar:" ",Right:$n,Left:Qn,Up:Zn,Down:Jn},Jr="keydown",na="["+(ea=pn+"-lazy")+"], ["+(ta=ea+"-srcset")+"]",ra=[" ","Enter"],aa=Object.freeze({__proto__:null,Media:function(e,t,n){function r(e){e&&c.destroy()}function a(e,t){var n=matchMedia(t);c.bind(n,"change",i),d.push([e,n])}function i(){var t=l.is(7),a=n.direction,i=d.reduce(function(e,t){return h(e,t[1].matches?t[0]:{})},{});T(n),o(i),n.destroy?e.destroy("completely"===n.destroy):t?(r(!0),e.mount()):a!==n.direction&&e.refresh()}function o(t,r,a){h(n,t),r&&h(Object.getPrototypeOf(n),t),!a&&l.is(1)||e.emit(On,n)}var l=e.state,u=n.breakpoints||{},s=n.reducedMotion||{},c=Q(),d=[];return{setup:function(){var e="min"===n.mediaQuery;dn(u).sort(function(t,n){return e?+t-+n:+n-+t}).forEach(function(t){a(u[t],"("+(e?"min":"max")+"-width:"+t+"px)")}),a(s,rn),i()},destroy:r,reduce:function(e){matchMedia(rn).matches&&(e?h(n,s):T(n,dn(s)))},set:o}},Direction:function(e,t,n){return{resolve:function(e,t,r){var a="rtl"!==(r=r||n.direction)||t?r===er?0:-1:1;return tr[e]&&tr[e][a]||e.replace(/width|left|right/i,function(e,t){var n=tr[e.toLowerCase()][a]||e;return t>0?n.charAt(0).toUpperCase()+n.slice(1):n})},orient:function(e){return e*("rtl"===n.direction?1:-1)}}},Elements:function(e,t,n){function a(){var e,t,r;s=l("."+vr),c=N(s,"."+Nr),G(s&&c,"A track/list element is missing."),b(x,v(c,"."+Ar+":not(."+Cr+")")),A({arrows:Tr,pagination:Sr,prev:Rr,next:xr,bar:Mr,toggle:Lr},function(e,t){T[t]=l("."+e)}),C(T,{root:_,track:s,list:c,slides:x}),t=_.id||""+(e=mn)+X(gn[e]=(gn[e]||0)+1),r=n.role,_.id=t,s.id=s.id||t+"-track",c.id=c.id||t+"-list",!L(_,nr)&&"SECTION"!==_.tagName&&r&&R(_,nr,r),R(_,mr,h.carousel),R(c,nr,"presentation"),o()}function i(e){var t=Er.concat("style");r(x),H(_,S),H(s,O),I([s,c],t),I(_,e?t:["style",mr])}function o(){H(_,S),H(s,O),S=u(gr),O=u(vr),E(_,S),E(s,O),R(_,ur,n.label),R(_,sr,n.labelledby)}function l(e){var t=U(_,e);return t&&function(e,t){if(un(e.closest))return e.closest(t);for(var n=e;n&&1===n.nodeType&&!g(n,t);)n=n.parentElement;return n}(t,"."+gr)===_?t:void 0}function u(e){return[e+"--"+n.type,e+"--"+n.direction,n.drag&&e+"--draggable",n.isNavigation&&e+"--nav",e===gr&&Pr]}var s,c,d,m=$(e),p=m.on,y=m.bind,_=e.root,h=n.i18n,T={},x=[],S=[],O=[];return C(T,{setup:a,mount:function(){p(Sn,i),p(Sn,a),p(On,o),y(document,Vr+" keydown",function(e){d="keydown"===e.type},{capture:!0}),y(_,"focusin",function(){f(_,Hr,!!d)})},destroy:i})},Slides:function(e,t,n){function a(){x.forEach(function(e,t){l(e,t,-1)})}function o(){s(function(e){e.destroy()}),r(w)}function l(t,n,r){var a=function(e,t,n,r){function a(){var a=e.splides.map(function(e){var n=e.splide.Components.Slides.getAt(t);return n?n.slide.id:""}).join(" ");R(r,ur,W(v.slideX,(O?n:t)+1)),R(r,ir,a),R(r,nr,C?"button":""),C&&I(r,mr)}function o(){s||l()}function l(){var n,a;s||(n=e.index,(a=u())!==D(r,Pr)&&(f(r,Pr,a),R(r,or,_&&a||""),m(a?Tn:In,k)),function(){var t,n=function(){if(e.is(Xr))return u();var t=B(b.Elements.track),n=B(r),a=h("left",!0),i=h("right",!0);return En(t[a])<=yn(n[a])&&En(n[i])<=yn(t[i])}(),a=!n&&(!u()||O);e.state.is([4,5])||R(r,cr,a||""),R(F(r,y.focusableNodes||""),rr,a?-1:""),C&&R(r,rr,a?-1:0),n!==D(r,Ur)&&(f(r,Ur,n),m(n?Rn:xn,k)),n||document.activeElement!==r||(t=b.Slides.getAt(e.index))&&M(t.slide)}(),f(r,kr,t===n-1),f(r,wr,t===n+1))}function u(){var r=e.index;return r===t||y.cloneStatus&&r===n}var s,c=$(e),d=c.on,m=c.emit,p=c.bind,b=e.Components,E=e.root,y=e.options,_=y.isNavigation,g=y.updateOnMove,v=y.i18n,A=y.pagination,C=y.slideFocus,h=b.Direction.resolve,T=L(r,"style"),x=L(r,ur),O=n>-1,P=N(r,"."+hr),k={index:t,slideIndex:n,slide:r,container:P,isClone:O,mount:function(){O||(r.id=E.id+"-slide"+X(t+1),R(r,nr,A?"tabpanel":"group"),R(r,mr,v.slide),R(r,ur,x||W(v.slideLabel,[t+1,e.length]))),p(r,"click",i(m,hn,k)),p(r,"keydown",i(m,"sk",k)),d([Cn,"sh",wn],l),d(Gn,a),g&&d(An,o)},destroy:function(){s=!0,c.destroy(),H(r,jr),I(r,Er),R(r,"style",T),R(r,ur,x||"")},update:l,style:function(e,t,n){S(n&&P||r,e,t)},isWithin:function(n,r){var a=_n(n-t);return O||!y.rewind&&!e.is(Wr)||(a=bn(a,e.length-a)),a<=r}};return k}(e,n,r,t);a.mount(),w.push(a),w.sort(function(e,t){return e.index-t.index})}function u(e){return e?b(function(e){return!e.isClone}):w}function s(e,t){u(t).forEach(e)}function b(e){return w.filter(un(e)?e:function(t){return sn(e)?g(t.slide,e):p(d(e),t.index)})}var v=$(e),A=v.on,C=v.emit,h=v.bind,T=t.Elements,x=T.slides,O=T.list,w=[];return{mount:function(){a(),A(Sn,o),A(Sn,a)},destroy:o,update:function(){s(function(e){e.update()})},register:l,get:u,getIn:function(e){var r=t.Controller,a=r.toIndex(e),i=r.hasFocus()?1:n.perPage;return b(function(e){return z(e.index,a,a+i-1)})},getAt:function(e){return b(e)[0]},add:function(e,t){m(e,function(e){var r,a,o,l,u;sn(e)&&(e=k(e)),c(e)&&((r=x[t])?_(e,r):y(O,e),E(e,n.classes.slide),a=e,o=i(C,Mn),l=F(a,"img"),(u=l.length)?l.forEach(function(e){h(e,"load error",function(){--u||o()})}):o())}),C(Sn)},remove:function(e){P(b(e).map(function(e){return e.slide})),C(Sn)},forEach:s,filter:b,style:function(e,t,n){s(function(r){r.style(e,t,n)})},getLength:function(e){return e?x.length:w.length},isEnough:function(){return w.length>n.perPage}}},Layout:function(e,t,n){function r(){y=n.direction===er,S(R,"maxWidth",j(n.width)),S(x,T("paddingLeft"),o(!1)),S(x,T("paddingRight"),o(!0)),a(!0)}function a(e){var t,r=B(R);(e||_.width!==r.width||_.height!==r.height)&&(S(x,"height",(t="",y&&(G(t=l(),"height or heightRatio is missing."),t="calc("+t+" - "+o(!1)+" - "+o(!0)+")"),t)),L(T("marginRight"),j(n.gap)),L("width",n.autoWidth?null:j(n.fixedWidth)||(y?"":s())),L("height",j(n.fixedHeight)||(y?n.autoHeight?null:s():l()),!0),_=r,C(Ln),g!==(g=E())&&(f(R,Yr,g),C("overflow",g)))}function o(e){var t=n.padding,r=T(e?"right":"left");return t&&j(t[r]||(u(t)?0:t))||"0px"}function l(){return j(n.height||B(O).width*n.heightRatio)}function s(){var e=j(n.gap);return"calc((100%"+(e&&" + "+e)+")/"+(n.perPage||1)+(e&&" - "+e)+")"}function c(){return B(O)[T("width")]}function d(e,t){var n=M(e||0);return n?B(n.slide)[T("width")]+(t?0:b()):0}function m(e,t){var n,r,a=M(e);return a?(n=B(a.slide)[T("right")],r=B(O)[T("left")],_n(n-r)+(t?0:b())):0}function p(t){return m(e.length-1)-m(0)+d(0,t)}function b(){var e=M(0);return e&&parseFloat(S(e.slide,T("marginRight")))||0}function E(){return e.is(Xr)||p(!0)>c()}var y,_,g,v=$(e),N=v.on,A=v.bind,C=v.emit,h=t.Slides,T=t.Direction.resolve,I=t.Elements,R=I.root,x=I.track,O=I.list,M=h.getAt,L=h.style;return{mount:function(){var e,t;r(),A(window,"resize load",(e=i(C,Mn),t=Z(0,e,null,1),function(){t.isPaused()&&t.start()})),N([On,Sn],r),N(Mn,a)},resize:a,listSize:c,slideSize:d,sliderSize:p,totalSize:m,getPadding:function(e){return parseFloat(S(x,T("padding"+(e?"Right":"Left"))))||0},isOverflow:E}},Clones:function(e,t,n){function a(){d(Sn,i),d([On,Mn],l),(s=u())&&(function(t){var r=p.get().slice(),a=r.length;if(a){for(;r.length<t;)b(r,r);b(r.slice(-t),r.slice(0,t)).forEach(function(i,o){var l=o<t,u=function(t,r){var a=t.cloneNode(!0);return E(a,n.classes.clone),a.id=e.root.id+"-clone"+X(r+1),a}(i.slide,o);l?_(u,r[0].slide):y(m.list,u),b(g,u),p.register(u,o-t+(l?0:a),i.index)})}}(s),t.Layout.resize(!0))}function i(){o(),a()}function o(){P(g),r(g),c.destroy()}function l(){var e=u();s!==e&&(s<e||!e)&&c.emit(Sn)}function u(){var r,a=n.clones;return e.is(Wr)?cn(a)&&(a=(r=n[f("fixedWidth")]&&t.Layout.slideSize(0))&&yn(B(m.track)[f("width")]/r)||n[f("autoWidth")]&&e.length||2*n.perPage):a=0,a}var s,c=$(e),d=c.on,m=t.Elements,p=t.Slides,f=t.Direction.resolve,g=[];return{mount:a,destroy:o}},Move:function(e,t,n){function r(){t.Controller.isBusy()||(t.Scroll.cancel(),a(e.index),t.Slides.update())}function a(e){i(s(e,!0))}function i(n,r){if(!e.is(Xr)){var a=r?n:function(n){if(e.is(Wr)){var r=u(n),a=r>t.Controller.getEnd();(r<0||a)&&(n=o(n,a))}return n}(n);S(R,"transform","translate"+h("X")+"("+a+"px)"),n!==a&&f("sh")}}function o(e,t){var n=e-d(t),r=A();return e-T(r*(yn(_n(n)/r)||1))*(t?1:-1)}function l(){i(c(),!0),m.cancel()}function u(e){var n,r,a,i,o,l;for(n=t.Slides.get(),r=0,a=1/0,i=0;i<n.length&&(o=n[i].index,(l=_n(s(o,!0)-e))<=a);i++)a=l,r=o;return r}function s(t,r){var a=T(v(t-1)-function(e){var t=n.focus;return"center"===t?(N()-_(e,!0))/2:+t*_(e)||0}(t));return r?function(t){return n.trimSpace&&e.is(Kr)&&(t=q(t,0,T(A(!0)-N()))),t}(a):a}function c(){var e=h("left");return B(R)[e]-B(x)[e]+T(g(!1))}function d(e){return s(e?t.Controller.getEnd():0,!!n.trimSpace)}var m,p=$(e),b=p.on,f=p.emit,E=e.state.set,y=t.Layout,_=y.slideSize,g=y.getPadding,v=y.totalSize,N=y.listSize,A=y.sliderSize,C=t.Direction,h=C.resolve,T=C.orient,I=t.Elements,R=I.list,x=I.track;return{mount:function(){m=t.Transition,b([vn,Ln,On,Sn],r)},move:function(e,t,n,r){var a,u;e!==t&&(a=e>n,u=T(o(c(),a)),a?u>=0:u<=R[h("scrollWidth")]-B(x)[h("width")])&&(l(),i(o(c(),e>n),!0)),E(4),f(An,t,n,e),m.start(t,function(){E(3),f(Cn,t,n,e),r&&r()})},jump:a,translate:i,shift:o,cancel:l,toIndex:u,toPosition:s,getPosition:c,getLimit:d,exceededLimit:function(e,t){t=cn(t)?c():t;var n=!0!==e&&T(t)<T(d(!1)),r=!1!==e&&T(t)>T(d(!0));return n||r},reposition:r}},Controller:function(e,t,n){function r(){E=x(!0),y=n.perMove,_=n.perPage,f=s();var e=q(B,0,S?f:E-1);e!==B&&(B=e,A.reposition())}function a(){f!==s()&&N(Wn)}function o(e,t){var n=y||(p()?1:_),r=l(B+n*(e?-1:1),B,!(y||p()));return-1===r&&M&&!V(C(),h(!e),1)?e?0:f:t?r:u(r)}function l(t,r,a){if(R()||p()){var i=function(t){if(M&&"move"===n.trimSpace&&t!==B)for(var r=C();r===T(t,!0)&&z(t,0,e.length-1,!n.rewind);)t<B?--t:++t;return t}(t);i!==t&&(r=t,t=i,a=!1),t<0||t>f?t=y||!z(0,t,r,!0)&&!z(f,r,t,!0)?O?a?t<0?-(E%_||_):E:t:n.rewind?t<0?f:0:-1:c(d(t)):a&&t!==r&&(t=c(d(r)+(t<r?-1:1)))}else t=-1;return t}function u(e){return O?(e+E)%E||0:e}function s(){for(var e=E-(p()||O&&y?1:_);S&&e-- >0;)if(T(E-1,!0)!==T(e,!0)){e++;break}return q(e,0,E-1)}function c(e){return q(p()?e:_*e,0,f)}function d(e){return p()?bn(e,f):En((e>=f?E-1:e)/_)}function m(e){e!==B&&(P=B,B=e)}function p(){return!cn(n.focus)||n.isNavigation}function b(){return e.state.is([4,5])&&!!n.waitForTransition}var f,E,y,_,g=$(e),v=g.on,N=g.emit,A=t.Move,C=A.getPosition,h=A.getLimit,T=A.toPosition,I=t.Slides,R=I.isEnough,x=I.getLength,S=n.omitEnd,O=e.is(Wr),M=e.is(Kr),L=i(o,!1),D=i(o,!0),B=n.start||0,P=B;return{mount:function(){r(),v([On,Sn,Wn],r),v(Ln,a)},go:function(e,t,n){if(!b()){var r=function(e){var t,n,r,a=B;return sn(e)?(n=(t=e.match(/([+\-<>])(\d+)?/)||[])[1],r=t[2],"+"===n||"-"===n?a=l(B+ +(""+n+(+r||1)),B):">"===n?a=r?c(+r):L(!0):"<"===n&&(a=D(!0))):a=O?e:q(e,0,f),a}(e),a=u(r);a>-1&&(t||a!==B)&&(m(a),A.move(r,a,P,n))}},scroll:function(e,n,r,a){t.Scroll.scroll(e,n,r,function(){var e=u(A.toIndex(C()));m(S?bn(e,f):e),a&&a()})},getNext:L,getPrev:D,getAdjacent:o,getEnd:s,setIndex:m,getIndex:function(e){return e?P:B},toIndex:c,toPage:d,toDest:function(e){var t=A.toIndex(e);return M?q(t,0,f):t},hasFocus:p,isBusy:b}},Arrows:function(e,t,n){function r(){var e;!(e=n.arrows)||M&&L||(S=h||x("div",g.arrows),M=u(!0),L=u(!1),c=!0,y(S,[M,L]),!h&&_(S,T)),M&&L&&(C(D,{prev:M,next:L}),O(S,e?"":"none"),E(S,d=Tr+"--"+n.direction),e&&(p([vn,Cn,Sn,wn,Wn],s),b(L,"click",i(l,">")),b(M,"click",i(l,"<")),s(),R([M,L],ir,T.id),f(Fn,M,L))),p(On,a)}function a(){o(),r()}function o(){m.destroy(),H(S,d),c?(P(h?[M,L]:S),M=L=null):I([M,L],Er)}function l(e){A.go(e,!0)}function u(e){return k('<button class="'+g.arrow+" "+(e?g.prev:g.next)+'" type="button"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40" width="40" height="40" focusable="false"><path d="'+(n.arrowPath||"m15.5 0.932-4.3 4.38 14.5 14.6-14.5 14.5 4.3 4.4 14.6-14.6 4.4-4.3-4.4-4.4-14.6-14.6z")+'" />')}function s(){if(M&&L){var t=e.index,n=A.getPrev(),r=A.getNext(),a=n>-1&&t<n?v.last:v.prev,i=r>-1&&t>r?v.first:v.next;M.disabled=n<0,L.disabled=r<0,R(M,ur,a),R(L,ur,i),f(Hn,M,L,n,r)}}var c,d,m=$(e),p=m.on,b=m.bind,f=m.emit,g=n.classes,v=n.i18n,N=t.Elements,A=t.Controller,h=N.arrows,T=N.track,S=h,M=N.prev,L=N.next,D={};return{arrows:D,mount:r,destroy:o,update:s}},Autoplay:function(e,t,n){function r(){E()&&t.Slides.isEnough()&&(b.start(!n.resetProgress),s=u=A=!1,o(),p(Vn))}function a(e){void 0===e&&(e=!0),A=!!e,o(),E()||(b.pause(),p(qn))}function i(){A||(u||s?a(!1):r())}function o(){v&&(f(v,Pr,!A),R(v,ur,n.i18n[A?"play":"pause"]))}function l(e){var r=t.Slides.getAt(e);b.set(r&&+L(r.slide,Qr)||n.interval)}var u,s,c=$(e),d=c.on,m=c.bind,p=c.emit,b=Z(n.interval,e.go.bind(e,">"),function(e){var t=y.bar;t&&S(t,"width",100*e+"%"),p(zn,e)}),E=b.isPaused,y=t.Elements,_=t.Elements,g=_.root,v=_.toggle,N=n.autoplay,A="pause"===N;return{mount:function(){N&&(n.pauseOnHover&&m(g,"mouseenter mouseleave",function(e){u="mouseenter"===e.type,i()}),n.pauseOnFocus&&m(g,"focusin focusout",function(e){s="focusin"===e.type,i()}),v&&m(v,"click",function(){A?r():a(!0)}),d([An,kn,Sn],b.rewind),d(An,l),v&&R(v,ir,y.track.id),A||r(),o())},destroy:b.cancel,play:r,pause:a,isPaused:E}},Cover:function(e,t,n){function r(e){t.Slides.forEach(function(t){var n=N(t.container||t.slide,"img");n&&n.src&&a(e,n,t)})}function a(e,t,n){n.style("background",e?'center/cover no-repeat url("'+t.src+'")':"",!0),O(t,e?"none":"")}var o=$(e).on;return{mount:function(){n.cover&&(o(Kn,i(a,!0)),o([vn,On,Sn],i(r,!0)))},destroy:i(r,!1)}},Scroll:function(e,t,n){function r(e,n,r,u,d){var m,y,g,A=E();l(),!r||v&&_()||(m=t.Layout.sliderSize(),y=K(e)*m*En(_n(e)/m)||0,e=f.toPosition(t.Controller.toDest(e%m))+y),g=V(A,e,1),N=1,n=g?0:n||fn(_n(e-A)/1.5,800),c=u,s=Z(n,a,i(o,A,e,d),1),b(5),p(kn),s.start()}function a(){b(3),c&&c(),p(wn)}function o(e,t,a,i){var o,l,u=E(),s=(e+(t-e)*(o=i,(l=n.easingFunc)?l(o):1-Math.pow(1-o,4))-u)*N;g(u+s),v&&!a&&_()&&(N*=.6,_n(s)<10&&r(y(_(!0)),600,!1,c,!0))}function l(){s&&s.cancel()}function u(){s&&!s.isPaused()&&(l(),a())}var s,c,d=$(e),m=d.on,p=d.emit,b=e.state.set,f=t.Move,E=f.getPosition,y=f.getLimit,_=f.exceededLimit,g=f.translate,v=e.is(Kr),N=1;return{mount:function(){m(An,l),m([On,Sn],u)},destroy:l,scroll:r,cancel:u}},Drag:function(e,t,n){function r(){var e=n.drag;f(!e),v="free"===e}function a(e){var t,r,a;A=!1,C||(t=b(e),r=e.target,a=n.noDrag,g(r,"."+Or+", ."+Ir)||a&&g(r,a)||!t&&e.button||(D.isBusy()?w(e,!0):(h=t?B:window,N=O.is([4,5]),_=null,x(h,zr,i,$r),x(h,qr,o,$r),M.cancel(),L.cancel(),s(e))))}function i(t){if(O.is(6)||(O.set(6),R(Dn)),t.cancelable)if(N){M.translate(E+c(t)/(G&&e.is(Kr)?5:1));var r=d(t)>200,a=G!==(G=j());(r||a)&&s(t),A=!0,R(Bn),w(t)}else(function(e){return _n(c(e))>_n(c(e,!0))})(t)&&(N=function(e){var t=n.dragMinThreshold,r=u(t),a=r&&t.mouse||0,i=(r?t.touch:+t)||10;return _n(c(e))>(b(e)?i:a)}(t),w(t))}function o(r){O.is(6)&&(O.set(3),R(Pn)),N&&(function(r){var a=function(t){if(e.is(Wr)||!G){var n=d(t);if(n&&n<200)return c(t)/n}return 0}(r),i=function(e){return H()+K(e)*bn(_n(e)*(n.flickPower||600),v?1/0:t.Layout.listSize()*(n.flickMaxPages||1))}(a),o=n.rewind&&n.rewindByDrag;P(!1),v?D.scroll(i,0,n.snap):e.is(Xr)?D.go(F(K(a))<0?o?"<":"-":o?">":"+"):e.is(Kr)&&G&&o?D.go(j(!0)?">":"<"):D.go(D.toDest(i),!0),P(!0)}(r),w(r)),S(h,zr,i),S(h,qr,o),N=!1}function l(e){!C&&A&&w(e,!0)}function s(e){_=y,y=e,E=H()}function c(e,t){return p(e,t)-p(m(e),t)}function d(e){return Y(e)-Y(m(e))}function m(e){return y===e&&_||y}function p(e,t){return(b(e)?e.changedTouches[0]:e)["page"+U(t?"Y":"X")]}function b(e){return"undefined"!=typeof TouchEvent&&e instanceof TouchEvent}function f(e){C=e}var E,y,_,v,N,A,C,h,T=$(e),I=T.on,R=T.emit,x=T.bind,S=T.unbind,O=e.state,M=t.Move,L=t.Scroll,D=t.Controller,B=t.Elements.track,P=t.Media.reduce,k=t.Direction,U=k.resolve,F=k.orient,H=M.getPosition,j=M.exceededLimit,G=!1;return{mount:function(){x(B,zr,on,$r),x(B,qr,on,$r),x(B,Vr,a,$r),x(B,"click",l,{capture:!0}),x(B,"dragstart",w),I([vn,On],r)},disable:f,isDragging:function(){return N}}},Keyboard:function(e,t,n){function r(){var e=n.keyboard;e&&(l="global"===e?window:p,d(l,Jr,o))}function a(){m(l,Jr)}function i(){var e=u;u=!0,an(function(){u=e})}function o(t){if(!u){var n=J(t);n===b(Qn)?e.go("<"):n===b($n)&&e.go(">")}}var l,u,s=$(e),c=s.on,d=s.bind,m=s.unbind,p=e.root,b=t.Direction.resolve;return{mount:function(){r(),c(On,a),c(On,r),c(An,i)},destroy:a,disable:function(e){u=e}}},LazyLoad:function(e,t,n){function a(){r(_),t.Slides.forEach(function(e){F(e.slide,na).forEach(function(t){var r,a,i,o=L(t,ea),l=L(t,ta);o===t.src&&l===t.srcset||(r=n.classes.spinner,i=N(a=t.parentElement,"."+r)||x("span",r,a),_.push([t,e,i]),t.src||O(t,"none"))})}),f?s():(m(y),d(y,o),o())}function o(){(_=_.filter(function(t){var r=n.perPage*((n.preloadPages||1)+1)-1;return!t[1].isWithin(e.index,r)||l(t)})).length||m(y)}function l(e){var t=e[0];E(e[1].slide,Fr),p(t,"load error",i(u,e)),R(t,"src",L(t,ea)),R(t,"srcset",L(t,ta)),I(t,ea),I(t,ta)}function u(e,t){var n=e[0],r=e[1];H(r.slide,Fr),"error"!==t.type&&(P(e[2]),O(n,""),b(Kn,n,r),b(Mn)),f&&s()}function s(){_.length&&l(_.shift())}var c=$(e),d=c.on,m=c.off,p=c.bind,b=c.emit,f="sequential"===n.lazyLoad,y=[Cn,wn],_=[];return{mount:function(){n.lazyLoad&&(a(),d(Sn,a))},destroy:i(r,_),check:o}},Pagination:function(e,t,n){function o(){m&&(P(S?a(m.children):m),H(m,p),r(L),m=null),b.destroy()}function l(e){h(">"+e,!0)}function u(e,t){var n,r=L.length,a=J(t),i=s(),o=-1;a===T($n,!1,i)?o=++e%r:a===T(Qn,!1,i)?o=(--e+r)%r:"Home"===a?o=0:"End"===a&&(o=r-1),(n=L[o])&&(M(n.button),h(">"+o),w(t,!0))}function s(){return n.paginationDirection||n.direction}function c(e){return L[N.toPage(e)]}function d(){var e,t,n=c(C(!0)),r=c(C());n&&(H(e=n.button,Pr),I(e,lr),R(e,rr,-1)),r&&(E(t=r.button,Pr),R(t,lr,!0),R(t,rr,"")),y(jn,{list:m,items:L},n,r)}var m,p,b=$(e),f=b.on,y=b.emit,_=b.bind,g=t.Slides,v=t.Elements,N=t.Controller,A=N.hasFocus,C=N.getIndex,h=N.go,T=t.Direction.resolve,S=v.pagination,L=[];return{items:L,mount:function t(){o(),f([On,Sn,Wn],t);var r=n.pagination;S&&O(S,r?"":"none"),r&&(f([An,kn,wn],d),function(){var t,r,a,o,c,d=e.length,b=n.classes,f=n.i18n,y=n.perPage,C=A()?N.getEnd()+1:yn(d/y);for(E(m=S||x("ul",b.pagination,v.track.parentElement),p=Sr+"--"+s()),R(m,nr,"tablist"),R(m,ur,f.select),R(m,dr,s()===er?"vertical":""),t=0;t<C;t++)r=x("li",null,m),a=x("button",{class:b.page,type:"button"},r),o=g.getIn(t).map(function(e){return e.slide.id}),c=!A()&&y>1?f.pageX:f.slideX,_(a,"click",i(l,t)),n.paginationKeyboard&&_(a,"keydown",i(u,t)),R(r,nr,"presentation"),R(a,nr,"tab"),R(a,ir,o.join(" ")),R(a,ur,W(c,t+1)),R(a,rr,-1),L.push({li:r,button:a,page:t})}(),d(),y(Yn,{list:m,items:L},c(e.index)))},destroy:o,getAt:c,update:d}},Sync:function(e,t,n){function a(){var t,n;e.splides.forEach(function(t){t.isParent||(l(e,t.splide),l(t.splide,e))}),d&&((n=(t=$(e)).on)(hn,s),n("sk",c),n([vn,On],u),b.push(t),t.emit(Gn,e.splides))}function o(){b.forEach(function(e){e.destroy()}),r(b)}function l(e,t){var n=$(e);n.on(An,function(e,n,r){t.go(t.is(Wr)?r:e)}),b.push(n)}function u(){R(t.Elements.list,dr,n.direction===er?"vertical":"")}function s(t){e.go(t.index)}function c(e,t){p(ra,J(t))&&(s(e),w(t))}var d=n.isNavigation,m=n.slideFocus,b=[];return{setup:i(t.Media.set,{slideFocus:cn(m)?d:m},!0),mount:a,destroy:o,remount:function(){o(),a()}}},Wheel:function(e,t,n){function r(r){if(r.cancelable){var a=r.deltaY,o=a<0,l=Y(r),u=n.wheelMinThreshold||0,s=n.wheelSleep||0;_n(a)>u&&l-i>s&&(e.go(o?"<":">"),i=l),function(r){return!n.releaseWheel||e.state.is(4)||-1!==t.Controller.getAdjacent(r)}(o)&&w(r)}}var a=$(e).bind,i=0;return{mount:function(){n.wheel&&a(t.Elements.track,"wheel",r,$r)}}},Live:function(e,t,n){function r(e){R(l,br,e),e?(y(l,s),c.start()):(P(s),c.cancel())}function a(e){u&&R(l,pr,e?"off":"polite")}var o=$(e).on,l=t.Elements.track,u=n.live&&!n.isNavigation,s=x("span",Dr),c=Z(90,i(r,!1));return{mount:function(){u&&(a(!t.Autoplay.isPaused()),R(l,fr,!0),s.textContent="…",o(Vn,i(a,!0)),o(qn,i(a,!1)),o([Cn,wn],i(r,!0)))},disable:a,destroy:function(){I(l,[pr,fr,br]),P(s)}}}}),ia={type:"slide",role:"region",speed:400,perPage:1,cloneStatus:!0,arrows:!0,pagination:!0,paginationKeyboard:!0,interval:5e3,pauseOnHover:!0,pauseOnFocus:!0,resetProgress:!0,easing:"cubic-bezier(0.25, 1, 0.5, 1)",drag:!0,direction:"ltr",trimSpace:!0,focusableNodes:"a, button, textarea, input, select, iframe",live:!0,classes:Gr,i18n:{prev:"Previous slide",next:"Next slide",first:"Go to first slide",last:"Go to last slide",slideX:"Go to slide %s",pageX:"Go to page %s",play:"Start autoplay",pause:"Pause autoplay",carousel:"carousel",slide:"slide",select:"Select a slide to show",slideLabel:"%s of %s"},reducedMotion:{speed:0,rewindSpeed:0,autoplay:"pause"}},oa=function(){function e(t,n){var r,a;this.event=$(),this.Components={},this.state=(r=1,{set:function(e){r=e},is:function(e){return p(d(e),r)}}),this.splides=[],this._o={},this._E={},G(a=sn(t)?U(document,t):t,a+" is invalid."),this.root=a,n=h({label:L(a,ur)||"",labelledby:L(a,sr)||""},ia,e.defaults,n||{});try{h(n,JSON.parse(L(a,pn)))}catch(e){G(!1,"Invalid JSON")}this._o=Object.create(h({},n))}var t,n,i=e.prototype;return i.mount=function(e,t){var n=this,r=this.state,a=this.Components;return G(r.is([1,7]),"Already mounted!"),r.set(1),this._C=a,this._T=t||this._T||(this.is(Xr)?ee:te),this._E=e||this._E,A(C({},aa,this._E,{Transition:this._T}),function(e,t){var r=e(n,a,n._o);a[t]=r,r.setup&&r.setup()}),A(a,function(e){e.mount&&e.mount()}),this.emit(vn),E(this.root,Br),r.set(3),this.emit(Nn),this},i.sync=function(e){return this.splides.push({splide:e}),e.splides.push({splide:this,isParent:!0}),this.state.is(3)&&(this._C.Sync.remount(),e.Components.Sync.remount()),this},i.go=function(e){return this._C.Controller.go(e),this},i.on=function(e,t){return this.event.on(e,t),this},i.off=function(e){return this.event.off(e),this},i.emit=function(e){var t;return(t=this.event).emit.apply(t,[e].concat(a(arguments,1))),this},i.add=function(e,t){return this._C.Slides.add(e,t),this},i.remove=function(e){return this._C.Slides.remove(e),this},i.is=function(e){return this._o.type===e},i.refresh=function(){return this.emit(Sn),this},i.destroy=function(e){void 0===e&&(e=!0);var t=this.event,n=this.state;return n.is(1)?$(this).on(Nn,this.destroy.bind(this,e)):(A(this._C,function(t){t.destroy&&t.destroy(e)},!0),t.emit(Un),t.destroy(),e&&r(this.splides),n.set(7)),this},t=e,(n=[{key:"options",get:function(){return this._o},set:function(e){this._C.Media.set(e,!0,!0)}},{key:"length",get:function(){return this._C.Slides.getLength(!0)}},{key:"index",get:function(){return this._C.Controller.getIndex()}}])&&function(e,t){var n,r;for(n=0;n<t.length;n++)(r=t[n]).enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),(la=oa).defaults={},la.STATES={CREATED:1,MOUNTED:2,IDLE:3,MOVING:4,SCROLLING:5,DRAGGING:6,DESTROYED:7},ua=[[vn,"onMounted"],[Nn,"onReady"],[An,"onMove"],[Cn,"onMoved"],[hn,"onClick"],[Tn,"onActive"],[In,"onInactive"],[Rn,"onVisible"],[xn,"onHidden"],[Sn,"onRefresh"],[On,"onUpdated"],[Mn,"onResize"],[Ln,"onResized"],[Dn,"onDrag"],[Bn,"onDragging"],[Pn,"onDragged"],[kn,"onScroll"],[wn,"onScrolled"],[Un,"onDestroy"],[Fn,"onArrowsMounted"],[Hn,"onArrowsUpdated"],[Yn,"onPaginationMounted"],[jn,"onPaginationUpdated"],[Gn,"onNavigationMounted"],[Vn,"onAutoplayPlay"],[zn,"onAutoplayPlaying"],[qn,"onAutoplayPause"],[Kn,"onLazyLoadLoaded"]],sa=({children:e,className:t,...n})=>Qt().createElement("div",{className:ne("splide__track",t),...n},Qt().createElement("ul",{className:"splide__list"},e)),ca=class extends Qt().Component{constructor(){super(...arguments),this.splideRef=Qt().createRef(),this.slides=[]}componentDidMount(){const{options:e,extensions:t,transition:n}=this.props,{current:r}=this.splideRef;r&&(this.splide=new la(r,e),this.bind(this.splide),this.splide.mount(t,n),this.options=ie({},e||{}),this.slides=this.getSlides())}componentWillUnmount(){this.splide&&(this.splide.destroy(),this.splide=void 0),this.options=void 0,this.slides.length=0}componentDidUpdate(){if(!this.splide)return;const{options:e}=this.props;e&&!ae(this.options,e)&&(this.splide.options=e,this.options=ie({},e));const t=this.getSlides();var n,r;n=this.slides,r=t,(n.length!==r.length||n.some((e,t)=>e!==r[t]))&&(this.splide.refresh(),this.slides=t)}sync(e){var t;null==(t=this.splide)||t.sync(e)}go(e){var t;null==(t=this.splide)||t.go(e)}getSlides(){var e;if(this.splide){const t=null==(e=this.splide.Components.Elements)?void 0:e.list.children;return t&&Array.prototype.slice.call(t)||[]}return[]}bind(e){ua.forEach(([t,n])=>{const r=this.props[n];"function"==typeof r&&e.on(t,(...t)=>{r(e,...t)})})}omit(e,t){return t.forEach(t=>{Object.prototype.hasOwnProperty.call(e,t)&&delete e[t]}),e}render(){const{className:e,tag:t="div",hasTrack:n=!0,children:r,...a}=this.props;return Qt().createElement(t,{className:ne("splide",e),ref:this.splideRef,...this.omit(a,["options",...ua.map(e=>e[1])])},n?Qt().createElement(sa,null,r):r)}};const So=(0,Wt.createContext)({trackMarginStyle:{marginLeft:"0px",marginRight:"0px"},trackPaddingStyle:{paddingLeft:"0px",paddingRight:"0px"}}),Oo=({children:e,config:t,id:n,"aria-label":r,"aria-labelledby":a,slideRole:i,slideLabel:o="%s of %s",paginationLabel:l="",paginationRole:u,paginationButtonLabel:s,paginationButtonCurrent:c,hasGradientEffect:d={desktop:!0}})=>{const{mobile:m,tablet:p,desktop:b}=t,f=(0,Wt.useRef)(null),{width:E}=co(),[y,_]=(0,Wt.useState)({}),[g,v]=(0,Wt.useState)({marginLeft:"0px",marginRight:"0px"}),[N,A]=(0,Wt.useState)({paddingLeft:"0px",paddingRight:"0px"}),C=m?.isActive??!0,h=p?.isActive??!0,T=b?.isActive??!0;(0,Wt.useEffect)(()=>{const{splide:e}=f.current||{},t=f.current?.splide?.index,n=f.current?.splide?.length||0,r="after:brui-absolute after:brui-h-full after:!brui-w-[90px] after:brui-top-0 after:-brui-right-16 sm:after:-brui-right-30 md:after:-brui-right-16 brui-carousel-right-gradient before:brui-absolute before:!brui-h-full before:!brui-top-0 before:-brui-left-16 sm:before:-brui-left-30 md:before:-brui-left-16 brui-carousel-left-gradient before:brui-z-1".split(" ");e&&(window.innerWidth<768?(_(0===t?{left:"16px",right:"32px"}:t===n-1?{left:"32px",right:"16px"}:{left:"24px",right:"24px"}),v({marginLeft:"-16px",marginRight:"-16px"}),d?.mobile?r.map(t=>{e.root?.classList.add(t)}):r.map(t=>{e.root?.classList.remove(t)})):window.innerWidth>=768&&window.innerWidth<992?(_(0===t?{left:"32px",right:"42px"}:t===n-2?{left:"42px",right:"32px"}:{left:"32px",right:"32px"}),v({marginLeft:"-32px",marginRight:"-32px"}),d?.tablet?r.map(t=>{e.root?.classList.add(t)}):r.map(t=>{e.root?.classList.remove(t)})):(_({left:"0px",right:"0px"}),v({marginLeft:"0px",marginRight:"0px"}),d?.desktop?r.map(t=>{e.root?.classList.add(t)}):r.map(t=>{e.root?.classList.remove(t)})))},[E]),(0,Wt.useEffect)(()=>{const e=f.current?.splide;return e&&(I(),e.on("move",()=>{I(),setTimeout(()=>{R()},1)})),()=>{e&&e.off("move")}},[E]),(0,Wt.useEffect)(()=>{const e=f.current?.splide;if(e&&void 0!==i&&""!==i&&(()=>{const t=e.Components?.Elements?.slides;t&&t.forEach(e=>{e.setAttribute("role",i)})})(),e){const t=()=>{if(null!=u&&""!==u){const t=e.Components?.Elements?.pagination;t?.setAttribute("role",u)}},n=()=>{t(),R()};e.on("pagination:mounted",n)}return()=>{e&&e.off("pagination:mounted")}},[E,f]);const I=()=>{const e=f.current?.splide?.index,t=f.current?.splide?.length||0;window.innerWidth<768&&C?A(0===e?{paddingLeft:"16px",paddingRight:"32px"}:e===t-1?{paddingLeft:"32px",paddingRight:"16px"}:{paddingLeft:"24px",paddingRight:"24px"}):window.innerWidth>=768&&window.innerWidth<992&&h?A(0===e?{paddingLeft:"32px",paddingRight:"42px"}:e===t-2?{paddingLeft:"42px",paddingRight:"32px"}:{paddingLeft:"32px",paddingRight:"32px"}):window.innerWidth>=992&&T&&A({paddingLeft:"0px",paddingRight:"0px"})},R=()=>{const e=f.current?.splide;if(e){const t=e.Components?.Elements?.pagination,n=t?.querySelectorAll(".splide__pagination__page");n&&n.forEach((e,t)=>{if(e.removeAttribute("role"),e.removeAttribute("tabindex"),e.removeAttribute("aria-controls"),e.removeAttribute("aria-selected"),e.classList.contains("is-active")&&c&&s){const r=s.replace("{current}",`${t+1}`).replace("{total}",`${n.length}`);e.setAttribute("aria-label",`${r} ${c}`)}else s&&e.setAttribute("aria-label",s.replace("{current}",`${t+1}`).replace("{total}",`${n.length}`))})}};return Qt().createElement(So.Provider,{value:{trackMarginStyle:g,trackPaddingStyle:N}},Qt().createElement(ca,{id:`${n}-parent`,ref:f,className:"brui-h-full","aria-label":r,"aria-labelledby":a,hasTrack:!1,options:{i18n:{slideLabel:o,select:l},padding:y,mediaQuery:"min",destroy:!1,focusableNodes:"button, input",paginationKeyboard:!1,classes:{arrows:"splide__arrows",arrow:"splide__arrow",prev:"splide__arrow--prev !brui-h-40 !brui-w-40 !brui-bg-white !brui-border-solid !brui-border-1 !brui-border-gray-8 !brui-text-blue !brui-border-rounded-60 hover:!brui-border-blue-1 !-brui-left-10 focus:!brui-outline-blue focus:!brui-outline focus:!brui-outline-2 focus:!brui-outline-offset-3 !brui-opacity-100 brui-shadow-6sm bi_brui bi_chevron_left",next:"splide__arrow--next !brui-h-40 !brui-w-40 !brui-bg-white !brui-border-solid !brui-border-1 !brui-border-gray-8 !brui-text-blue !brui-border-rounded-60 hover:!brui-border-blue-1 !-brui-right-10 focus:!brui-outline-blue focus:!brui-outline focus:!brui-outline-2 focus:!brui-outline-offset-3 !brui-opacity-100 brui-shadow-6sm bi_brui bi_chevron",pagination:"splide__pagination",page:"splide__pagination__page !brui-bg-white !brui-block !brui-border-solid !brui-border-2 !brui-border-gray-10 focus:!brui-outline-blue focus:!brui-outline focus:!brui-outline-2 focus:!brui-outline-offset-3 !brui-opacity-100"},breakpoints:{320:{perPage:m?.perPage,perMove:m?.perMove,arrows:m?.arrows,gap:m?.gap,destroy:!C},768:{perPage:p?.perPage,perMove:p?.perMove,arrows:p?.arrows,gap:p?.gap,destroy:!h},992:{perPage:b?.perPage,perMove:b?.perMove,arrows:b?.arrows,gap:b?.gap,destroy:!T}}},onMoved:()=>{d&&(()=>{const{splide:e}=f.current||{},t=f.current?.splide?.index||0,n=f.current?.splide?.length||1,r=f.current?.splide?.options.perPage||1;e&&(e.root.classList.add("before:!brui-w-[90px]","after:!brui-w-[90px]"),0===t&&e.root.classList.remove("before:!brui-w-[90px]"),t+r-1>=n-1&&e.root.classList.remove("after:!brui-w-[90px]"))})()}},e))},Mo=()=>Qt().createElement("div",{className:"splide__arrows"}),Lo=({children:e,className:t})=>{const{trackMarginStyle:n,trackPaddingStyle:r}=(0,Wt.useContext)(So),a={...n,...r};return Qt().createElement("div",{className:["splide__track splide__track-custom",t].join(" ").trim(),style:a},Qt().createElement("div",{className:"splide__list"},e))},Do=({children:e})=>Qt().createElement("div",{className:"splide__slide"},e),Bo=({className:e})=>Qt().createElement("ul",{className:["splide__pagination",e].join(" ").trim()}),Po={default:"group-has-[:focus-visible]/inputcheckbox:brui-outline-blue group-has-[:focus-visible]/inputcheckbox:brui-outline group-has-[:focus-visible]/inputcheckbox:brui-outline-2 group-has-[:focus-visible]/inputcheckbox:brui-outline-offset-3",boxedInMobile:"group-has-[:focus-visible]/inputcheckbox:sm:brui-outline-blue group-has-[:focus-visible]/inputcheckbox:sm:brui-outline group-has-[:focus-visible]/inputcheckbox:sm:brui-outline-2 group-has-[:focus-visible]/inputcheckbox:sm:brui-outline-offset-3",alignMiddle:"group-has-[:focus-visible]/inputcheckbox:brui-outline-blue group-has-[:focus-visible]/inputcheckbox:brui-outline group-has-[:focus-visible]/inputcheckbox:brui-outline-2 group-has-[:focus-visible]/inputcheckbox:brui-outline-offset-3"},ko=(0,Wt.forwardRef)(function({id:e,name:t,value:n,hasError:r=!1,variant:a,...i},o){return Qt().createElement("div",{className:"brui-relative brui-group/inputcheckbox"},Qt().createElement("input",{type:"checkbox",id:e,name:t,value:n,className:"brui-absolute brui-size-full brui-opacity-0 enabled:brui-cursor-pointer disabled:brui-cursor-default brui-z-10",...i,ref:o}),Qt().createElement("div",null,Qt().createElement("div",{className:["brui-size-24 brui-rounded-2 brui-border group-has-[:disabled]/inputcheckbox:brui-bg-white group-has-[:disabled]/inputcheckbox:brui-border-gray-5 group-has-[:checked]/inputcheckbox:brui-bg-blue group-has-[:checked]/inputcheckbox:brui-border-blue group-has-[:checked]/label:brui-font-bold group-has-[:disabled]/inputcheckbox:brui-opacity-40",r?"brui-border-red":"brui-border-gray-2",Po[a]].join(" ").trim()}),Qt().createElement(Eo,{className:"group-has-[:checked]/inputcheckbox:brui-block brui-absolute brui-hidden brui-text-white group-has-[:checked:disabled]/inputcheckbox:brui-text-gray-7 brui-text-12 brui-transform brui-top-1/2 brui-left-1/2 -brui-translate-x-1/2 -brui-translate-y-1/2 group-has-[:disabled]/inputcheckbox:brui-opacity-40",iconClass:"bi_brui",iconName:"bi_arrow_chekcbox"})))}),wo=({elementType:e,children:t,className:n,role:r,...a})=>{const i=e||"span";return Qt().createElement(i,{className:n,role:r,...a},t)},Uo=({id:e,iconName:t="bi_brui",iconClass:n="bi_error_bl_bg_cf",errorMessage:r,className:a="",show:i=!0})=>i?Qt().createElement("div",{className:["brui-flex brui-items-center brui-mt-10",a].join(" ").trim()},Qt().createElement(Eo,{iconClass:n,iconName:t,className:"brui-text-16 brui-text-red brui-mr-10"}),Qt().createElement(wo,{id:e,elementType:"div",className:"brui-box-border brui-text-red brui-text-12 brui-leading-14"},r)):null,Fo=(0,Wt.forwardRef)(function({id:e,name:t,value:n,children:r,variant:a,hasError:i=!1,"aria-describedby":o,errorMessage:l,boldLabelOnCheck:u,...s},c){const d={default:{wrapper:"brui-group/label brui-relative brui-inline-flex brui-w-full brui-items-start brui-content-start brui-border-0",label:"brui-pl-12 brui-text-14 brui-leading-19"},boxedInMobile:{wrapper:i?"brui-group/label brui-relative brui-inline-flex brui-w-full brui-border-2 brui-border-red brui-items-center brui-content-center brui-rounded-6 brui-border-1 sm:brui-border-0 has-[:checked]:sm:brui-border-0 has-[:checked]:brui-border-2 has-[:checked]:brui-border-blue brui-p-15 sm:brui-p-0":"brui-group/label brui-relative brui-inline-flex brui-w-full brui-items-center brui-content-center brui-rounded-6 brui-border-1 sm:brui-border-0 has-[:checked]:sm:brui-border-0 has-[:checked]:brui-border-2 has-[:checked]:brui-border-blue brui-p-15 sm:brui-p-0 brui-border-gray-3 has-[input:focus-visible]:brui-outline has-[input:focus-visible]:brui-outline-2 has-[input:focus-visible]:brui-outline-offset-3 has-[input:focus-visible]:brui-outline-blue has-[input:focus-visible]:sm:brui-outline-0",label:"brui-pl-12 brui-text-14 brui-leading-19"},alignMiddle:{wrapper:"brui-group/label brui-relative brui-inline-flex brui-w-full brui-items-start brui-content-start brui-border-0",label:"brui-pl-12 brui-text-14 brui-leading-19 brui-self-center"}},m=l&&i?`error-${e}`:"",p=(0,Wt.useMemo)(()=>o?[o||"",m].join(" ").trim():m,[o,m]);return Qt().createElement(Qt().Fragment,null,Qt().createElement("div",{className:[d[a].wrapper].join(" ").trim()},Qt().createElement(ko,{id:e,name:t,value:n,ref:c,hasError:i,"aria-describedby":p||"",variant:a,...s}),Qt().createElement("label",{htmlFor:e,className:["brui-cursor-pointer group-has-[:disabled]/label:brui-cursor-default",d[a].label,u?"group-has-[:checked]/label:brui-font-bold":""].join(" ").trim()},r)),i&&Qt().createElement(Uo,{id:m,iconClass:"vi_warning_c",iconName:"vi_vrui",errorMessage:l||""}))}),Ho=Fo,Yo={topRight:"brui-right-24 brui-top-24",topLeft:"sm:brui-top-30 sm:brui-left-30 brui-top-40 brui-left-15",topCenter:"brui-top-30 brui-left-1/2 -brui-translate-x-1/2",default:"brui-right-24 brui-top-24"},jo=(0,Wt.forwardRef)(function({checkboxPlacement:e="default",...t},n){return Qt().createElement("div",{className:"brui-group/inputcheckbox brui-absolute brui-right-0 brui-top-0 rui-leading-0 brui-w-full brui-h-full"},Qt().createElement("div",{className:["brui-shadow-4sm group-has-[:checked]/inputcheckbox:brui-shadow-none group-has-[:disabled]/inputcheckbox:brui-shadow-none brui-absolute brui-w-full brui-h-full group-has-[:disabled]/inputcheckbox:brui-border-gray-2 group-has-[:disabled]/inputcheckbox:brui-border-1 group-has-[:checked]/inputcheckbox:brui-border-2 brui-border group-has-[:focus-visible]/inputcheckbox:brui-outline-blue group-has-[:focus-visible]/inputcheckbox:brui-outline group-has-[:focus-visible]/inputcheckbox:brui-outline-2 group-has-[:focus-visible]/inputcheckbox:brui-outline-offset-3 brui-rounded-20 transition-all",t.disabled&&(t.checked||t.defaultChecked)?"":"group-has-[:checked]/inputcheckbox:brui-border-blue"].join(" ").trim()}),Qt().createElement("input",{type:"checkbox",ref:n,...t,className:"brui-absolute brui-left-0 brui-top-0 brui-w-full brui-h-full brui-z-10 brui-opacity-0"}),Qt().createElement("div",{className:["brui-absolute",Yo[e]].join(" ").trim()},Qt().createElement("div",{className:["brui-w-24 brui-h-24 brui-rounded-2 brui-border  ",t.disabled&&(t.checked||t.defaultChecked)?"brui-border-gray-7 group-has-[:disabled]/inputcheckbox:brui-bg-white":"brui-border-gray-2 group-has-[:checked]/inputcheckbox:brui-bg-blue group-has-[:checked]/inputcheckbox:brui-border-blue"].join(" ").trim()}),Qt().createElement(Eo,{className:["group-has-[:checked]/inputcheckbox:brui-block brui-absolute brui-hidden brui-text-12 brui-transform brui-top-1/2 brui-left-1/2 -brui-translate-x-1/2 -brui-translate-y-1/2",t.disabled&&(t.checked||t.defaultChecked)?"brui-text-gray-1":"brui-text-white"].join(" ").trim(),iconClass:"bi_brui",iconName:"bi_arrow_chekcbox"})))}),Go={topRight:"brui-py-32 brui-px-24",topLeft:"brui-p-16 sm:brui-p-32",topCenter:"brui-py-30 brui-px-15 sm:brui-px-30",default:"brui-py-32 brui-px-24"},Vo=(0,Wt.forwardRef)(function({children:e,className:t,"aria-labelledby":n,"aria-describedby":r,name:a,checkboxPlacement:i="topRight",defaultPadding:o,...l},u){return Qt().createElement("div",{className:["brui-group/checkboxcard brui-rounded-20 brui-relative",o?Go[i]:"",t,l.disabled&&(l.checked||l.defaultChecked)?"brui-bg-gray-3":""].join(" ").trim()},Qt().createElement(jo,{"aria-labelledby":n,"aria-describedby":r,name:a,ref:u,checkboxPlacement:i,...l}),e)}),zo=Vo,qo=({children:e,className:t,...n})=>Qt().createElement("div",{className:["brui-mb-24",t].join(" ").trim(),...n},e),Ko=({children:e,className:t,...n})=>Qt().createElement("div",{className:["brui-mt-auto",t].join(" ").trim(),...n},e),Wo=({children:e,className:t,...n})=>Qt().createElement("div",{className:["brui-px-16 sm:brui-px-30 md:brui-px-16 lg:brui-container",t].join(" ").trim(),...n},e),Xo=({direction:e="horizontal",width:t=2,className:n,style:r,...a})=>Qt().createElement("div",{className:["brui-bg-gray-1","horizontal"===e?"brui-w-full":"brui-flex",n].join(" ").trim(),style:{..."horizontal"===e?{height:t}:{width:t},...r||{}},...a}),Qo=(0,Wt.createContext)({visibility:!1}),$o=({children:e})=>{const[t,n]=(0,Wt.useState)(!1),r=(0,Wt.useRef)(null),a=bo(0),i=()=>{if(r.current){const e=r.current.getBoundingClientRect();n(e.top>=0&&e.bottom<=window.innerHeight)}};return(0,Wt.useEffect)(()=>{i()},[a]),(0,Wt.useEffect)(()=>(window.addEventListener("scroll",i),()=>window.removeEventListener("scroll",i)),[]),Qt().createElement(Qo.Provider,{value:{visibility:!t}},Qt().createElement("div",{ref:r},e))},Zo=({children:e,className:t,...n})=>{const{visibility:r}=(0,Wt.useContext)(Qo);return Qt().createElement("div",{className:["brui-fixed brui-left-0 brui-right-0 brui-bottom-0 brui-z-50 "+(r?"":"brui-hidden"),t].join(" ").trim(),...n},e)},Jo=({children:e,className:t,...n})=>Qt().createElement("div",{className:t,...n},e),el=({children:e,...t})=>Qt().createElement("li",{...t},e),tl={simple:{divClass:"brui-mb-32 sm:brui-mb-0 brui-text-center sm:brui-text-left",navClass:"brui-mb-16 sm:brui-mb-8 brui-text-12 brui-leading-14"},default:{divClass:"brui-text-center sm:brui-text-left",navClass:"brui-text-12 brui-leading-14"}},nl=({ariaLabel:e,children:t,copyRight:n,variant:r})=>Qt().createElement("div",{className:tl[r].divClass},Qt().createElement("nav",{className:tl[r].navClass,"aria-label":e},Qt().createElement("ul",{className:"sm:-brui-mx-8 sm:brui-inline-flex"},Qt().Children.map(t,(e,t)=>Qt().createElement(el,{className:"brui-mb-5 last-of-type:brui-mb-0 sm:brui-mb-0 sm:after:brui-inline-block sm:after:brui-align-middle sm:after:brui-h-12 sm:after:brui-w-px sm:after:brui-bg-gray-4 sm:after:last-of-type:brui-w-0",key:t},e)))),n&&Qt().createElement(wo,{elementType:"div",className:"brui-text-gray-4 brui-text-12 brui-leading-14"},n)),rl={default:"brui-flex-col"},al=({variant:e="default",className:t,children:n,...r})=>Qt().createElement("div",{className:[rl[e],"brui-flex brui-box-border brui-w-full",t].join(" ").trim(),...r},n),il=(0,Wt.createContext)(void 0),ol=({children:e,hasError:t,errorMessage:n,className:r})=>{const a=(0,Wt.useId)();return Qt().createElement(il.Provider,{value:{formGroupHasError:t,formGroupErrorMessage:n,inputErrorId:a}},Qt().createElement("div",{className:r},e),t&&void 0!==n&&""!==n&&Qt().createElement(Uo,{id:a,iconClass:"bi_error_bl_bg_cf",iconName:"bi_brui",errorMessage:n||""}))},ll={xs:"brui-text-18 brui-leading-20",sm:"brui-text-20 brui-leading-22 md:brui-text-22 md:brui-leading-28",md:"brui-text-22 brui-leading-24 sm:brui-text-24 sm:brui-leading-26 md:brui-text-24 lg:brui-text-24 md:brui-leading-[31px]",lg:"brui-text-26 brui-leading-28 -brui-tracking-0.3 sm:brui-text-32 sm:brui-leading-36 sm:-brui-tracking-0.6 md:brui-text-32 md:brui-leading-38",xl:"brui-text-30 brui-leading-36 -brui-tracking-0.4 sm:brui-text-34 sm:brui-leading-40 sm:-brui-tracking-0.6 md:brui-text-40 md:brui-leading-46",default:""},ul=({level:e,variant:t,children:n,className:r,...a})=>Qt().createElement(Qt().Fragment,null,Qt().createElement(e,{className:[ll[t],r,"brui-font-bellslim-black"].join(" "),...a},n)),sl=({title:e,subtitle:t,status:n,hideSubtitle:r,children:a,className:i,editButton:o,variant:l="default",disableSrOnlyText:u,autoScrollActiveStep:s=!0,...c})=>{const d="active"===n?{containerStyles:{default:"brui-flex brui-flex-col brui-pt-32 brui-pb-48",leftAlign:"brui-flex brui-flex-col brui-pt-40 brui-pb-24 sm:brui-py-48",leftAlignNoStep:"brui-flex brui-flex-col sm:brui-pt-45 brui-pt-45"},headingTitle:{default:["brui-text-center brui-text-darkblue",i].join(" ").trim(),leftAlign:"",leftAlignNoStep:""},headingSubtitle:{default:"brui-text-14 brui-text-blue",leftAlign:"brui-text-14 brui-text-blue",leftAlignNoStep:"brui-text-14 brui-text-blue"},headingContainer:{default:"brui-flex brui-justify-center",leftAlign:"brui-flex brui-justify-start",leftAlignNoStep:"brui-flex brui-justify-start"}}:"inactive"===n?{containerStyles:{default:"brui-flex brui-flex-col brui-pt-32 brui-pb-48",leftAlign:"brui-flex brui-flex-col brui-py-24 sm:brui-py-48",leftAlignNoStep:"brui-flex brui-flex-col brui-py-45",leftAlignHeading:"brui-flex brui-flex-col"},headingTitle:{default:["brui-text-center brui-text-gray-2",i].join(" ").trim(),leftAlign:"brui-text-gray-7 !brui-text-22 !brui-leading-24",leftAlignNoStep:"brui-text-gray-2 sm:brui-text-32 sm:brui-text-26",leftAlignHeading:"brui-text-gray-2 sm:brui-text-24 sm:brui-text-24 md:brui-text-24 lg:brui-text-24"},headingSubtitle:{default:"brui-text-14 brui-text-gray-2",leftAlign:"brui-text-14 brui-text-gray-7",leftAlignNoStep:"brui-text-14 brui-text-gray-2",leftAlignHeading:"brui-text-14 brui-text-gray-2"},headingContainer:{default:"brui-flex brui-justify-center",leftAlign:"",leftAlignNoStep:""}}:"complete"===n?{containerStyles:{default:"brui-flex brui-flex-col brui-pt-32 brui-pb-48",leftAlign:"brui-flex brui-flex-col brui-py-24 sm:brui-pt-48",leftAlignNoStep:"brui-flex brui-flex-col brui-pt-45 sm:brui-pt-45"},headingTitle:{default:["brui-text-darkblue !brui-text-20 !brui-leading-22 md:!brui-text-22 md:!brui-leading-28",i].join(" ").trim(),leftAlign:"brui-text-gray-2 !brui-text-22 !brui-leading-24",leftAlignNoStep:["brui-text-gray-2 sm:!brui-text-24 !brui-text-26 !brui-leading-28 sm:!brui-leading-26 !brui-tracking-[-0.4px]",i].join(" ").trim()},headingSubtitle:{default:"brui-text-14 brui-text-gray-4",leftAlign:"brui-text-14 brui-text-gray-2",leftAlignNoStep:"brui-text-14 brui-text-gray-2"},headingContainer:{default:"brui-flex brui-justify-between brui-gap-x-10 md:brui-mx-80",leftAlign:"brui-flex brui-justify-between brui-gap-x-10",leftAlignNoStep:"brui-flex brui-justify-between brui-gap-x-10"}}:{containerStyles:{default:"",leftAlign:"",leftAlignNoStep:"",leftAlignHeading:""},headingTitle:{default:"",leftAlign:"",leftAlignNoStep:"",leftAlignHeading:""},headingSubtitle:{default:"",leftAlign:"",leftAlignNoStep:"",leftAlignHeading:""},headingContainer:{default:"",leftAlign:"",leftAlignNoStep:"",leftAlignHeading:""}},m=(0,Wt.useRef)(null);return(0,Wt.useEffect)(()=>{"active"===n&&s&&m.current?.scrollIntoView({behavior:"smooth"})},[n]),Qt().createElement("div",{ref:m,className:d.containerStyles[l]},Qt().createElement("div",{className:d.headingContainer[l]},Qt().createElement(ul,{level:"h2",variant:"lg",className:d.headingTitle[l],...c},Qt().createElement("span",{className:"brui-flex brui-flex-col"},r?null:Qt().createElement(wo,{elementType:"span",className:d.headingSubtitle[l]},t," "),e,u?null:Qt().createElement(Ao,null," ",{active:"(Current Step)",inactive:"(Disabled: Please click the next step button above to proceed)",complete:"(Complete Step)"}[n]))),"complete"===n?o:null),"active"===n||"complete"===n?a:null)},cl={solidBlue:"brui-bg-blue-1 brui-text-white brui-border-blue-1 brui-border-2 brui-border-solid brui-rounded-4 hover:brui-bg-blue hover:brui-border-blue focus:brui-border-blue focus:brui-bg-blue focus:brui-outline focus:brui-outline-blue-2 focus:brui-outline-2 focus:brui-outline-offset-2",underline:"",boldLink:"brui-text-blue focus:brui-outline-blue-2 brui-font-bold hover:brui-underline focus:brui-underline brui-inline-block hover:brui-text-blue-1 focus:brui-text-blue-1 !brui-p-5",outlinedBlue:"",solidRed:"brui-font-sans brui-rounded-4 brui-bg-red brui-text-white brui-border-red brui-border-2 brui-border-solid hover:brui-bg-red-1 hover:brui-border-red-1 focus:brui-bg-red-1 focus:brui-border-red-1 focus:brui-outline-blue focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 brui-inline-block",outlinedBlack:"brui-font-sans brui-rounded-4 brui-bg-transparent brui-text-darkblue  brui-border-darkblue brui-border-2 brui-border-solid hover:brui-bg-transparent-1 focus:brui-outline-blue focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 brui-inline-block",solidWhite:"brui-font-sans brui-rounded-4 brui-bg-white brui-text-darkblue brui-border-white brui-border-2 brui-border-solid hover:brui-bg-pink hover:brui-border-pink focus:brui-bg-pink focus:brui-border-pink focus:brui-outline-white focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 brui-inline-block",outlinedWhite:"brui-font-sans brui-rounded-4 brui-bg-transparent brui-text-white  brui-border-white brui-border-2 brui-border-solid hover:brui-bg-transparent-1 focus:brui-bg-transparent-1 focus:brui-outline-white focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 brui-inline-block",textRed:"brui-font-sans brui-rounded-4 brui-bg-transparent brui-text-red brui-underline brui-underline-offset-2 hover:brui-text-red-1 hover:brui-no-underline focus:brui-text-red-1 focus:brui-no-underline focus:brui-outline-blue-2 focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 !brui-p-0",textBlue:"brui-font-sans brui-rounded-4 brui-bg-transparent brui-text-blue brui-underline brui-underline-offset-2 hover:brui-text-blue-1 hover:brui-no-underline focus:brui-text-blue-1 focus:brui-no-underline focus:brui-outline-blue focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 !brui-p-0",textWhite:"brui-font-sans brui-rounded-4 brui-bg-transparent brui-text-white brui-underline brui-underline-offset-2 hover:brui-text-pink hover:brui-no-underline focus:brui-text-pink focus:brui-no-underline focus:brui-outline-white focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 !brui-p-0",default:"brui-font-sans brui-rounded-4 brui-bg-transparent brui-text-blue brui-underline brui-underline-offset-2 hover:brui-text-blue-1 hover:brui-no-underline focus:brui-text-blue-1 focus:brui-no-underline focus:brui-outline-blue focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 !brui-p-0"},dl={regular:"brui-text-16 brui-py-8 brui-px-30 brui-leading-17",small:"brui-text-14 brui-py-5 brui-px-14 brui-leading-17",default:""},ml=(0,Wt.forwardRef)(function({variant:e="default",size:t="regular",className:n,href:r,...a},i){const o={linkStyle:[dl[t],cl[e],n].join(" ").trim()};return Qt().createElement("a",{className:o.linkStyle,href:r,ref:i,...a})}),pl=ml,bl=({icon:e,text:t,className:n,position:r,href:a,...i})=>{const o=(0,Wt.useMemo)(()=>"right"===r?Qt().createElement(Qt().Fragment,null,Qt().createElement(wo,{elementType:"span",className:"group-hover:brui-underline brui-mr-8"},t),e):Qt().createElement(Qt().Fragment,null,e,Qt().createElement(wo,{elementType:"span",className:"group-hover:brui-underline brui-ml-8"},t)),[r,e,t]);return Qt().createElement(pl,{className:["brui-group brui-no-underline",n,"right"===r?"brui-inline-block":"brui-inline-flex brui-items-center"].join(" ").trim(),href:a,...i},o)},fl={default:"brui-bg-white"},El={defaultInput:"brui-font-normal brui-text-gray-9 brui-rounded brui-border-solid brui-border-2 brui-box-border placeholder:brui-text-gray brui-text-14 brui-leading-18 brui-h-44 brui-w-full brui-py-13 brui-px-10 hover:brui-border-gray focus:brui-border-gray focus:brui-outline-2 focus:brui-outline-blue-2 focus:brui-outline-offset-4 disabled:brui-border-gray-2 disabled:brui-text-gray-2"},yl=(0,Wt.forwardRef)(function({id:e,variant:t="default",errorMessage:n,className:r,"aria-describedby":a,iconClass:i="bi_error_bl_bg_cf",iconName:o="bi_brui",isError:l,errorMessageClassName:u="",...s},c){const d=n&&l?`error-${e}`:"",m=(0,Wt.useMemo)(()=>a?[a||"",d].join(" ").trim():d,[a,d]);return Qt().createElement("div",{className:"brui-flex brui-flex-col"},Qt().createElement("div",{className:"brui-flex brui-items-center"},Qt().createElement("input",{className:[El.defaultInput,fl[t],l?"invalid:brui-border-red brui-border-red":"brui-border-gray-7",r].join(" ").trim(),type:"text","aria-describedby":m||"",ref:c,id:e,...s})),l&&n&&Qt().createElement(Uo,{id:d,iconClass:i,iconName:o,errorMessage:n||"",className:u}))}),_l={defaultLabel:"disabled:brui-text-gray-2 brui-font-semibold brui-text-14 brui-leading-18"},gl=({children:e,required:t,className:n,isError:r,htmlFor:a,overrideClassNames:i,...o})=>{const l=Qt().createElement("span",{className:["brui-mr-8 brui-text-black"].join(" ").trim(),"aria-hidden":"true"},"*");return Qt().createElement(Qt().Fragment,null,Qt().createElement("label",{htmlFor:a,className:i||[_l.defaultLabel,r?"brui-text-red":"brui-text-darkblue",n].join(" ").trim(),...o},t&&l,e))};!function(e){e.ENTER="Enter",e.ESCAPE="Escape",e.SPACE="Space"}(da||(da={})),ma="data-focus-lock",pa="data-focus-lock-disabled",ba="undefined"!=typeof window?Wt.useLayoutEffect:Wt.useEffect,fa=new WeakMap,Ea={width:"1px",height:"0px",padding:0,overflow:"hidden",position:"fixed",top:"1px",left:"1px"},ya=function(){return ya=Object.assign||function(e){var t,n,r,a;for(n=1,r=arguments.length;n<r;n++)for(a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},ya.apply(this,arguments)},Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError,_a=ce({},function(e){return{target:e.target,currentTarget:e.currentTarget}}),ga=ce(),va=ce(),Na=function(e){void 0===e&&(e={});var t=se(null);return t.options=ya({async:!0,ssr:!1},e),t}({async:!0,ssr:"undefined"!=typeof document}),Aa=(0,Wt.createContext)(void 0),Ca=[],ha=(0,Wt.forwardRef)(function(e,t){var n,r,a,i,o,l=(0,Wt.useState)(),u=l[0],s=l[1],c=(0,Wt.useRef)(),d=(0,Wt.useRef)(!1),m=(0,Wt.useRef)(null),p=(0,Wt.useState)({})[1],b=e.children,f=e.disabled,E=void 0!==f&&f,y=e.noFocusGuards,_=void 0!==y&&y,g=e.persistentFocus,v=void 0!==g&&g,N=e.crossFrame,A=void 0===N||N,C=e.autoFocus,h=void 0===C||C,T=(e.allowTextSelection,e.group),I=e.className,R=e.whiteList,x=e.hasPositiveIndices,S=e.shards,O=void 0===S?Ca:S,M=e.as,L=void 0===M?"div":M,D=e.lockProps,B=void 0===D?{}:D,P=e.sideCar,k=e.returnFocus,w=void 0!==k&&k,U=e.focusOptions,F=e.onActivation,H=e.onDeactivation,Y=(0,Wt.useState)({})[0],j=(0,Wt.useCallback)(function(e){var t,n,r=e.captureFocusRestore;m.current||(n=null==(t=document)?void 0:t.activeElement,m.current=n,n!==document.body&&(m.current=r(n))),c.current&&F&&F(c.current),d.current=!0,p()},[F]),G=(0,Wt.useCallback)(function(){d.current=!1,H&&H(c.current),p()},[H]),V=(0,Wt.useCallback)(function(e){var t,n,r,a=m.current;a&&(t=("function"==typeof a?a():a)||document.body,(n="function"==typeof w?w(t):w)&&(r="object"==typeof n?n:void 0,m.current=null,e?Promise.resolve().then(function(){return t.focus(r)}):t.focus(r)))},[w]),z=(0,Wt.useCallback)(function(e){d.current&&_a.useMedium(e)},[]),q=ga.useMedium,K=(0,Wt.useCallback)(function(e){c.current!==e&&(c.current=e,s(e))},[]),W=oe(((n={})[pa]=E&&"disabled",n[ma]=T,n),B),X=!0!==_,Q=X&&"tail"!==_,$=(r=[t,K],a=function(e){return r.forEach(function(t){return le(t,e)})},(i=(0,Wt.useState)(function(){return{value:null,callback:a,facade:{get current(){return i.value},set current(e){var t=i.value;t!==e&&(i.value=e,i.callback(e,t))}}}})[0]).callback=a,o=i.facade,ba(function(){var e,t,n,a=fa.get(o);a&&(e=new Set(a),t=new Set(r),n=o.current,e.forEach(function(e){t.has(e)||le(e,null)}),t.forEach(function(t){e.has(t)||le(t,n)})),fa.set(o,r)},[r]),o),Z=(0,Wt.useMemo)(function(){return{observed:c,shards:O,enabled:!E,active:d.current}},[E,d.current,O,u]);return Qt().createElement(Wt.Fragment,null,X&&[Qt().createElement("div",{key:"guard-first","data-focus-guard":!0,tabIndex:E?-1:0,style:Ea}),x?Qt().createElement("div",{key:"guard-nearest","data-focus-guard":!0,tabIndex:E?-1:1,style:Ea}):null],!E&&Qt().createElement(P,{id:Y,sideCar:Na,observed:u,disabled:E,persistentFocus:v,crossFrame:A,autoFocus:h,whiteList:R,shards:O,onActivation:j,onDeactivation:G,returnFocus:V,focusOptions:U,noFocusGuards:_}),Qt().createElement(L,oe({ref:$},W,{className:I,onBlur:q,onFocus:z}),Qt().createElement(Aa.Provider,{value:Z},b)),Q&&Qt().createElement("div",{"data-focus-guard":!0,tabIndex:E?-1:0,style:Ea}))}),ha.propTypes={};const vl=ha;Ta=function(e){for(var t=Array(e.length),n=0;n<e.length;++n)t[n]=e[n];return t},Ia=function(e){return Array.isArray(e)?e:[e]},Ra=function(e){return Array.isArray(e)?e[0]:e},xa=function(e){return e.parentNode&&e.parentNode.nodeType===Node.DOCUMENT_FRAGMENT_NODE?e.parentNode.host:e.parentNode},Sa=function(e){return e===document||e&&e.nodeType===Node.DOCUMENT_NODE},Oa=function(e,t){var n,r=e.get(t);return void 0!==r?r:(n=function(e,t){return!e||Sa(e)||!function(e){if(e.nodeType!==Node.ELEMENT_NODE)return!1;var t=window.getComputedStyle(e,null);return!(!t||!t.getPropertyValue||"none"!==t.getPropertyValue("display")&&"hidden"!==t.getPropertyValue("visibility"))}(e)&&!function(e){return e.hasAttribute("inert")}(e)&&t(xa(e))}(t,Oa.bind(void 0,e)),e.set(t,n),n)},Ma=function(e,t){var n,r=e.get(t);return void 0!==r?r:(n=function(e,t){return!(e&&!Sa(e))||!!Pa(e)&&t(xa(e))}(t,Ma.bind(void 0,e)),e.set(t,n),n)},La=function(e){return e.dataset},Da=function(e){return"INPUT"===e.tagName},Ba=function(e){return Da(e)&&"radio"===e.type},Pa=function(e){var t=e.getAttribute("data-no-autofocus");return![!0,"true",""].includes(t)},ka=function(e){var t;return Boolean(e&&(null===(t=La(e))||void 0===t?void 0:t.focusGuard))},wa=function(e){return!ka(e)},Ua=function(e){return Boolean(e)},Fa=function(e,t){var n=Math.max(0,e.tabIndex),r=Math.max(0,t.tabIndex),a=n-r,i=e.index-t.index;if(a){if(!n)return 1;if(!r)return-1}return a||i},Ha=function(e,t,n){return Ta(e).map(function(e,t){var r=function(e){return e.tabIndex<0&&!e.hasAttribute("tabindex")?0:e.tabIndex}(e);return{node:e,index:t,tabIndex:n&&-1===r?(e.dataset||{}).focusGuard?0:-1:r}}).filter(function(e){return!t||e.tabIndex>=0}).sort(Fa)},Ya=["button:enabled","select:enabled","textarea:enabled","input:enabled","a[href]","area[href]","summary","iframe","object","embed","audio[controls]","video[controls]","[tabindex]","[contenteditable]","[autofocus]"].join(","),ja="".concat(Ya,", [data-focus-guard]"),Ga=function(e,t){return Ta((e.shadowRoot||e).children).reduce(function(e,n){return e.concat(n.matches(t?ja:Ya)?[n]:[],Ga(n))},[])},Va=function(e,t){return e.reduce(function(e,n){var r,a=Ga(n,t),i=(r=[]).concat.apply(r,a.map(function(e){return function(e,t){var n;return e instanceof HTMLIFrameElement&&(null===(n=e.contentDocument)||void 0===n?void 0:n.body)?Va([e.contentDocument.body],t):[e]}(e,t)}));return e.concat(i,n.parentNode?Ta(n.parentNode.querySelectorAll(Ya)).filter(function(e){return e===n}):[])},[])},za=function(e,t){return Ta(e).filter(function(e){return Oa(t,e)}).filter(function(e){return function(e){return!((Da(e)||function(e){return"BUTTON"===e.tagName}(e))&&("hidden"===e.type||e.disabled))}(e)})},qa=function(e,t){return void 0===t&&(t=new Map),Ta(e).filter(function(e){return Ma(t,e)})},Ka=function(e,t,n){return Ha(za(Va(e,n),t),!0,n)},Wa=function(e,t){return Ha(za(Va(e),t),!1)},Xa=function(e,t){return e.shadowRoot?Xa(e.shadowRoot,t):!(void 0===Object.getPrototypeOf(e).contains||!Object.getPrototypeOf(e).contains.call(e,t))||Ta(e.children).some(function(e){var n,r;return e instanceof HTMLIFrameElement?!!(r=null===(n=e.contentDocument)||void 0===n?void 0:n.body)&&Xa(r,t):Xa(e,t)})},Qa=function(e){if(void 0===e&&(e=document),e&&e.activeElement){var t=e.activeElement;return t.shadowRoot?Qa(t.shadowRoot):t instanceof HTMLIFrameElement&&function(){try{return t.contentWindow.document}catch(e){return}}()?Qa(t.contentWindow.document):t}},$a=function(e){return e.parentNode?$a(e.parentNode):e},Za=function(e){return Ia(e).filter(Boolean).reduce(function(e,t){var n=t.getAttribute(ma);return e.push.apply(e,n?function(e){var t,n,r,a,i;for(t=new Set,n=e.length,r=0;r<n;r+=1)for(a=r+1;a<n;a+=1)((i=e[r].compareDocumentPosition(e[a]))&Node.DOCUMENT_POSITION_CONTAINED_BY)>0&&t.add(a),(i&Node.DOCUMENT_POSITION_CONTAINS)>0&&t.add(r);return e.filter(function(e,n){return!t.has(n)})}(Ta($a(t).querySelectorAll("[".concat(ma,'="').concat(n,'"]:not([').concat(pa,'="disabled"])')))):[t]),e},[])},Ja=function(e,t){return void 0===t&&(t=Qa(Ra(e).ownerDocument)),!(!t||t.dataset&&t.dataset.focusGuard)&&Za(e).some(function(e){return Xa(e,t)||function(e,t){return Boolean(Ta(e.querySelectorAll("iframe")).some(function(e){return function(e,t){return e===t}(e,t)}))}(e,t)})},ei=function(e,t){e&&("focus"in e&&e.focus(t),"contentWindow"in e&&e.contentWindow&&e.contentWindow.focus())},ti=function(e,t){return Ba(e)&&e.name?function(e,t){return t.filter(Ba).filter(function(t){return t.name===e.name}).filter(function(e){return e.checked})[0]||e}(e,t):e},ni=function(e){return e[0]&&e.length>1?ti(e[0],e):e[0]},ri=function(e,t){return e.indexOf(ti(t,e))},ai="NEW_FOCUS",ii=function(e,t,n){var r,a=e.map(function(e){return e.node}),i=qa(a.filter((r=n,function(e){var t,n=null===(t=La(e))||void 0===t?void 0:t.autofocus;return e.autofocus||void 0!==n&&"false"!==n||r.indexOf(e)>=0})));return i&&i.length?ni(i):ni(qa(t))},oi=function(e,t){return void 0===t&&(t=[]),t.push(e),e.parentNode&&oi(e.parentNode.host||e.parentNode,t),t},li=function(e,t){var n,r,a,i;for(n=oi(e),r=oi(t),a=0;a<n.length;a+=1)if(i=n[a],r.indexOf(i)>=0)return i;return!1},ui=function(e,t,n){var r=Ia(e),a=Ia(t),i=r[0],o=!1;return a.filter(Boolean).forEach(function(e){o=li(o||e,e)||o,n.filter(Boolean).forEach(function(e){var t=li(i,e);t&&(o=!o||Xa(t,o)?t:li(t,o))})}),o},si=function(e,t){return e.reduce(function(e,n){return e.concat(function(e,t){return za((n=e.querySelectorAll("[".concat("data-autofocus-inside","]")),Ta(n).map(function(e){return Va([e])}).reduce(function(e,t){return e.concat(t)},[])),t);var n}(n,t))},[])},ci=function(e,t){var n,r,a,i,o,l,u,s,c,d=Qa(Ia(e).length>0?document:Ra(e).ownerDocument),m=Za(e).filter(wa),p=ui(d||e,e,m),b=new Map,f=Wa(m,b),E=f.filter(function(e){var t=e.node;return wa(t)});if(E[0])return i=Wa([p],b).map(function(e){return e.node}),n=i,r=E,a=new Map,r.forEach(function(e){return a.set(e.node,e)}),o=n.map(function(e){return a.get(e)}).filter(Ua),l=o.map(function(e){return e.node}),u=o.filter(function(e){return e.tabIndex>=0}).map(function(e){return e.node}),s=function(e,t,n,r,a){var i,o,l,u,s,c,d,m,p,b,f,E,y,_,g,v,N,A=e.length,C=e[0],h=e[A-1],T=ka(r);if(!(r&&e.indexOf(r)>=0))return i=void 0!==r?n.indexOf(r):-1,o=a?n.indexOf(a):i,l=a?e.indexOf(a):-1,-1===i?-1!==l?l:ai:-1===l?ai:(c=i-o,d=n.indexOf(C),m=n.indexOf(h),u=n,s=new Set,u.forEach(function(e){return s.add(ti(e,u))}),p=u.filter(function(e){return s.has(e)}),b=void 0!==r?p.indexOf(r):-1,f=a?p.indexOf(a):b,E=p.filter(function(e){return e.tabIndex>=0}),y=void 0!==r?E.indexOf(r):-1,_=a?E.indexOf(a):y,g=y>=0&&_>=0?_-y:f-b,!c&&l>=0||0===t.length?l:(v=ri(e,t[0]),N=ri(e,t[t.length-1]),i<=d&&T&&Math.abs(c)>1?N:i>=m&&T&&Math.abs(c)>1?v:c&&Math.abs(g)>1?l:i<=d?N:i>m?v:c?Math.abs(c)>1?l:(A+l+c)%A:void 0))}(l,u,i,d,t),s===ai?(c=ii(f,u,si(m,b))||ii(f,l,si(m,b)))?{node:c}:void 0:void 0===s?s:o[s]},di=0,mi=!1,pi=function(e,t,n){void 0===n&&(n={});var r=ci(e,t);if(!mi&&r){if(di>2)return mi=!0,void setTimeout(function(){mi=!1},1);di++,ei(r.node,n.focusOptions),di--}},bi=function(e){var t=function(e){if(!e)return null;for(var t=[],n=e;n&&n!==document.body;)t.push({current:pe(n),parent:pe(n.parentElement),left:pe(n.previousElementSibling),right:pe(n.nextElementSibling)}),n=n.parentElement;return{element:pe(e),stack:t,ownerDocument:e.ownerDocument}}(e);return function(){return function(e){var t,n,r,a,i,o,l,u,s,c,d,m,p,b,f,E,y,_,g,v,N;if(e)for(o=e.stack,l=e.ownerDocument,u=new Map,s=0,c=o;s<c.length;s++)if((m=null===(t=(d=c[s]).parent)||void 0===t?void 0:t.call(d))&&l.contains(m)){for(p=null===(n=d.left)||void 0===n?void 0:n.call(d),b=d.current(),f=m.contains(b)?b:void 0,E=null===(r=d.right)||void 0===r?void 0:r.call(d),y=Ka([m],u),_=null!==(i=null!==(a=null!=f?f:null==p?void 0:p.nextElementSibling)&&void 0!==a?a:E)&&void 0!==i?i:p;_;){for(g=0,v=y;g<v.length;g++)if(N=v[g],null==_?void 0:_.contains(N.node))return N.node;_=_.nextElementSibling}if(y.length)return y[0].node}}(t)}},fi=function(e,t,n){var r,a,i;void 0===t&&(t={}),r=function(e){return Object.assign({scope:document.body,cycle:!0,onlyTabbable:!0},e)}(t),a=function(e,t,n){var r,a,i;return e&&t?(r=Ia(t)).every(function(t){return!Xa(t,e)})?{}:(i=(a=n?Ka(r,new Map):Wa(r,new Map)).findIndex(function(t){return t.node===e}),-1!==i?{prev:a[i-1],next:a[i+1],first:a[0],last:a[a.length-1]}:void 0):{}}(e,r.scope,r.onlyTabbable),a&&(i=n(a,r.cycle))&&ei(i.node,r.focusOptions)},Ei=function(e,t,n){var r,a,i,o,l=(a=e,i=null===(r=t.onlyTabbable)||void 0===r||r,{first:(o=i?Ka(Ia(a),new Map):Wa(Ia(a),new Map))[0],last:o[o.length-1]})[n];l&&ei(l.node,t.focusOptions)},yi=function(e){return e&&"current"in e?e.current:e},_i=function(){return document&&document.activeElement===document.body},gi=null,vi=null,Ni=function(){return null},Ai=null,Ci=!1,hi=!1,Ti=function(){return!0},Ii=function e(t,n,r){return n&&(n.host===t&&(!n.activeElement||r.contains(n.activeElement))||n.parentNode&&e(t,n.parentNode,r))},Ri=function(e){return Wa(e,new Map)},xi=function(){var e,t,n,r,a,i,o,l,u,s,c,d,m,p,b,f,E,y,_,g,v,N,A=!1;return gi&&(u=(l=gi).observed,s=l.persistentFocus,c=l.autoFocus,d=l.shards,m=l.crossFrame,p=l.focusOptions,b=l.noFocusGuards,f=u||Ai&&Ai.portaledElement,!_i()||!vi||vi===document.body||document.body.contains(vi)&&Ri([(o=vi).parentNode]).some(function(e){return e.node===o})||(E=Ni())&&E.focus(),y=document&&document.activeElement,f&&(_=[f].concat(d.map(yi).filter(Boolean)),y&&!function(e){return(gi.whiteList||Ti)(e)}(y)||(s||function(){if(!(m?Boolean(Ci):"meanwhile"===Ci)||!b||!vi||hi)return!1;var e=Ri(_),t=e.findIndex(function(e){return e.node===vi});return 0===t||t===e.length-1}()||!(_i()||function(e){void 0===e&&(e=document);var t=Qa(e);return!!t&&Ta(e.querySelectorAll("[".concat("data-no-focus-lock","]"))).some(function(e){return Xa(e,t)})}())||!vi&&c)&&(f&&!(Ja(_)||y&&function(e,t){return t.some(function(t){return Ii(e,t,t)})}(y,_)||(i=y,Ai&&Ai.portaledElement===i))&&(document&&!vi&&y&&!c?(y.blur&&y.blur(),document.body.focus()):(A=pi(_,vi,{focusOptions:p}),Ai={})),(vi=document&&document.activeElement)!==document.body&&(Ni=bi(vi)),Ci=!1),document&&y!==document.activeElement&&document.querySelector("[data-focus-auto-guard]")&&(g=document&&document.activeElement,t=Za(e=_).filter(wa),n=ui(e,e,t),r=Ha(Va([n],!0),!0,!0),a=Va(t,!1),v=r.map(function(e){var t=e.node;return{node:t,index:e.index,lockItem:a.indexOf(t)>=0,guard:ka(t)}}),N=v.map(function(e){return e.node}).indexOf(g),N>-1&&(v.filter(function(e){var t=e.guard,n=e.node;return t&&n.dataset.focusAutoGuard}).forEach(function(e){return e.node.removeAttribute("tabIndex")}),fe(N,v.length,1,v),fe(N,-1,-1,v))))),A},Si=function(e){xi()&&e&&(e.stopPropagation(),e.preventDefault())},Oi=function(){return be(xi)},Mi=function(){hi=!0},Li=function(){hi=!1,Ci="just",be(function(){Ci="meanwhile"})},Di={moveFocusInside:pi,focusInside:Ja,focusNextElement:function(e,t){void 0===t&&(t={}),fi(e,t,function(e,t){var n=e.next,r=e.first;return n||t&&r})},focusPrevElement:function(e,t){void 0===t&&(t={}),fi(e,t,function(e,t){var n=e.prev,r=e.last;return n||t&&r})},focusFirstElement:function(e,t){void 0===t&&(t={}),Ei(e,t,"first")},focusLastElement:function(e,t){void 0===t&&(t={}),Ei(e,t,"last")},captureFocusRestore:bi},_a.assignSyncMedium(function(e){var t=e.target,n=e.currentTarget;n.contains(t)||(Ai={observerNode:n,portaledElement:t})}),ga.assignMedium(Oi),va.assignMedium(function(e){return e(Di)});const Nl=(Bi=function(e){return e.filter(function(e){return!e.disabled})},Pi=function(e){var t,n,r=e.slice(-1)[0];r&&!gi&&(document.addEventListener("focusin",Si),document.addEventListener("focusout",Oi),window.addEventListener("focus",Mi),window.addEventListener("blur",Li)),n=(t=gi)&&r&&r.id===t.id,gi=r,t&&!n&&(t.onDeactivation(),e.filter(function(e){return e.id===t.id}).length||t.returnFocus(!r)),r?(vi=null,n&&t.observed===r.observed||r.onActivation(Di),xi(),be(xi)):(document.removeEventListener("focusin",Si),document.removeEventListener("focusout",Oi),window.removeEventListener("focus",Mi),window.removeEventListener("blur",Li),vi=null)},function(e){function t(){n=Bi(o.map(function(e){return e.props})),Pi(n)}var n,r,a,i,o=[],l=function(r){function a(){return r.apply(this,arguments)||this}var i,l,u=r;return(i=a).prototype=Object.create(u.prototype),i.prototype.constructor=i,de(i,u),a.peek=function(){return n},(l=a.prototype).componentDidMount=function(){o.push(this),t()},l.componentDidUpdate=function(){t()},l.componentWillUnmount=function(){var e=o.indexOf(this);o.splice(e,1),t()},l.render=function(){return Qt().createElement(e,this.props)},a}(Wt.PureComponent);return r=l,a="displayName",i="SideEffect("+function(e){return e.displayName||e.name||"Component"}(e)+")",(a=function(e){var t=function(e){var t,n;if("object"!=me(e)||!e)return e;if(void 0!==(t=e[Symbol.toPrimitive])){if("object"!=me(n=t.call(e,"string")))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==me(t)?t:t+""}(a))in r?Object.defineProperty(r,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[a]=i,l})(function(){return null});ki=(0,Wt.forwardRef)(function(e,t){return Qt().createElement(vl,oe({sideCar:Nl,ref:t},e))}),(wi=vl.propTypes||{}).sideCar,function(e,t){var n,r;if(null==e)return{};for(r in n={},e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}}(wi,["sideCar"]),ki.propTypes={};const Al=ki;Ui=n(514);const Cl=({container:e=document.body,children:t})=>(0,Ui.createPortal)(t,e),hl=(0,Wt.createContext)({variant:"default",ariaLabelledby:""}),Tl=({isNestedModal:e=!1,variant:t="default","aria-labelledby":n,"aria-describedby":r,onOverlayClick:a,onEscapeKeyPressed:i,children:o,className:l,focusLockOptions:u={returnFocus:!0,lockProps:{tabIndex:-1}},hasBackdrop:s=!0,modalWidth:c={mobile:"100%",tablet:"645px",desktop:"645px"},...d})=>{const m=n||(0,Wt.useId)(),{width:p}=co(100),b=(0,Wt.useRef)(null);((e,t)=>{(0,Wt.useEffect)(()=>{if(t)return;const n=document.body.style.overflow;let r;document.body.style.overflow="hidden";const a=e.current;return a&&(r=nn(a)),()=>{document.body.style.overflow=n,r&&r()}},[t])})(b,e),so(e=>{e.stopPropagation(),e.key===da.ESCAPE&&i?.(e)});const f=(0,Wt.useMemo)(()=>p<768?c.mobile:p>=768&&p<992?c.tablet:c.desktop,[p,c]);return Qt().createElement(hl.Provider,{value:{variant:t,ariaLabelledby:m,ariaDescribedBy:r}},Qt().createElement(Cl,null,Qt().createElement(Al,{as:"div",...u||{}},Qt().createElement("div",{className:["brui-bg-black-1 brui-transition-opacity brui-overflow-y-auto","brui-h-dvh brui-w-screen","brui-flex brui-items-start","brui-fixed brui-inset-0 brui-m-auto brui-z-[9998]",s?"brui-bg-opacity-50":"brui-bg-opacity-0",l||"brui-justify-center"].join(" ").trim(),onClick:a,role:"dialog","aria-modal":!0,"aria-labelledby":m,tabIndex:-1,...d},Qt().createElement("div",{className:["brui-relative brui-z-10","brui-w-full","brui-flex"].join(" ").trim(),ref:b,role:"document",style:{maxWidth:f}},o)))))},Il={default:"brui-modal-body brui-flex brui-flex-col brui-items-start brui-bg-white brui-gap-16"},Rl=({isDefaultPadding:e=!0,className:t,children:n,...r})=>{const{variant:a}=(0,Wt.useContext)(hl),i=(0,Wt.useMemo)(()=>({modalBodyStyle:[Il[a],e&&"brui-py-30 brui-px-15 sm:brui-px-30",t].join(" ").trim()}),[a]);return Qt().createElement("div",{className:i.modalBodyStyle,...r},n)},xl={default:["brui-min-w-full brui-inline-block brui-bg-white brui-text-left brui-overflow-hidden brui-shadow-xl brui-transform brui-transition-all","brui-flex brui-flex-col","brui-relative brui-opacity-0 sm:brui-align-middle"].join(" ").trim()},Sl={top:"brui-self-start",bottom:"brui-self-end",center:"brui-self-center",left:"",right:""},Ol={top:"brui-rounded-b-10 -brui-translate-y-[50px]",bottom:"brui-rounded-t-10 brui-translate-y-[50px]",center:"brui-rounded-10 -brui-translate-y-[50px]",centerRadius10:"brui-rounded-10 -brui-translate-y-[50px]",left:"brui-rounded-r-10 brui-h-full",right:"brui-rounded-l-10 brui-h-full"},Ml=({className:e,children:t,verticalAlignment:n={mobile:"bottom",tablet:"center",desktop:"center"},useDefaultRadius:r=!0})=>{const{variant:a}=(0,Wt.useContext)(hl),{width:i}=co(100),{verticalAlignmentBreakpoint:o,radiusBreakpoint:l}=((e,t,n,r)=>{const a=(0,Wt.useMemo)(()=>{let a,i;return e<768?(a=t[r.mobile],i=n[r.mobile]):e>=768&&e<992?(a=t[r.tablet],i=n[r.tablet]):(a=t[r.desktop],i=n[r.desktop]),{verticalAlignmentBreakpoint:a,radiusBreakpoint:i}},[e,t,n,r]);return a})(i,Sl,Ol,n),[u,s]=(0,Wt.useState)(!1),c=(0,Wt.useMemo)(()=>({modalContentStyle:[xl[a],u?"brui-transition-all brui-duration-300 brui-ease-out brui-delay-50 brui-opacity-100 brui-transform-none":"",o,r?l:"",e].join(" ").trim()}),[a,e,u]);return(0,Wt.useEffect)(()=>{s(!0)},[]),Qt().createElement("div",{className:"brui-flex brui-justify-center brui-w-full "+("center"===n.mobile?"brui-min-h-dvh brui-px-16":"brui-min-h-dvh")},Qt().createElement("div",{onClick:e=>e.stopPropagation(),className:c.modalContentStyle},t))},Ll={variant:{gray:"brui-bg-gray-5",transparent:"",lightGray:"brui-bg-gray-3"}},Dl=({variant:e="gray",children:t,className:n,isDefaultPadding:r=!0,...a})=>Qt().createElement("div",{className:["brui-flex sm:brui-gap-32","brui-justify-center sm:brui-justify-start brui-flex-col sm:brui-flex-row",r&&"brui-ps-24 brui-pe-16 brui-py-16 brui-gap-16 sm:brui-px-32 sm:brui-py-24",Ll.variant[e],n].join(" ").trim(),...a},t),Bl={variant:{grayBar:"brui-bg-gray-5",transparent:"",none:"",lightGrayBar:"brui-bg-gray-3"}},Pl=({variant:e="transparent",headerIcon:t,title:n,rightButtonIcon:r,onRightButtonClicked:a,rightButtonLabel:i="Close Modal",className:o,children:l,isDefaultPadding:u=!0})=>{const{ariaLabelledby:s}=(0,Wt.useContext)(hl);return Qt().createElement("div",{className:["brui-modal-header brui-flex brui-flex-col",Bl.variant[e],u&&"brui-py-25 brui-px-15 sm:brui-px-30",o].join(" ").trim()},Qt().createElement("div",{className:"brui-flex brui-justify-between brui-gap-16"},Qt().createElement("div",{className:["brui-flex brui-items-start  sm:brui-items-center brui-justify-center sm:brui-justify-start brui-gap-20 brui-flex-col sm:brui-flex-row"].join(" ").trim()},t||null,Qt().createElement(ul,{className:"brui-text-22 brui-leading-24 sm:brui-text-24 sm:brui-leading-26 brui-text-black brui-tracking-[-0.4px]",id:s||"modal=dialog-title",level:"h2",variant:"default"},n)),r?Qt().createElement("div",{className:"brui-pl-30"},Qt().createElement("button",{type:"button","aria-label":i,onClick:()=>a?.(),className:"brui-flex brui-rounded-2 brui-text-blue hover:brui-text-blue-1 focus:!brui-outline-blue focus:!brui-outline focus:brui-outline-2 focus:brui-outline-offset-3",id:`${s}-close-button`||"modal-dialog-close-button"},"default"===r?Qt().createElement(Eo,{className:"brui-text-20",iconClass:"bi_brui",iconName:"bi_close"}):null,Qt().createElement(Ao,null,i))):null),l)},kl={ordinaryPrice:{divClassName:null,spanClassName:null,forwardSlashClassName:null},defaultPrice:{divClassName:"brui-font-bold brui-text-18 brui-text-blue",spanClassName:null,forwardSlashClassName:null},bigPrice:{divClassName:"brui-font-bellslim-heavy brui-text-28 brui-leading-25 sm:brui-text-40 sm:brui-leading-40 -brui-tracking-1 brui-whitespace-nowrap",spanClassName:"brui-text-16 brui-leading-16 sm:brui-text-20 sm:brui-leading-30 -brui-tracking-0.45 brui-relative brui-top-0 sm:-brui-top-3 brui-align-top",forwardSlashClassName:"brui-text-[11px] brui-leading-16 sm:brui-text-[13px] sm:brui-leading-20 -brui-tracking-0.45 brui-relative brui-top-0 sm:brui-top-3 brui-align-top"},smallPrice:{divClassName:"brui-font-bellslim-heavy brui-text-28 brui-leading-25 -brui-tracking-1 brui-whitespace-nowrap",spanClassName:"brui-text-16 brui-leading-16 -brui-tracking-0.45 brui-relative brui-align-top",forwardSlashClassName:"brui-text-[11px] brui-leading-16 -brui-tracking-0.45 brui-relative brui-align-top"}},wl={strikeText:{en:"previous price",fr:"précédent Coût"},decimalPointText:{en:".",fr:","},perMonth:{visibleText:{en:"MO.",fr:"MOIS"},screenReaderText:{en:" per month",fr:" par mois"}},perDay:{visibleText:{en:"DAY",fr:"JOUR"},screenReaderText:{en:" per day",fr:" par jour"}}},Ul={CR:{en:"Credit",fr:"Crédit"},"-":{en:"Negative",fr:"Négatif"}},Fl=({price:e,variant:t="defaultPrice",strike:n,reverse:r,language:a="en",suffixText:i,className:o,negativeIndicator:l="CR",showZeroDecimalPart:u,srText:s})=>{if(!e&&0!==e)return null;const c=e<0,d=Math.abs(e),m=d.toFixed(2).toString().split("."),p=m[0];let b=m[1]||"";b=parseFloat(p)===d?"00":b,i&&"00"===b&&!u&&(b="");const f=(0,Wt.useMemo)(()=>{let e="",o="",u="";const c="fr"===a,d=n?"brui-line-through":"";let m,f,E;return s?u=`${s} `:null!==l&&(u=`${Ul[l][a]} `),"bigPrice"!==t&&"smallPrice"!==t||(e=c?"":"brui-mr-px",(b||i)&&(o=c?"":"brui-ml-[2px]")),m=r?"brui-text-white":n?"brui-text-gray":"ordinaryPrice"===t?"":"brui-text-darkblue",c?(f=Qt().createElement(Qt().Fragment,null,Qt().createElement("span",{className:d},p),b?Qt().createElement("span",{className:[kl[t].spanClassName,o,d].join(" ").trim()},wl.decimalPointText.fr+b):null,Qt().createElement("span",{className:[kl[t].spanClassName,e].join(" ").trim()}," $")),E=`${p}${b?wl.decimalPointText.fr+b:""} $`):(f=Qt().createElement(Qt().Fragment,null,Qt().createElement("span",{className:[kl[t].spanClassName,e].join(" ").trim()},"$"),Qt().createElement("span",{className:d},p),b?Qt().createElement("span",{className:[kl[t].spanClassName,o,d].join(" ").trim()},wl.decimalPointText.en+b):null),E=`$${p}${b?wl.decimalPointText.en+b:""}`),{dollarMargin:e,leftMargin:o,strikeClass:d,priceTextColor:m,priceMarkup:f,priceScreenReaderText:E,negativeText:u}},[p,t,b,i,r,n,a,s]);return Qt().createElement(Qt().Fragment,null,Qt().createElement("div",{className:[kl[t].divClassName,f.priceTextColor,o].join(" ").trim(),"aria-hidden":"true"},c&&`${l?.toString()} `,f.priceMarkup,i?Qt().createElement(Qt().Fragment,null,Qt().createElement("span",{className:[kl[t].forwardSlashClassName,f.leftMargin].join(" ").trim()},"/"),Qt().createElement("span",{className:[kl[t].spanClassName,f.leftMargin].join(" ").trim()},wl[i].visibleText[a])):null),n&&Qt().createElement("span",{className:"brui-sr-only"},wl.strikeText[a]," "),Qt().createElement("span",{className:"brui-sr-only"},c&&f.negativeText,f.priceScreenReaderText,i?wl[i].screenReaderText[a]:null))},Hl={default:"group-has-[:focus-visible]/inputradiobutton:brui-outline-blue group-has-[:focus-visible]/inputradiobutton:brui-outline group-has-[:focus-visible]/inputradiobutton:brui-outline-2 group-has-[:focus-visible]/inputradiobutton:brui-outline-offset-3",boxedInMobile:"group-has-[:focus-visible]/inputradiobutton:sm:brui-outline-blue group-has-[:focus-visible]/inputradiobutton:sm:brui-outline group-has-[:focus-visible]/inputradiobutton:sm:brui-outline-2 group-has-[:focus-visible]/inputradiobutton:sm:brui-outline-offset-3"},Yl=(0,Wt.forwardRef)(function({id:e,name:t,value:n,hasError:r=!1,variant:a,...i},o){return Qt().createElement("div",{className:"brui-relative brui-group/inputradiobutton"},Qt().createElement("input",{type:"radio",id:e,name:t,value:n,className:"brui-absolute brui-size-full brui-opacity-0 enabled:brui-cursor-pointer disabled:brui-cursor-default brui-z-10",...i,ref:o}),Qt().createElement("div",{className:"brui-relative"},Qt().createElement("div",{className:["brui-size-24 brui-rounded-12 brui-border group-has-[:disabled]/inputradiobutton:brui-opacity-40 group-has-[:checked]/inputradiobutton:brui-bg-blue-1  group-has-[:checked]/inputradiobutton:brui-border-blue-1",r?"brui-border-red":"brui-border-gray-2",Hl[a]].join(" ").trim()}),Qt().createElement("div",{className:["brui-scale-0 group-has-[:checked]/inputradiobutton:brui-scale-100 brui-absolute brui-w-12 brui-h-12 brui-bg-white brui-rounded-6 brui-top-1/2 brui-left-1/2 brui-transform -brui-translate-x-1/2 -brui-translate-y-1/2 brui-transition-transform group-has-[:disabled]/inputradiobutton:brui-opacity-40",r?"brui-bg-red":"brui-bg-blue"].join(" ").trim()})))}),jl=(0,Wt.forwardRef)(function({id:e,name:t,value:n,children:r,variant:a,hasError:i=!1,RadioButtonWrapperClass:o,...l},u){const s={default:{wrapper:"brui-group/label brui-relative brui-inline-flex brui-w-full brui-border-0",label:"brui-pl-12 enabled:brui-text-14 enabled:brui-leading-19 brui-cursor-pointer brui-self-center group-has-[:disabled]/label:brui-text-gray-7 group-has-[:disabled]/label:brui-cursor-default group-has-[:checked]/label:brui-font-bold group-has-[:checked:disabled]/label:brui-font-normal"},boxedInMobile:{wrapper:"brui-group/label brui-relative brui-inline-flex brui-w-full brui-rounded-4 brui-border-1 sm:brui-border-0 has-[:checked]:sm:brui-border-0 has-[:checked]:brui-border-2 brui-p-15 sm:brui-p-0 has-[input:focus-visible]:brui-outline has-[input:focus-visible]:brui-outline-2 has-[input:focus-visible]:brui-outline-offset-3 has-[input:focus-visible]:brui-outline-blue-2 has-[input:focus-visible]:sm:brui-outline-0 has-[:checked:disabled]:brui-border-[#BABEC2] brui-shadow-4sm sm:brui-shadow-none"+(i?" brui-border-red has-[:checked]:brui-border-red":" brui-border-[#d7d7d7] has-[:checked]:brui-border-blue"),label:"brui-pl-12 brui-text-14 brui-leading-19 enabled:brui-cursor-pointer group-has-[:checked]/label:brui-font-bold group-has-[:checked:disabled]/label:brui-font-normal brui-self-center group-has-[:disabled]/label:brui-text-gray-7 group-has-[:disabled]/label:brui-cursor-default group-has-[:checked:disabled]/label:brui-font-normal"}};return Qt().createElement(Qt().Fragment,null,Qt().createElement("div",{className:[s[a].wrapper,o].join(" ").trim()},Qt().createElement(Yl,{id:e,name:t,value:n,ref:u,hasError:i,variant:a,...l}),Qt().createElement("label",{htmlFor:e,className:[s[a].label].join(" ").trim()},r)))}),Gl=jl,Vl={topRight:"brui-right-24 brui-top-24",topLeft:"brui-top-16 brui-left-16 sm:brui-top-32 sm:brui-left-32",leftCenter:"brui-left-12 brui-transform -brui-translate-y-1/2 brui-top-1/2"},zl=(0,Wt.forwardRef)(function({radioPlacement:e="topRight",radioPlacementMobile:t,borderRadiusClassName:n,cursorPointer:r=!1,...a},i){const{width:o}=co(),l=(0,Wt.useMemo)(()=>t&&o<768?t:e,[o]);return Qt().createElement("div",{className:"brui-group/inputradio brui-absolute brui-right-0 brui-top-0 brui-leading-0 brui-w-full brui-h-full"},Qt().createElement("div",{className:["brui-absolute brui-w-full brui-h-full group-has-[:checked]/inputradio:brui-border-2 group-has-[:checked]/inputradio:brui-border-blue group-has-[:focus-visible]/inputradio:brui-outline-blue-2 group-has-[:focus-visible]/inputradio:brui-outline group-has-[:focus-visible]/inputradio:brui-outline-2 group-has-[:focus-visible]/inputradio:brui-outline-offset-3 transition-all brui-shadow-4sm group-has-[:checked]/inputradio:brui-shadow-none",n||"brui-rounded-20"].join(" ").trim()}),Qt().createElement("input",{type:"radio",className:"brui-absolute brui-left-0 brui-top-0 brui-w-full brui-h-full brui-z-10 brui-opacity-0 "+(r?"brui-cursor-pointer":""),ref:i,...a}),Qt().createElement("div",{className:["brui-absolute",Vl[l]].join(" ").trim()},Qt().createElement("div",{className:"brui-w-24 brui-h-24 brui-rounded-12 brui-border brui-border-gray-7 group-has-[:checked]/inputradio:brui-bg-blue-1 group-has-[:checked]/inputradio:brui-border-blue-1"}),Qt().createElement("div",{className:"brui-scale-0 group-has-[:checked]/inputradio:brui-scale-100 brui-absolute brui-w-12 brui-h-12 brui-bg-white brui-rounded-6 brui-top-1/2 brui-left-1/2 brui-transform -brui-translate-x-1/2 -brui-translate-y-1/2 brui-transition-transform"})))}),ql={topRight:"brui-py-32 brui-px-24",topLeft:"brui-p-16 sm:brui-p-32",leftCenter:"brui-p-12 brui-pl-48"},Kl=(0,Wt.forwardRef)(function({children:e,className:t,"aria-labelledby":n,"aria-describedby":r,name:a,radioPlacement:i="topRight",radioPlacementMobile:o,borderRadiusClassName:l,defaultPadding:u=!0,disabled:s,...c},d){return Qt().createElement("div",{className:["brui-group brui-border brui-relative",u&&ql[i],l||"brui-rounded-20",t,s&&"brui-bg-gray-5 brui-border-gray-3 brui-opacity-60"].join(" ").trim()},Qt().createElement(zl,{"aria-labelledby":n,"aria-describedby":r,name:a,ref:d,radioPlacement:i,radioPlacementMobile:o,borderRadiusClassName:l,disabled:s,...c}),e)}),Wl=Kl,Xl=({children:e,className:t,...n})=>Qt().createElement("div",{className:["brui-hidden group-has-[:checked]:brui-block",t].join(" ").trim(),...n},e),Ql=({children:e,className:t,...n})=>Qt().createElement("div",{className:["brui-mb-24",t].join(" ").trim(),...n},e),$l=({children:e,className:t,...n})=>Qt().createElement("div",{className:["brui-mt-auto",t].join(" ").trim(),...n},e),Zl=(0,Wt.createContext)({sameHeightGroups:[],updateSameHeightGroups:()=>{},deleteItem:()=>{}}),Jl=({children:e})=>{const[t,n]=(0,Wt.useState)([]),r=(0,Wt.useCallback)((e,t)=>{n(n=>n.some(t=>t.groupIndex===e)?n.map(n=>n.groupIndex===e?n.items.some(e=>e.index===t.index)?{...n,items:n.items.map(e=>e.index===t.index?t:e)}:{...n,items:[...n.items,t]}:n):[...n,{groupIndex:e,items:[t]}])},[]);return Qt().createElement(Zl.Provider,{value:{sameHeightGroups:t,updateSameHeightGroups:r,deleteItem:(e,t)=>{n(n=>n.map(n=>n.groupIndex===e?{...n,items:n.items.filter(e=>e.index!==t)}:n))}}},e)},eu=({children:e,index:t,groupIndex:n})=>{const r=(0,Wt.useRef)(null),[a,i]=(0,Wt.useState)(null),{width:o}=co(100),l=bo(),[u,s]=(0,Wt.useState)(o<768),c=(0,Wt.useContext)(Zl);if(!c)return null;const{sameHeightGroups:d,updateSameHeightGroups:m,deleteItem:p}=c;return(0,Wt.useEffect)(()=>{const e=d.find(e=>e.groupIndex===n),t=e?.items.map(e=>e.height);s(o<768),i(t?Math.max(...t):null)},[o,l,d]),(0,Wt.useEffect)(()=>{if(r.current){const e=r.current.offsetHeight;d&&m(n,{index:t,height:e})}return()=>{p(n,t)}},[l]),Qt().createElement("div",{ref:r,style:{minHeight:u?0:a?`${a}px`:"auto"},"data-id":t},e)},tu=(0,Wt.createContext)(null),nu=()=>{const e=(0,Wt.useContext)(tu);if(null===e)throw new Error("Select components must be wrapped in <Select />");return e},ru=tu,au=new Set(["inline","contents"]),iu=new Set(["table","td","th"]),ou=[":popover-open",":modal"],lu=["transform","translate","scale","rotate","perspective"],uu=["transform","translate","scale","rotate","perspective","filter"],su=["paint","layout","strict","content"],cu=new Set(["html","body","#document"]);Fi=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"].join(","),Hi="undefined"==typeof Element,Yi=Hi?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,ji=!Hi&&Element.prototype.getRootNode?function(e){var t;return null==e||null===(t=e.getRootNode)||void 0===t?void 0:t.call(e)}:function(e){return null==e?void 0:e.ownerDocument},Gi=function e(t,n){var r,a;return void 0===n&&(n=!0),""===(a=null==t||null===(r=t.getAttribute)||void 0===r?void 0:r.call(t,"inert"))||"true"===a||n&&t&&e(t.parentNode)},Vi=function e(t,n,r){var a,i,o,l,u,s,c,d;for(a=[],i=Array.from(t);i.length;)o=i.shift(),Gi(o,!1)||("SLOT"===o.tagName?(u=e((l=o.assignedElements()).length?l:o.children,!0,r),r.flatten?a.push.apply(a,u):a.push({scopeParent:o,candidates:u})):(Yi.call(o,Fi)&&r.filter(o)&&(n||!t.includes(o))&&a.push(o),s=o.shadowRoot||"function"==typeof r.getShadowRoot&&r.getShadowRoot(o),c=!Gi(s,!1)&&(!r.shadowRootFilter||r.shadowRootFilter(o)),s&&c?(d=e(!0===s?o.children:s.children,!0,r),r.flatten?a.push.apply(a,d):a.push({scopeParent:o,candidates:d})):i.unshift.apply(i,o.children)));return a},zi=function(e){return!isNaN(parseInt(e.getAttribute("tabindex"),10))},qi=function(e){if(!e)throw new Error("No node provided");return e.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||function(e){var t,n=null==e||null===(t=e.getAttribute)||void 0===t?void 0:t.call(e,"contenteditable");return""===n||"true"===n}(e))&&!zi(e)?0:e.tabIndex},Ki=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},Wi=function(e){return"INPUT"===e.tagName},Xi=function(e){var t=e.getBoundingClientRect(),n=t.width,r=t.height;return 0===n&&0===r},Qi=function(e,t){return!(t.disabled||Gi(t)||function(e){return Wi(e)&&"hidden"===e.type}(t)||function(e,t){var n,r,a,i,o=t.displayCheck,l=t.getShadowRoot;if("hidden"===getComputedStyle(e).visibility)return!0;if(n=Yi.call(e,"details>summary:first-of-type")?e.parentElement:e,Yi.call(n,"details:not([open]) *"))return!0;if(o&&"full"!==o&&"legacy-full"!==o){if("non-zero-area"===o)return Xi(e)}else{if("function"==typeof l){for(r=e;e;){if(a=e.parentElement,i=ji(e),a&&!a.shadowRoot&&!0===l(a))return Xi(e);e=e.assignedSlot?e.assignedSlot:a||i===e.ownerDocument?a:i.host}e=r}if(function(e){var t,n,r,a,i,o,l,u=e&&ji(e),s=null===(t=u)||void 0===t?void 0:t.host,c=!1;if(u&&u!==e)for(c=!!(null!==(n=s)&&void 0!==n&&null!==(r=n.ownerDocument)&&void 0!==r&&r.contains(s)||null!=e&&null!==(a=e.ownerDocument)&&void 0!==a&&a.contains(e));!c&&s;)c=!(null===(o=s=null===(i=u=ji(s))||void 0===i?void 0:i.host)||void 0===o||null===(l=o.ownerDocument)||void 0===l||!l.contains(s));return c}(e))return!e.getClientRects().length;if("legacy-full"!==o)return!0}return!1}(t,e)||function(e){return"DETAILS"===e.tagName&&Array.prototype.slice.apply(e.children).some(function(e){return"SUMMARY"===e.tagName})}(t)||function(e){var t,n,r;if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(t=e.parentElement;t;){if("FIELDSET"===t.tagName&&t.disabled){for(n=0;n<t.children.length;n++)if("LEGEND"===(r=t.children.item(n)).tagName)return!!Yi.call(t,"fieldset[disabled] *")||!r.contains(e);return!0}t=t.parentElement}return!1}(t))},$i=function(e,t){return!(function(e){return function(e){return Wi(e)&&"radio"===e.type}(e)&&!function(e){var t,n,r,a;if(!e.name)return!0;if(n=e.form||ji(e),r=function(e){return n.querySelectorAll('input[type="radio"][name="'+e+'"]')},"undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.escape)t=r(window.CSS.escape(e.name));else try{t=r(e.name)}catch(e){return!1}return a=function(e,t){for(var n=0;n<e.length;n++)if(e[n].checked&&e[n].form===t)return e[n]}(t,e.form),!a||a===e}(e)}(t)||qi(t)<0||!Qi(e,t))},Zi=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!(isNaN(t)||t>=0)},Ji=function e(t){var n=[],r=[];return t.forEach(function(t,a){var i=!!t.scopeParent,o=i?t.scopeParent:t,l=function(e,t){var n=qi(e);return n<0&&t&&!zi(e)?0:n}(o,i),u=i?e(t.candidates):o;0===l?i?n.push.apply(n,u):n.push(o):r.push({documentOrder:a,tabIndex:l,item:t,isScope:i,content:u})}),r.sort(Ki).reduce(function(e,t){return t.isScope?e.push.apply(e,t.content):e.push(t.content),e},[]).concat(n)},eo=function(e,t){var n;return n=(t=t||{}).getShadowRoot?Vi([e],t.includeContainer,{filter:$i.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:Zi}):function(e,t,n){if(Gi(e))return[];var r=Array.prototype.slice.apply(e.querySelectorAll(Fi));return t&&Yi.call(e,Fi)&&r.unshift(e),r.filter(n)}(e,t.includeContainer,$i.bind(null,t)),Ji(n)};const du=Math.min,mu=Math.max,pu=Math.round,bu=Math.floor,fu=e=>({x:e,y:e}),Eu={left:"right",right:"left",bottom:"top",top:"bottom"},yu={start:"end",end:"start"},_u=new Set(["top","bottom"]),gu=["left","right"],vu=["right","left"],Nu=["top","bottom"],Au=["bottom","top"],Cu=new Set(["left","top"]),hu=fu(0),Tu=new Set(["absolute","fixed"]),Iu={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:a}=e;const i="fixed"===a,o=ge(r),l=!!t&&Ie(t.floating);if(r===o||l&&i)return n;let u={scrollLeft:0,scrollTop:0},s=fu(1);const c=fu(0),d=Ae(r);if((d||!d&&!i)&&(("body"!==ye(r)||he(o))&&(u=Me(r)),Ae(r))){const e=ct(r);s=ut(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}const m=!o||d||i?fu(0):mt(o,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+c.x+m.x,y:n.y*s.y-u.scrollTop*s.y+c.y+m.y}},getDocumentElement:ge,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:a}=e;const i=[..."clippingAncestors"===n?Ie(t)?[]:function(e,t){const n=t.get(e);if(n)return n;let r=Be(e,[],!1).filter(e=>Ne(e)&&"body"!==ye(e)),a=null;const i="fixed"===Oe(e).position;let o=i?Le(e):e;for(;Ne(o)&&!Se(o);){const t=Oe(o),n=Re(o);n||"fixed"!==t.position||(a=null),(i?!n&&!a:!n&&"static"===t.position&&a&&Tu.has(a.position)||he(o)&&!n&&bt(e,o))?r=r.filter(e=>e!==o):a=t,o=Le(o)}return t.set(e,r),r}(t,this._c):[].concat(n),r],o=i[0],l=i.reduce((e,n)=>{const r=pt(t,n,a);return e.top=mu(r.top,e.top),e.right=du(r.right,e.right),e.bottom=du(r.bottom,e.bottom),e.left=mu(r.left,e.left),e},pt(t,o,a));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:_t,getElementRects:async function(e){const t=this.getOffsetParent||_t,n=this.getDimensions,r=await n(e.floating);return{reference:ft(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){const{width:t,height:n}=ot(e);return{width:t,height:n}},getScale:ut,isElement:Ne,isRTL:function(e){return"rtl"===Oe(e).direction}},Ru=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:a,y:i,placement:o,middlewareData:l}=t,u=await async function(e,t){const{placement:n,platform:r,elements:a}=e,i=await(null==r.isRTL?void 0:r.isRTL(a.floating)),o=We(n),l=Xe(n),u="y"===Ze(n),s=Cu.has(o)?-1:1,c=i&&u?-1:1,d=Ke(t,e);let{mainAxis:m,crossAxis:p,alignmentAxis:b}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof b&&(p="end"===l?-1*b:b),u?{x:p*c,y:m*s}:{x:m*s,y:p*c}}(t,e);return o===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:a+u.x,y:i+u.y,data:{...u,placement:o}}}}},xu=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:a}=t,{mainAxis:i=!0,crossAxis:o=!1,limiter:l={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=Ke(e,t),s={x:n,y:r},c=await it(t,u),d=Ze(We(a)),m=Qe(d);let p=s[m],b=s[d];if(i){const e="y"===m?"bottom":"right";p=qe(p+c["y"===m?"top":"left"],p,p-c[e])}if(o){const e="y"===d?"bottom":"right";b=qe(b+c["y"===d?"top":"left"],b,b-c[e])}const f=l.fn({...t,[m]:p,[d]:b});return{...f,data:{x:f.x-n,y:f.y-r,enabled:{[m]:i,[d]:o}}}}}},Su=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,a,i,o;const{placement:l,middlewareData:u,rects:s,initialPlacement:c,platform:d,elements:m}=t,{mainAxis:p=!0,crossAxis:b=!0,fallbackPlacements:f,fallbackStrategy:E="bestFit",fallbackAxisSideDirection:y="none",flipAlignment:_=!0,...g}=Ke(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};const v=We(l),N=Ze(c),A=We(c)===c,C=await(null==d.isRTL?void 0:d.isRTL(m.floating)),h=f||(A||!_?[tt(c)]:function(e){const t=tt(e);return[et(e),t,et(t)]}(c)),T="none"!==y;!f&&T&&h.push(...function(e,t,n,r){const a=Xe(e);let i=function(e,t,n){switch(e){case"top":case"bottom":return n?t?vu:gu:t?gu:vu;case"left":case"right":return t?Nu:Au;default:return[]}}(We(e),"start"===n,r);return a&&(i=i.map(e=>e+"-"+a),t&&(i=i.concat(i.map(et)))),i}(c,_,y,C));const I=[c,...h],R=await it(t,g),x=[];let S=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&x.push(R[v]),b){const e=function(e,t,n){void 0===n&&(n=!1);const r=Xe(e),a=Je(e),i=$e(a);let o="x"===a?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(o=tt(o)),[o,tt(o)]}(l,s,C);x.push(R[e[0]],R[e[1]])}if(S=[...S,{placement:l,overflows:x}],!x.every(e=>e<=0)){const e=((null==(a=u.flip)?void 0:a.index)||0)+1,t=I[e];if(t&&("alignment"!==b||N===Ze(t)||S.every(e=>e.overflows[0]>0&&Ze(e.placement)===N)))return{data:{index:e,overflows:S},reset:{placement:t}};let n=null==(i=S.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(E){case"bestFit":{const e=null==(o=S.filter(e=>{if(T){const t=Ze(e.placement);return t===N||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:o[0];e&&(n=e);break}case"initialPlacement":n=c}if(l!==n)return{reset:{placement:n}}}return{}}}},Ou=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:a,rects:i,platform:o,elements:l,middlewareData:u}=t,{element:s,padding:c=0}=Ke(e,t)||{};if(null==s)return{};const d=nt(c),m={x:n,y:r},p=Je(a),b=$e(p),f=await o.getDimensions(s),E="y"===p,y=E?"top":"left",_=E?"bottom":"right",g=E?"clientHeight":"clientWidth",v=i.reference[b]+i.reference[p]-m[p]-i.floating[b],N=m[p]-i.reference[p],A=await(null==o.getOffsetParent?void 0:o.getOffsetParent(s));let C=A?A[g]:0;C&&await(null==o.isElement?void 0:o.isElement(A))||(C=l.floating[g]||i.floating[b]);const h=v/2-N/2,T=C/2-f[b]/2-1,I=du(d[y],T),R=du(d[_],T),x=I,S=C-f[b]-R,O=C/2-f[b]/2+h,M=qe(x,O,S),L=!u.arrow&&null!=Xe(a)&&O!==M&&i.reference[b]/2-(O<x?I:R)-f[b]/2<0,D=L?O<x?O-x:O-S:0;return{[p]:m[p]+D,data:{[p]:M,centerOffset:O-M-D,...L&&{alignmentOffset:D}},reset:L}}});to="undefined"!=typeof document?Wt.useLayoutEffect:function(){};const Mu=e=>({name:"arrow",options:e,fn(t){const{element:n,padding:r}="function"==typeof e?e(t):e;return n&&(a=n,{}.hasOwnProperty.call(a,"current"))?null!=n.current?Ou({element:n.current,padding:r}).fn(t):{}:n?Ou({element:n,padding:r}).fn(t):{};var a}}),Lu=(e,t)=>({...xu(e),options:[e,t]}),Du=(e,t)=>({...Su(e),options:[e,t]}),Bu=(e,t)=>({...Mu(e),options:[e,t]}),Pu={...Xt},ku=Pu.useInsertionEffect||(e=>e());no="undefined"!=typeof document?Wt.useLayoutEffect:Wt.useEffect;let wu=!1,Uu=0;const Fu=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+Uu++,Hu=Pu.useId||function(){const[e,t]=Wt.useState(()=>wu?Fu():void 0);return no(()=>{null==e&&t(Fu())},[]),Wt.useEffect(()=>{wu=!0},[]),e},Yu=Wt.forwardRef(function(e,t){const{context:{placement:n,elements:{floating:r},middlewareData:{arrow:a,shift:i}},width:o=14,height:l=7,tipRadius:u=0,strokeWidth:s=0,staticOffset:c,stroke:d,d:m,style:{transform:p,...b}={},...f}=e,E=Hu(),[y,_]=Wt.useState(!1);if(no(()=>{r&&"rtl"===Oe(r).direction&&_(!0)},[r]),!r)return null;const[g,v]=n.split("-"),N="top"===g||"bottom"===g;let A=c;(N&&null!=i&&i.x||!N&&null!=i&&i.y)&&(A=null);const C=2*s,h=C/2,T=o/2*(u/-8+1),I=l/2*u/4,R=!!m,x=A&&"end"===v?"bottom":"top";let S=A&&"end"===v?"right":"left";A&&y&&(S="end"===v?"left":"right");const O=null!=(null==a?void 0:a.x)?A||a.x:"",M=null!=(null==a?void 0:a.y)?A||a.y:"",L=m||"M0,0 H"+o+" L"+(o-T)+","+(l-I)+" Q"+o/2+","+l+" "+T+","+(l-I)+" Z",D={top:R?"rotate(180deg)":"",left:R?"rotate(90deg)":"rotate(-90deg)",bottom:R?"":"rotate(180deg)",right:R?"rotate(-90deg)":"rotate(90deg)"}[g];return Wt.createElement("svg",Rt({},f,{"aria-hidden":!0,ref:t,width:R?o:o+C,height:o,viewBox:"0 0 "+o+" "+(l>o?l:o),style:{position:"absolute",pointerEvents:"none",[S]:O,[x]:M,[g]:N||R?"100%":"calc(100% - "+C/2+"px)",transform:[D,p].filter(e=>!!e).join(" "),...b}}),C>0&&Wt.createElement("path",{clipPath:"url(#"+E+")",fill:"none",stroke:d,strokeWidth:C+(m?0:1),d:L}),Wt.createElement("path",{stroke:C&&!m?f.fill:"none",d:L}),Wt.createElement("clipPath",{id:E},Wt.createElement("rect",{x:-h,y:h*(R?-1:1),width:o+C,height:o})))}),ju=Wt.createContext(null),Gu=Wt.createContext(null),Vu=()=>{var e;return(null==(e=Wt.useContext(ju))?void 0:e.id)||null},zu=()=>Wt.useContext(Gu),qu=xt("safe-polygon");let Ku=0,Wu=new WeakMap,Xu=new WeakSet,Qu={},$u=0;const Zu=e=>e&&(e.host||Zu(e.parentNode)),Ju=()=>({getShadowRoot:!0,displayCheck:"function"==typeof ResizeObserver&&ResizeObserver.toString().includes("[native code]")?"full":"none"}),es={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"fixed",whiteSpace:"nowrap",width:"1px",top:0,left:0},ts=Wt.forwardRef(function(e,t){const[n,r]=Wt.useState();no(()=>(/apple/i.test(navigator.vendor)&&r("button"),document.addEventListener("keydown",kt),()=>{document.removeEventListener("keydown",kt)}),[]);const a={ref:t,tabIndex:0,role:n,"aria-hidden":!n||void 0,[xt("focus-guard")]:"",style:es};return Wt.createElement("span",Rt({},e,a))}),ns=Wt.createContext(null),rs="data-floating-ui-focusable";let as=[];const is=Wt.forwardRef(function(e,t){return Wt.createElement("button",Rt({},e,{type:"button",ref:t,tabIndex:-1,style:es}))}),os={pointerdown:"onPointerDown",mousedown:"onMouseDown",click:"onClick"},ls={pointerdown:"onPointerDownCapture",mousedown:"onMouseDownCapture",click:"onClickCapture"},us=e=>{var t,n;return{escapeKey:"boolean"==typeof e?e:null!=(t=null==e?void 0:e.escapeKey)&&t,outsidePress:"boolean"==typeof e?e:null==(n=null==e?void 0:e.outsidePress)||n}},ss="active",cs="selected",ds=new Map([["select","listbox"],["combobox","listbox"],["label",!1]]),ms=(0,Wt.forwardRef)(function({options:e,name:t,onChange:n,id:r,...a},i){const{selectedOptionValue:o,selectRef:l}=nu(),u=Tt([i,l]);return Qt().createElement("select",{id:`hidden-${r}`,ref:u,name:t,value:o,className:"brui-sr-only","aria-hidden":"true",tabIndex:-1,onChange:n,...a},Qt().createElement("option",{value:""}),e.map(e=>Qt().createElement("option",{key:e.id,value:e.value})))}),ps="brui-text-left brui-font-normal brui-text-darkblue focus:brui-text-darkblue brui-text-14 brui-leading-18 disabled:hover:brui-bg-gray-5 hover:brui-bg-gray-6 brui-rounded disabled:brui-border-gray-3 brui-h-44 brui-w-full brui-py-13 brui-px-10 brui-border-2 brui-flex brui-items-center brui-justify-between focus-visible:brui-outline-1 focus-visible:brui-outline-blue-2 focus-visible:brui-outline-offset-4",bs=({"aria-controls":e,id:t,"aria-labelledby":n,"aria-describedby":r,className:a,hasError:i,"aria-required":o})=>{const{selectedOptionValue:l,buttonRef:u,options:s,isOpen:c,selectedOptionDisplayName:d,navigatorIndex:m,setIsNavigating:p,setSelectedOptionValue:b,setSelectedOptionDisplayName:f,setIsOpen:E,setNavigatorIndex:y,selectRef:_}=nu(),g=e=>{const t=document.getElementById(`${e}`);t&&t.scrollIntoView({behavior:"auto",block:"nearest"})},[v]=(0,Wt.useState)("");return(0,Wt.useEffect)(()=>{if(v){const e=s.findIndex(e=>e.displayName.toString().toLowerCase().startsWith(v.toLowerCase()));e>-1&&(y(e),p(!0),g(s[e].id))}},[v]),Qt().createElement("button",{id:t,ref:u,type:"button",onClick:()=>{E(!c)},onKeyDown:e=>{let t;if("ArrowDown"===e.key||"ArrowUp"===e.key)if(e.preventDefault(),E(!0),p(!0),"ArrowDown"===e.key){t=null===m?0:m+1;const e=s.length-1,n=t>e;y(t>e?e:t),g(s[n?e:t].id)}else"ArrowUp"===e.key&&(t=null===m?0:m-1,y(t<0?0:t),g(s[t<0?0:t].id));else if("Enter"===e.key&&c&&null!==m)l!==s[m].value&&(b(s[m].value),f(s[m].displayName),setTimeout(()=>{if(_.current){const e=new Event("change",{bubbles:!0});_.current.dispatchEvent(e)}},0)),p(!1);else if("Escape"===e.key){E(!1),p(!1);const e=s.findIndex(e=>e.value===l);y(e)}},onBlur:()=>{E(!1)},"aria-controls":e,"aria-haspopup":"listbox","aria-expanded":c,"aria-labelledby":n,"aria-describedby":r,"aria-activedescendant":c&&s[m]?s[m].id:void 0,role:"combobox",className:[ps,a,i?"brui-border-red focus:brui-border-red":"brui-border-gray-7 focus:brui-text-gray-9"].join(" ").trim(),"aria-required":o},d,Qt().createElement(Eo,{className:["brui-text-15 brui-ml-auto brui-text-blue"].join(" ").trim(),iconClass:"bi_brui",iconName:c?"bi_small_select_trigger_half_open":"bi_small_select_trigger_half_f"}))},fs="brui-bg-white brui-text-14 brui-leading-18 brui-rounded-b-4 brui-shadow-2sm brui-w-full brui-z-[1001] brui-overflow-y-auto brui-border-gray-4 brui-max-h-[230px]",Es=({id:e,children:t,dropDownHeight:n})=>{const{isOpen:r}=nu(),{width:a}=co();return Qt().createElement("div",{id:e,className:"brui-absolute brui-w-full brui-top-[44px] brui-z-[1001]"},Qt().createElement("ul",{role:"listbox",className:[fs,r?"brui-absolute":"brui-hidden"].join(" ").trim(),style:n?po(a,n):void 0},t))},ys=(0,Wt.forwardRef)(function({defaultValue:e,placeHolder:t,children:n,"aria-labelledby":r,"aria-describedby":a,name:i,id:o,onChange:l,className:u,hasError:s,errorMessage:c,"aria-required":d,disableDropdownIcon:m,dropDownHeight:p,...b},f){const{formGroupHasError:E,formGroupErrorMessage:y,inputErrorId:_}=(0,Wt.useContext)(il)||{},g=void 0!==y?y:c,[v,N]=(0,Wt.useState)([]),[A,C]=(0,Wt.useState)(null),[h,T]=(0,Wt.useState)(e),[I,R]=(0,Wt.useState)(t),[x,S]=(0,Wt.useState)(!1),[O,M]=(0,Wt.useState)(!1),L=(0,Wt.useRef)(null),D=(0,Wt.useRef)(null),B=`dropdown-${(0,Wt.useId)()}`;(0,Wt.useEffect)(()=>{const t=v.findIndex(t=>t.value===e);e&&v.length>0&&t>-1&&(T(v[t].value),C(t),R(v[t].displayName))},[v,e]);const P=(0,Wt.useMemo)(()=>a||_?_?[a||"",_].join(" ").trim():[a||"",`${o}-error`].join(" ").trim():"",[a,_,o]);return Qt().createElement(ru.Provider,{value:{selectedOptionValue:h,selectedOptionDisplayName:I,isOpen:x,buttonRef:L,selectRef:D,isNavigating:O,options:v,navigatorIndex:A,setSelectedOptionValue:T,setSelectedOptionDisplayName:R,setIsOpen:S,setIsNavigating:M,setNavigatorIndex:C,initializeOptions:e=>{N(t=>[...t,e])}}},Qt().createElement("div",{className:"brui-relative"},Qt().createElement(bs,{"aria-controls":B,id:o,"aria-labelledby":r,"aria-describedby":P,className:u,hasError:E||s,"aria-required":d,disableDropdownIcon:m}),Qt().createElement(Es,{id:B,dropDownHeight:p||void 0},n),Qt().createElement(ms,{options:v,id:o,name:i,onChange:l,ref:f,...b}),void 0===E&&s&&g&&Qt().createElement(Uo,{id:_||`${o}-error`,iconClass:"bi_error_bl_bg_cf",iconName:"bi_brui",errorMessage:g||""})))}),_s=ys,gs=({displayName:e,value:t,id:n,...r})=>{const{selectedOptionValue:a,buttonRef:i,selectRef:o,isNavigating:l,options:u,navigatorIndex:s,setSelectedOptionValue:c,setSelectedOptionDisplayName:d,setIsOpen:m,setNavigatorIndex:p,initializeOptions:b}=nu();(0,Wt.useEffect)(()=>{u.findIndex(e=>e.value===t)<0&&b({value:t,id:n,displayName:e})},[]);const f=["brui-px-18 brui-py-10 hover:brui-bg-gray-1",a===t&&!l||l&&u[s].id===n?"brui-bg-gray-1":""].join(" ").trim();return Qt().createElement("li",{value:t,id:n,onMouseDown:()=>{((e,t)=>{if(a!==e){c(e),d(t);const n=u.findIndex(t=>t.value===e);p(n),setTimeout(()=>{if(o.current){const e=new Event("change",{bubbles:!0});o.current.dispatchEvent(e)}},0)}m(!1),setTimeout(()=>{i.current?.focus()},1)})(t,e)},role:"option",className:f,...r},e)},vs=({elementType:e,leftButton:t,heading:n,rightButton:r,centerMobile:a})=>{if(e&&"div"!==(i=e)&&"header"!==i)throw new Error(`Invalid elementType: ${e}. Must be "div" or "header".`);var i;const o=e||"header",l=(0,Wt.useMemo)(()=>a?"brui-text-center max-sm:brui-absolute max-sm:brui-left-1/2 max-sm:brui-top-1/2 max-sm:brui-transform max-sm:-brui-translate-x-1/2 max-sm:-brui-translate-y-1/2":"brui-ps-16 sm:brui-ps-0",[a]);return Qt().createElement(o,{className:"brui-bg-blue"},Qt().createElement(Wo,null,Qt().createElement("div",{className:"brui-h-[54px] sm:brui-h-[75px] brui-flex brui-flex-row brui-items-center brui-text-white brui-relative brui-py-5"},Qt().createElement("div",{className:"sm:brui-basis-1/3"},t),Qt().createElement("div",{className:["sm:brui-basis-1/3 sm:brui-text-center",l].join(" ").trim()},n),Qt().createElement("div",{className:"sm:brui-basis-1/3 brui-hidden sm:brui-block brui-text-right"},r))))},Ns=({elementType:e,children:t,className:n,...r})=>{if(e&&"div"!==(a=e)&&"footer"!==a)throw new Error(`Invalid elementType: ${e}. Must be "div" or "footer".`);var a;const i=e||"footer";return Qt().createElement(i,{...r},Qt().createElement(Wo,null,Qt().createElement("div",{className:n},t)))},As=({text:e,variant:t,className:n,children:r,...a})=>{const i=(0,Wt.useMemo)(()=>({tagStyle:["brui-font-semibold brui-text-12 brui-leading-14 brui-rounded-6 brui-py-4 brui-px-8",{solidRedTag:"brui-bg-red brui-text-white",solidBlueTag:"brui-bg-blue brui-text-white",solidWhiteTag:"brui-bg-white brui-text-red",solidGreyTag:"brui-bg-gray-4 brui-text-white",solidBlackTag:"brui-bg-darkblue brui-text-white",solidGradientDarkBlueTag:"brui-bg-gradient-to-l brui-from-darkblue brui-from-0% brui-to-blue brui-to-100% brui-text-white",outlinedBlackTag:"brui-text-black brui-bg-transparent brui-border-1 brui-border-solid brui-border-black",outlinedWhiteTag:"brui-text-white brui-bg-transparent brui-border-1 brui-border-solid brui-border-white",outlinedRedTag:"brui-text-red brui-bg-transparent brui-border-1 brui-border-solid brui-border-red"}[t],n].join(" ").trim()}),[t]);return Qt().createElement("span",{className:i.tagStyle,...a},e," ",r)},Cs=(0,Wt.createContext)({activeTabId:"",updateActiveTabID:()=>{},mode:"manual",isFocusableTabPanel:!1}),hs=({mode:e,children:t,isFocusableTabPanel:n,...r})=>{const[a,i]=(0,Wt.useState)("");return Qt().createElement(Cs.Provider,{value:{activeTabId:a,updateActiveTabID:e=>{i(e)},mode:e,isFocusableTabPanel:n},...r},t)},Ts="brui-inline-flex brui-w-full hover:after:brui-content-[''] hover:after:brui-w-full hover:after:brui-h-full hover:after:brui-absolute hover:after:-brui-bottom-0 hover:after:brui-border-solid hover:after:brui-border-b-4 hover:after:brui-border-blue focus:!brui-outline-blue focus:!brui-outline focus:!brui-outline-2 focus:!brui-outline-offset-3 focus:!brui-rounded-6 brui-text-blue",Is=({id:e,active:t,activeByDefault:n,useDefaultFontStyle:r=!0,className:a,onClick:i,children:o,...l})=>{const{mode:u,activeTabId:s,updateActiveTabID:c}=(0,Wt.useContext)(Cs);(0,Wt.useEffect)(()=>{n&&!s&&c(e)},[n,s,c,e]),(0,Wt.useEffect)(()=>{t&&c(e)},[t]);const d=s===e;return Qt().createElement("div",{className:"brui-inline-flex brui-relative brui-mr-24 last:brui-mr-5 brui-py-20 brui-shrink-0"},Qt().createElement("button",{id:e,className:[a,Ts,d&&"active focus:after:brui-content-[''] focus:after:brui-w-full focus:after:brui-h-full focus:after:brui-absolute focus:after:-brui-bottom-0 focus:after:brui-border-solid focus:after:brui-border-b-4 focus:after:brui-border-blue after:brui-w-full after:brui-h-full after:brui-absolute after:-brui-bottom-0 after:brui-border-solid after:brui-border-b-4 after:brui-border-blue !brui-text-black",r,r&&"brui-text-18 brui-leading-22"].join(" "),onClick:t=>{c(e),i&&i(t)},onKeyDown:e=>{if("ArrowRight"===e.key||"ArrowLeft"===e.key){e.preventDefault();const t=e.currentTarget.parentElement;if(!t)return;const n="ArrowRight"===e.key?t.nextElementSibling:t.previousElementSibling;if(!n)return;const r=Array.from(n.children).filter(e=>"BUTTON"===e.tagName),a=r.find(e=>-1===e.tabIndex);a&&(a.focus(),"automatic"===u&&a.click())}},role:"tab","aria-selected":d?"true":"false",tabIndex:d?0:-1,...l},o))},Rs=({className:e,"aria-labelledby":t,children:n})=>{const r=(0,Wt.useRef)(null);return Qt().createElement("div",{className:"brui-relative sm:-brui-mx-5"},Qt().createElement("div",{ref:r,className:"brui-tablist-overflow brui-px-5 brui-relative brui-w-full brui-overflow-x-auto brui-scroll-smooth brui-scrollbar-width-none brui-whitespace-nowrap"},Qt().createElement("div",{className:e,role:"tablist","aria-labelledby":t},n)))},xs=({id:e,tabId:t,children:n,"aria-labelledby":r,activeByDefault:a})=>{const{activeTabId:i,updateActiveTabID:o,isFocusableTabPanel:l}=(0,Wt.useContext)(Cs);(0,Wt.useEffect)(()=>{a&&!i&&o(t)},[a,i,o,t]);const u=i===t,s=l?0:void 0;return Qt().createElement("div",{id:e,role:"tabpanel",className:"focus-visible:brui-outline-blue focus-visible:brui-outline focus-visible:brui-outline-2 focus-visible:brui-outline-offset-3 focus-visible:brui-rounded-6 "+(u?"brui-block":"brui-hidden"),"aria-labelledby":r,"data-tabid":t,tabIndex:s},n)},Ss=(0,Wt.createContext)(null),Os=({placement:e="bottom",children:t})=>{const n=function(e){const[t,n]=(0,Wt.useState)(!1),[r,a]=(0,Wt.useState)(!1),i=(0,Wt.useRef)(null),{width:o}=co(),[l,u]=(0,Wt.useState)(o<768),s=function(e){void 0===e&&(e={});const{nodeId:t}=e,n=function(e){const{open:t=!1,onOpenChange:n,elements:r}=e,a=Hu(),i=Wt.useRef({}),[o]=Wt.useState(()=>function(){const e=new Map;return{emit(t,n){var r;null==(r=e.get(t))||r.forEach(e=>e(n))},on(t,n){e.set(t,[...e.get(t)||[],n])},off(t,n){var r;e.set(t,(null==(r=e.get(t))?void 0:r.filter(e=>e!==n))||[])}}}()),l=null!=Vu(),[u,s]=Wt.useState(r.reference),c=It((e,t,r)=>{i.current.openEvent=e?t:void 0,o.emit("openchange",{open:e,event:t,reason:r,nested:l}),null==n||n(e,t,r)}),d=Wt.useMemo(()=>({setPositionReference:s}),[]),m=Wt.useMemo(()=>({reference:u||r.reference||null,floating:r.floating||null,domReference:r.reference}),[u,r.reference,r.floating]);return Wt.useMemo(()=>({dataRef:i,open:t,onOpenChange:c,elements:m,events:o,floatingId:a,refs:d}),[t,c,m,o,a,d])}({...e,elements:{reference:null,floating:null,...e.elements}}),r=e.rootContext||n,a=r.elements,[i,o]=Wt.useState(null),[l,u]=Wt.useState(null),s=(null==a?void 0:a.domReference)||i,c=Wt.useRef(null),d=zu();no(()=>{s&&(c.current=s)},[s]);const m=function(e){void 0===e&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:a,elements:{reference:i,floating:o}={},transform:l=!0,whileElementsMounted:u,open:s}=e,[c,d]=Wt.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[m,p]=Wt.useState(r);Nt(m,r)||p(r);const[b,f]=Wt.useState(null),[E,y]=Wt.useState(null),_=Wt.useCallback(e=>{e!==A.current&&(A.current=e,f(e))},[]),g=Wt.useCallback(e=>{e!==C.current&&(C.current=e,y(e))},[]),v=i||b,N=o||E,A=Wt.useRef(null),C=Wt.useRef(null),h=Wt.useRef(c),T=null!=u,I=ht(u),R=ht(a),x=ht(s),S=Wt.useCallback(()=>{if(!A.current||!C.current)return;const e={placement:t,strategy:n,middleware:m};R.current&&(e.platform=R.current),((e,t,n)=>{const r=new Map,a={platform:Iu,...n},i={...a.platform,_c:r};return(async(e,t,n)=>{const{placement:r="bottom",strategy:a="absolute",middleware:i=[],platform:o}=n,l=i.filter(Boolean),u=await(null==o.isRTL?void 0:o.isRTL(t));let s=await o.getElementRects({reference:e,floating:t,strategy:a}),{x:c,y:d}=at(s,r,u),m=r,p={},b=0;for(let f=0;f<l.length;f++){const{name:n,fn:i}=l[f],{x:E,y,data:_,reset:g}=await i({x:c,y:d,initialPlacement:r,placement:m,strategy:a,middlewareData:p,rects:s,platform:o,elements:{reference:e,floating:t}});c=null!=E?E:c,d=null!=y?y:d,p={...p,[n]:{...p[n],..._}},g&&b<=50&&(b++,"object"==typeof g&&(g.placement&&(m=g.placement),g.rects&&(s=!0===g.rects?await o.getElementRects({reference:e,floating:t,strategy:a}):g.rects),({x:c,y:d}=at(s,m,u))),f=-1)}return{x:c,y:d,placement:m,strategy:a,middlewareData:p}})(e,t,{...a,platform:i})})(A.current,C.current,e).then(e=>{const t={...e,isPositioned:!1!==x.current};O.current&&!Nt(h.current,t)&&(h.current=t,Ui.flushSync(()=>{d(t)}))})},[m,t,n,R,x]);to(()=>{!1===s&&h.current.isPositioned&&(h.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[s]);const O=Wt.useRef(!1);to(()=>(O.current=!0,()=>{O.current=!1}),[]),to(()=>{if(v&&(A.current=v),N&&(C.current=N),v&&N){if(I.current)return I.current(v,N,S);S()}},[v,N,S,I,T]);const M=Wt.useMemo(()=>({reference:A,floating:C,setReference:_,setFloating:g}),[_,g]),L=Wt.useMemo(()=>({reference:v,floating:N}),[v,N]),D=Wt.useMemo(()=>{const e={position:n,left:0,top:0};if(!L.floating)return e;const t=Ct(L.floating,c.x),r=Ct(L.floating,c.y);return l?{...e,transform:"translate("+t+"px, "+r+"px)",...At(L.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,l,L.floating,c.x,c.y]);return Wt.useMemo(()=>({...c,update:S,refs:M,elements:L,floatingStyles:D}),[c,S,M,L,D])}({...e,elements:{...a,...l&&{reference:l}}}),p=Wt.useCallback(e=>{const t=Ne(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),contextElement:e}:e;u(t),m.refs.setReference(t)},[m.refs]),b=Wt.useCallback(e=>{(Ne(e)||null===e)&&(c.current=e,o(e)),(Ne(m.refs.reference.current)||null===m.refs.reference.current||null!==e&&!Ne(e))&&m.refs.setReference(e)},[m.refs]),f=Wt.useMemo(()=>({...m.refs,setReference:b,setPositionReference:p,domReference:c}),[m.refs,b,p]),E=Wt.useMemo(()=>({...m.elements,domReference:s}),[m.elements,s]),y=Wt.useMemo(()=>({...m,...r,refs:f,elements:E,nodeId:t}),[m,f,E,t,r]);return no(()=>{r.dataRef.current.floatingContext=y;const e=null==d?void 0:d.nodesRef.current.find(e=>e.id===t);e&&(e.context=y)}),Wt.useMemo(()=>({...m,context:y,refs:f,elements:E}),[m,f,E,y])}({placement:e,open:t,onOpenChange:n,whileElementsMounted:vt,middleware:[{...Ru(24),options:[24,void 0]},Du({crossAxis:e.includes("-"),fallbackAxisSideDirection:"end",padding:5}),Lu({padding:5}),Bu({element:i,padding:8})]}),c=s.context,d=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,dataRef:a,elements:{domReference:i}}=e,{enabled:o=!0,event:l="click",toggle:u=!0,ignoreMouse:s=!1,keyboardHandlers:c=!0,stickIfOpen:d=!0}=t,m=Wt.useRef(),p=Wt.useRef(!1),b=Wt.useMemo(()=>({onPointerDown(e){m.current=e.pointerType},onMouseDown(e){const t=m.current;0===e.button&&"click"!==l&&(He(t,!0)&&s||(!n||!u||a.current.openEvent&&d&&"mousedown"!==a.current.openEvent.type?(e.preventDefault(),r(!0,e.nativeEvent,"click")):r(!1,e.nativeEvent,"click")))},onClick(e){const t=m.current;"mousedown"===l&&m.current?m.current=void 0:He(t,!0)&&s||(!n||!u||a.current.openEvent&&d&&"click"!==a.current.openEvent.type?r(!0,e.nativeEvent,"click"):r(!1,e.nativeEvent,"click"))},onKeyDown(e){m.current=void 0,e.defaultPrevented||!c||Ht(e)||(" "!==e.key||Yt(i)||(e.preventDefault(),p.current=!0),"Enter"===e.key&&r(!n||!u,e.nativeEvent,"click"))},onKeyUp(e){e.defaultPrevented||!c||Ht(e)||Yt(i)||" "===e.key&&p.current&&(p.current=!1,r(!n||!u,e.nativeEvent,"click"))}}),[a,i,l,s,c,r,n,d,u]);return Wt.useMemo(()=>o?{reference:b}:{},[o,b])}(c),m=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,dataRef:a,events:i,elements:o}=e,{enabled:l=!0,delay:u=0,handleClose:s=null,mouseOnly:c=!1,restMs:d=0,move:m=!0}=t,p=zu(),b=Vu(),f=St(s),E=St(u),y=St(n),_=Wt.useRef(),g=Wt.useRef(-1),v=Wt.useRef(),N=Wt.useRef(-1),A=Wt.useRef(!0),C=Wt.useRef(!1),h=Wt.useRef(()=>{}),T=Wt.useRef(!1),I=Wt.useCallback(()=>{var e;const t=null==(e=a.current.openEvent)?void 0:e.type;return(null==t?void 0:t.includes("mouse"))&&"mousedown"!==t},[a]);Wt.useEffect(()=>{function e(e){let{open:t}=e;t||(clearTimeout(g.current),clearTimeout(N.current),A.current=!0,T.current=!1)}if(l)return i.on("openchange",e),()=>{i.off("openchange",e)}},[l,i]),Wt.useEffect(()=>{function e(e){I()&&r(!1,e,"hover")}if(!l)return;if(!f.current)return;if(!n)return;const t=Ye(o.floating).documentElement;return t.addEventListener("mouseleave",e),()=>{t.removeEventListener("mouseleave",e)}},[o.floating,n,r,l,f,I]);const R=Wt.useCallback(function(e,t,n){void 0===t&&(t=!0),void 0===n&&(n="hover");const a=Ot(E.current,"close",_.current);a&&!v.current?(clearTimeout(g.current),g.current=window.setTimeout(()=>r(!1,e,n),a)):t&&(clearTimeout(g.current),r(!1,e,n))},[E,r]),x=It(()=>{h.current(),v.current=void 0}),S=It(()=>{if(C.current){const e=Ye(o.floating).body;e.style.pointerEvents="",e.removeAttribute(qu),C.current=!1}}),O=It(()=>!!a.current.openEvent&&["click","mousedown"].includes(a.current.openEvent.type));Wt.useEffect(()=>{function e(e){if(clearTimeout(g.current),A.current=!1,c&&!He(_.current)||d>0&&!Ot(E.current,"open"))return;const t=Ot(E.current,"open",_.current);t?g.current=window.setTimeout(()=>{y.current||r(!0,e,"hover")},t):n||r(!0,e,"hover")}function t(e){if(O())return;h.current();const t=Ye(o.floating);if(clearTimeout(N.current),T.current=!1,f.current&&a.current.floatingContext){n||clearTimeout(g.current),v.current=f.current({...a.current.floatingContext,tree:p,x:e.clientX,y:e.clientY,onClose(){S(),x(),O()||R(e,!0,"safe-polygon")}});const r=v.current;return t.addEventListener("mousemove",r),void(h.current=()=>{t.removeEventListener("mousemove",r)})}("touch"!==_.current||!we(o.floating,e.relatedTarget))&&R(e)}function i(e){O()||a.current.floatingContext&&(null==f.current||f.current({...a.current.floatingContext,tree:p,x:e.clientX,y:e.clientY,onClose(){S(),x(),O()||R(e)}})(e))}if(l&&Ne(o.domReference)){var u;const r=o.domReference;return n&&r.addEventListener("mouseleave",i),null==(u=o.floating)||u.addEventListener("mouseleave",i),m&&r.addEventListener("mousemove",e,{once:!0}),r.addEventListener("mouseenter",e),r.addEventListener("mouseleave",t),()=>{var a;n&&r.removeEventListener("mouseleave",i),null==(a=o.floating)||a.removeEventListener("mouseleave",i),m&&r.removeEventListener("mousemove",e),r.removeEventListener("mouseenter",e),r.removeEventListener("mouseleave",t)}}},[o,l,e,c,d,m,R,x,S,r,n,y,p,E,f,a,O]),no(()=>{var e,t;if(l&&n&&null!=(e=f.current)&&e.__options.blockPointerEvents&&I()){C.current=!0;const e=o.floating;if(Ne(o.domReference)&&e){const n=Ye(o.floating).body;n.setAttribute(qu,"");const r=o.domReference,a=null==p||null==(t=p.nodesRef.current.find(e=>e.id===b))||null==(t=t.context)?void 0:t.elements.floating;return a&&(a.style.pointerEvents=""),n.style.pointerEvents="none",r.style.pointerEvents="auto",e.style.pointerEvents="auto",()=>{n.style.pointerEvents="",r.style.pointerEvents="",e.style.pointerEvents=""}}}},[l,n,b,o,p,f,I]),no(()=>{n||(_.current=void 0,T.current=!1,x(),S())},[n,x,S]),Wt.useEffect(()=>()=>{x(),clearTimeout(g.current),clearTimeout(N.current),S()},[l,o.domReference,x,S]);const M=Wt.useMemo(()=>{function e(e){_.current=e.pointerType}return{onPointerDown:e,onPointerEnter:e,onMouseMove(e){function t(){A.current||y.current||r(!0,a,"hover")}const{nativeEvent:a}=e;c&&!He(_.current)||n||0===d||T.current&&e.movementX**2+e.movementY**2<2||(clearTimeout(N.current),"touch"===_.current?t():(T.current=!0,N.current=window.setTimeout(t,d)))}}},[c,r,n,y,d]),L=Wt.useMemo(()=>({onMouseEnter(){clearTimeout(g.current)},onMouseLeave(e){O()||R(e.nativeEvent,!1)}}),[R,O]);return Wt.useMemo(()=>l?{reference:M,floating:L}:{},[l,M,L])}(c,{handleClose:Vt({buffer:5})}),p=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,elements:a,dataRef:i}=e,{enabled:o=!0,escapeKey:l=!0,outsidePress:u=!0,outsidePressEvent:s="pointerdown",referencePress:c=!1,referencePressEvent:d="pointerdown",ancestorScroll:m=!1,bubbles:p,capture:b}=t,f=zu(),E=It("function"==typeof u?u:()=>!1),y="function"==typeof u?E:u,_=Wt.useRef(!1),g=Wt.useRef(!1),{escapeKey:v,outsidePress:N}=us(p),{escapeKey:A,outsidePress:C}=us(b),h=Wt.useRef(!1),T=It(e=>{var t;if(!n||!o||!l||"Escape"!==e.key)return;if(h.current)return;const a=null==(t=i.current.floatingContext)?void 0:t.nodeId,u=f?Lt(f.nodesRef.current,a):[];if(!v&&(e.stopPropagation(),u.length>0)){let e=!0;if(u.forEach(t=>{var n;null==(n=t.context)||!n.open||t.context.dataRef.current.__escapeKeyBubbles||(e=!1)}),!e)return}r(!1,function(e){return"nativeEvent"in e}(e)?e.nativeEvent:e,"escape-key")}),I=It(e=>{var t;const n=()=>{var t;T(e),null==(t=Ge(e))||t.removeEventListener("keydown",n)};null==(t=Ge(e))||t.addEventListener("keydown",n)}),R=It(e=>{var t;const n=_.current;_.current=!1;const o=g.current;if(g.current=!1,"click"===s&&o)return;if(n)return;if("function"==typeof y&&!y(e))return;const l=Ge(e),u="["+xt("inert")+"]",c=Ye(a.floating).querySelectorAll(u);let d=Ne(l)?l:null;for(;d&&!Se(d);){const e=Le(d);if(Se(e)||!Ne(e))break;d=e}if(c.length&&Ne(l)&&!l.matches("html,body")&&!we(l,a.floating)&&Array.from(c).every(e=>!we(d,e)))return;if(Ae(l)&&O){const t=l.clientWidth>0&&l.scrollWidth>l.clientWidth,n=l.clientHeight>0&&l.scrollHeight>l.clientHeight;let r=n&&e.offsetX>l.clientWidth;if(n&&"rtl"===Oe(l).direction&&(r=e.offsetX<=l.offsetWidth-l.clientWidth),r||t&&e.offsetY>l.clientHeight)return}const m=null==(t=i.current.floatingContext)?void 0:t.nodeId,p=f&&Lt(f.nodesRef.current,m).some(t=>{var n;return je(e,null==(n=t.context)?void 0:n.elements.floating)});if(je(e,a.floating)||je(e,a.domReference)||p)return;const b=f?Lt(f.nodesRef.current,m):[];if(b.length>0){let e=!0;if(b.forEach(t=>{var n;null==(n=t.context)||!n.open||t.context.dataRef.current.__outsidePressBubbles||(e=!1)}),!e)return}r(!1,e,"outside-press")}),x=It(e=>{var t;const n=()=>{var t;R(e),null==(t=Ge(e))||t.removeEventListener(s,n)};null==(t=Ge(e))||t.addEventListener(s,n)});Wt.useEffect(()=>{function e(e){r(!1,e,"ancestor-scroll")}function t(){window.clearTimeout(c),h.current=!0}function u(){c=window.setTimeout(()=>{h.current=!1},xe()?5:0)}if(!n||!o)return;i.current.__escapeKeyBubbles=v,i.current.__outsidePressBubbles=N;let c=-1;const d=Ye(a.floating);l&&(d.addEventListener("keydown",A?I:T,A),d.addEventListener("compositionstart",t),d.addEventListener("compositionend",u)),y&&d.addEventListener(s,C?x:R,C);let p=[];return m&&(Ne(a.domReference)&&(p=Be(a.domReference)),Ne(a.floating)&&(p=p.concat(Be(a.floating))),!Ne(a.reference)&&a.reference&&a.reference.contextElement&&(p=p.concat(Be(a.reference.contextElement)))),p=p.filter(e=>{var t;return e!==(null==(t=d.defaultView)?void 0:t.visualViewport)}),p.forEach(t=>{t.addEventListener("scroll",e,{passive:!0})}),()=>{l&&(d.removeEventListener("keydown",A?I:T,A),d.removeEventListener("compositionstart",t),d.removeEventListener("compositionend",u)),y&&d.removeEventListener(s,C?x:R,C),p.forEach(t=>{t.removeEventListener("scroll",e)}),window.clearTimeout(c)}},[i,a,l,y,s,n,r,m,o,v,N,T,A,I,R,C,x]),Wt.useEffect(()=>{_.current=!1},[y,s]);const S=Wt.useMemo(()=>({onKeyDown:T,[os[d]]:e=>{c&&r(!1,e.nativeEvent,"reference-press")}}),[T,r,c,d]),O=Wt.useMemo(()=>({onKeyDown:T,onMouseDown(){g.current=!0},onMouseUp(){g.current=!0},[ls[s]]:()=>{_.current=!0}}),[T,s]);return Wt.useMemo(()=>o?{reference:S,floating:O}:{},[o,S,O])}(c),b=function(e,t){var n;void 0===t&&(t={});const{open:r,floatingId:a}=e,{enabled:i=!0,role:o="dialog"}=t,l=null!=(n=ds.get(o))?n:o,u=Hu(),s=null!=Vu(),c=Wt.useMemo(()=>"tooltip"===l||"label"===o?{["aria-"+("label"===o?"labelledby":"describedby")]:r?a:void 0}:{"aria-expanded":r?"true":"false","aria-haspopup":"alertdialog"===l?"dialog":l,"aria-controls":r?a:void 0,..."listbox"===l&&{role:"combobox"},..."menu"===l&&{id:u},..."menu"===l&&s&&{role:"menuitem"},..."select"===o&&{"aria-autocomplete":"none"},..."combobox"===o&&{"aria-autocomplete":"list"}},[l,a,s,r,u,o]),d=Wt.useMemo(()=>{const e={id:a,...l&&{role:l}};return"tooltip"===l||"label"===o?e:{...e,..."menu"===l&&{"aria-labelledby":u}}},[l,a,u,o]),m=Wt.useCallback(e=>{let{active:t,selected:n}=e;const r={role:"option",...t&&{id:a+"-option"}};switch(o){case"select":return{...r,"aria-selected":t&&n};case"combobox":return{...r,...t&&{"aria-selected":!0}}}return{}},[a,o]);return Wt.useMemo(()=>i?{reference:c,floating:d,item:m}:{},[i,c,d,m])}(c),f=function(e){void 0===e&&(e=[]);const t=e.map(e=>null==e?void 0:e.reference),n=e.map(e=>null==e?void 0:e.floating),r=e.map(e=>null==e?void 0:e.item),a=Wt.useCallback(t=>jt(t,e,"reference"),t),i=Wt.useCallback(t=>jt(t,e,"floating"),n),o=Wt.useCallback(t=>jt(t,e,"item"),r);return Wt.useMemo(()=>({getReferenceProps:a,getFloatingProps:i,getItemProps:o}),[a,i,o])}([d,p,b,m]),E=e=>{a(e)};return(0,Wt.useEffect)(()=>{const e=o<768;u(e),n((r||t)&&!e),E((r||t)&&e)},[o]),{popoverIsOpen:t,setPopoverIsOpen:n,arrowRef:i,modalIsOpen:r,toggleModal:E,isMobile:l,...f,...s}}(e);return Qt().createElement(Ss.Provider,{value:n},t)},Ms=()=>{const e=(0,Wt.useContext)(Ss);if(null==e)throw new Error("Popover components must be wrapped in <Popover />");return e},Ls=(0,Wt.forwardRef)(function({children:e,ariaLabel:t="More info",className:n,disabled:r,...a},i){const o=Ms(),l=Tt([o.refs.setReference,i]),u=(0,Wt.useMemo)(()=>({popover:{ref:l,"data-state":o.popoverIsOpen?"open":"closed",...o.getReferenceProps(),"aria-haspopup":!1},modal:{ref:i,onClick:()=>o.toggleModal(!0)}}),[o,l,i]);return Qt().createElement(Qt().Fragment,null,Qt().createElement(xo,{type:"button",disabled:r,"aria-disabled":r,...a,variant:"icon",size:"default","aria-label":t,className:["disabled:brui-pointer-events-none",n].join(" ").trim(),...o.isMobile?u.modal:u.popover},e))}),Ds=Ls,Bs=(0,Wt.forwardRef)(function({children:e,className:t,ariaLabelledby:n="MoreInfo"},r){const{context:a,...i}=Ms(),o=Tt([i.refs.setFloating,r]),l=(0,Wt.useMemo)(()=>"right"===a.placement?"-brui-mr-1":"left"===a.placement?"-brui-ml-1":"",[a]);return Qt().createElement(Qt().Fragment,null,a.open&&Qt().createElement(Ft,{context:a,modal:!1,disabled:!0},Qt().createElement("div",{ref:o,style:i.floatingStyles,...i.getFloatingProps(),role:"none",className:["brui-border brui-border-gray-8 brui-p-25 brui-w-[290px] brui-hidden sm:brui-block brui-shadow-2sm brui-bg-white brui-z-[1000]",t].join(" ").trim()},Qt().createElement(Yu,{ref:i.arrowRef,context:a,width:30,height:15,fill:"white",stroke:"#e1e1e1",strokeWidth:1,tipRadius:0,className:l}),e)),i.modalIsOpen&&Qt().createElement(Tl,{"aria-labelledby":n,onEscapeKeyPressed:()=>i.toggleModal(!1),onOverlayClick:()=>i.toggleModal(!1)},Qt().createElement(Ml,{verticalAlignment:{mobile:"center",tablet:"center",desktop:"center"},useDefaultRadius:!1,className:"brui-rounded-10"},Qt().createElement(Rl,null,Qt().createElement(xo,{variant:"icon",size:"default",className:"brui-absolute brui-top-15 brui-right-15  brui-leading-0",onClick:()=>i.toggleModal(!1)},Qt().createElement(Eo,{className:"brui-text-20 brui-text-blue-1",iconClass:"bi_brui",iconName:"bi_close"}),Qt().createElement(Ao,null,"Close modal")),Qt().createElement("div",{className:"brui-w-full brui-pt-15 -brui-mb-5"},e)))))}),Ps=Bs,ks={Accordion:oo,AccordionContent:fo,AccordionIcon:yo,AccordionItem:uo,AccordionToggleTitle:_o,AccordionTrigger:go,Alert:ho,Button:xo,Card:No,Carousel:Oo,CarouselArrows:Mo,CarouselContent:Lo,CarouselItem:Do,CarouselPagination:Bo,Checkbox:Ho,CheckboxCard:zo,CheckboxCardBody:qo,CheckboxCardInput:jo,CheckboxCardPrice:Ko,Container:Wo,Divider:Xo,DockBar:$o,Fixed:Zo,FormControl:al,FormGroup:ol,FooterLegal:nl,Heading:ul,HeadingStep:sl,Icon:Eo,IconLink:bl,InputError:Uo,InputText:yl,Label:gl,Link:pl,ListItem:el,Modal:Tl,ModalBody:Rl,ModalContent:Ml,ModalFooter:Dl,ModalHeader:Pl,Portal:Cl,Price:Fl,RadioButton:Gl,RadioCard:Wl,DynamicRadioContent:Xl,RadioCardBody:Ql,RadioCardInput:zl,RadioCardPrice:$l,SameHeightGroup:Jl,SameHeightItem:eu,Select:_s,SelectContext:ru,SelectCustom:bs,SelectDropdown:Es,SelectNativeHidden:ms,SelectOption:gs,SimpleHeader:vs,SimpleFooter:Ns,SrOnly:Ao,Static:Jo,Tag:As,Tab:Is,Tabs:hs,TabList:Rs,TabPanel:xs,Text:wo,useHeightResizeObserver:mo,useKeyboardListener:so,useResponsiveHeight:po,useWindowResize:co,useBodyHeightObserver:bo,Popover:Os,PopoverContent:Ps,PopoverTrigger:Ds};return Kt})(),e.exports=r(n(1),n(11))},function(e){"use strict";e.exports=d}],m={};return e.n=function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,{a:n}),n},e.d=function(t,n){for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},e.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},e.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t={},function(){"use strict";function n(e,t){function n(){this.constructor=e}if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");p(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}function r(e,t){var n,r,a={};for(n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(a[n[r]]=e[n[r]]);return a}function a(e,t,n,r){var a,i,o=arguments.length,l=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)l=Reflect.decorate(e,t,n,r);else for(i=e.length-1;i>=0;i--)(a=e[i])&&(l=(o<3?a(l):o>3?a(t,n,l):a(t,n))||l);return o>3&&l&&Object.defineProperty(t,n,l),l}function i(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function o(e,t){var n,r,a,i,o="function"==typeof Symbol&&e[Symbol.iterator];if(!o)return e;n=o.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=n.next()).done;)a.push(r.value)}catch(s){i={error:s}}finally{try{r&&!r.done&&(o=n.return)&&o.call(n)}finally{if(i)throw i.error}}return a}function l(e,t,n){if(n||2===arguments.length)for(var r,a=0,i=t.length;a<i;a++)!r&&a in t||(r||(r=Array.prototype.slice.call(t,0,a)),r[a]=t[a]);return e.concat(r||Array.prototype.slice.call(t))}function u(e,t,n){if(e===w.Mobility)return t?n.MyBill:n.Mobility;switch(e){case w.OneBill:return n.OneBill;case w.TV:return n.TV;case w.Internet:return n.Internet;case w.HomePhone:return n.HomePhone;case w.MobilityAndOneBill:return n.MobilityAndOneBill;case w.SingleBan:return n.SingleBan;default:return"Unknown"}}function s(e){switch(e){case U.VI:return"VI";case U.MC:return"MC";case U.AX:return"AX";default:return""}}function c(e){switch(e){case U.VI:return"VISA";case U.MC:return"Mastercard";case U.AX:return"American Express";case U.DC:return"Diners Club";default:return"Unknown"}}function d(e,t){var n=t.filter(function(t){return t.Ban===e});return n&&n[0].TransactionId}var m,p,b,f,E,y,_,g,v,N,A,C,h,T,I,R,x,S,O,M,L,D,B,P,k,w,U,F,H,Y,j,G,V,z,q,K,W,X,Q,$,Z,J,ee,te,ne,re,ae,ie,oe,le,ue,se,ce,de,me,pe,be,fe,Ee,ye,_e,ge,ve,Ne,Ae,Ce,he,Te,Ie,Re,xe,Se,Oe,Me,Le,De,Be,Pe,ke,we,Ue,Fe,He,Ye,je,Ge,Ve,ze,qe,Ke,We,Xe,Qe,$e,Ze,Je,et,tt,nt,rt,at,it,ot,lt,ut,st,ct,dt,mt,pt,bt,ft,Et,yt,_t,gt,vt,Nt,At,Ct,ht,Tt,It,Rt,xt,St,Ot,Mt,Lt,Dt,Bt,Pt,kt,wt,Ut,Ft,Ht,Yt,jt,Gt,Vt,zt,qt,Kt,Wt,Xt,Qt,$t,Zt,Jt,en,tn,nn,rn,an,on,ln,un,sn,cn,dn,mn,pn,bn,fn,En,yn,_n,gn,vn,Nn,An,Cn,hn,Tn,In,Rn,xn,Sn,On,Mn,Ln,Dn,Bn,Pn,kn,wn,Un,Fn,Hn,Yn,jn,Gn,Vn,zn,qn,Kn,Wn,Xn,Qn,$n,Zn,Jn,er,tr,nr,rr,ar,ir,or,lr,ur,sr,cr,dr,mr,pr,br,fr,Er,yr,_r,gr,vr,Nr,Ar,Cr,hr,Tr,Ir,Rr,xr,Sr,Or,Mr,Lr,Dr,Br,Pr,kr,wr,Ur,Fr,Hr,Yr,jr,Gr,Vr,zr,qr,Kr,Wr,Xr,Qr,$r,Zr,Jr,ea,ta,na,ra,aa,ia,oa,la,ua,sa,ca,da;e.r(t),e.d(t,{default:function(){return ca}}),m={},e.r(m),e.d(m,{OmnitureOnApiFailure:function(){return Xe},OmnitureOnBoxNameLightBox:function(){return ze},OmnitureOnConfirmation:function(){return Ge},OmnitureOnConfirmationFailure:function(){return Ke},OmnitureOnCurrentBalance:function(){return Ye},OmnitureOnFindTransactionLightBox:function(){return Ve},OmnitureOnInteracFailure:function(){return Qe},OmnitureOnLoad:function(){return Fe},OmnitureOnOneTimePaymentFailure:function(){return We},OmnitureOnPaymentSelect:function(){return He},OmnitureOnReview:function(){return je},OmnitureOnSecurityCodeLightBox:function(){return qe},cardTokenizationError:function(){return Se},cardTokenizationSuccess:function(){return Oe},clearCardNumber:function(){return xe},createMultiPaymentAction:function(){return fe},createMultiPaymentCompleted:function(){return Ee},createMultiPaymentFailed:function(){return ye},createPaymentAction:function(){return oe},createPaymentCompleted:function(){return le},createPaymentFailed:function(){return ue},fetchPaymentItems:function(){return X},fetchPaymentItemsFailed:function(){return $},getConfig:function(){return J},getInteracBankInfo:function(){return Be},getPassKey:function(){return Te},getRedirectUrl:function(){return Me},interacBankInfoFailure:function(){return ke},interacBankInfoSuccess:function(){return Pe},onCardHolderNameChange:function(){return te},onCreditCardExpiryDateChange:function(){return re},onCreditCardNumberChange:function(){return ee},onSecurityCodeChange:function(){return ne},redirectUrlFailure:function(){return De},redirectUrlSuccess:function(){return Le},resetValidationErrors:function(){return ie},setConfig:function(){return Z},setCreditCardInfo:function(){return Re},setInteractBankInfoFailure:function(){return Ue},setIsLoading:function(){return we},setPassKey:function(){return Ie},setPaymentItems:function(){return Q},setValidationErrors:function(){return ae},submitMultiOrderPaymentAction:function(){return Ne},submitMultiOrderPaymentActionCompleted:function(){return Ae},submitMultiOrderPaymentActionFailed:function(){return Ce},submitOrderPaymentAction:function(){return me},submitOrderPaymentActionCompleted:function(){return pe},submitOrderPaymentActionFailed:function(){return be},tokenizeAndPropagateFormValues:function(){return he},validateMultiOrderPaymentAction:function(){return _e},validateMultiOrderPaymentActionCompleted:function(){return ge},validateMultiOrderPaymentActionFailed:function(){return ve},validateOrderPaymentAction:function(){return se},validateOrderPaymentActionCompleted:function(){return ce},validateOrderPaymentActionFailed:function(){return de}}),p=function(e,t){return p=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},p(e,t)},b=function(){return b=Object.assign||function(e){var t,n,r,a;for(n=1,r=arguments.length;n<r;n++)for(a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},b.apply(this,arguments)},Object.create,Object.create,f=function(e){return f=Object.getOwnPropertyNames||function(e){var t,n=[];for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[n.length]=t);return n},f(e)},"function"==typeof SuppressedError&&SuppressedError,E=e(1),y=e.n(E),_=e(2),g=e(3),v=e(4),N=e(5),A=g.CommonFeatures.BaseLocalization,C=function(t){function r(){return null!==t&&t.apply(this,arguments)||this}return n(r,t),Object.defineProperty(r.prototype,"defaultMessages",{get:function(){return e(6)},enumerable:!1,configurable:!0}),a([g.Injectable],r)}(A),h=g.CommonFeatures.BaseConfig,T=g.CommonFeatures.configProperty,I=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n(t,e),a([T("en"),i("design:type",String)],t.prototype,"language",void 0),a([T(g.LoggerSeverityLevel.All),i("design:type",Number)],t.prototype,"logLevel",void 0),a([T("/UXP.Services/eCare/Ordering/Mobility/Onebill/OrderForm/PreAuthorizePayment"),i("design:type",String)],t.prototype,"createPaymentURL",void 0),a([T("/UXP.Services/eCare/Ordering/Mobility/Onebill/OrderMultiForm/PreAuthorizePayment"),i("design:type",String)],t.prototype,"createMultiPaymentURL",void 0),a([T("B"),i("design:type",String)],t.prototype,"brand",void 0),a([T("BELLCAEXT"),i("design:type",String)],t.prototype,"channel",void 0),a([T("ON"),i("design:type",String)],t.prototype,"province",void 0),a([T("b14bc3rcGo"),i("design:type",String)],t.prototype,"userID",void 0),a([T(""),i("design:type",String)],t.prototype,"CSRFToken",void 0),a([T(""),i("design:type",Array)],t.prototype,"getPaymentItem",void 0),a([T(""),i("design:type",String)],t.prototype,"pagetitle",void 0),a([T(""),i("design:type",Object)],t.prototype,"DTSTokenization",void 0),a([T(""),i("design:type",String)],t.prototype,"paymentApiUrl",void 0),a([T(""),i("design:type",Array)],t.prototype,"getBankList",void 0),a([T(""),i("design:type",Array)],t.prototype,"transactionIdArray",void 0),a([T(""),i("design:type",String)],t.prototype,"RedirectUrl",void 0),a([T(""),i("design:type",String)],t.prototype,"BankInfoUrl",void 0),a([T(""),i("design:type",String)],t.prototype,"currentUrl",void 0),a([T(""),i("design:type",Array)],t.prototype,"creditCardAutopayOffers",void 0),a([T(""),i("design:type",Array)],t.prototype,"debitCardAutopayOffers",void 0),a([T(""),i("design:type",String)],t.prototype,"IsInteracEnabled",void 0),a([T(""),i("design:type",String)],t.prototype,"IsSingleClickEnabled",void 0),a([T(""),i("design:type",String)],t.prototype,"IsAutopayCreditEnabled",void 0),a([T(""),i("design:type",String)],t.prototype,"userProfileProvince",void 0),a([T("/"),i("design:type",String)],t.prototype,"flowInitUrl",void 0),a([g.Injectable],t)}(h),R=I,function(e){e[e.Creditcard=0]="Creditcard",e[e.Debit=1]="Debit",e[e.ExistingCreditcard=2]="ExistingCreditcard"}(x||(x={})),function(e){e[e.ChangeCreditCardInfo=0]="ChangeCreditCardInfo",e[e.SwitchToBankAccount=1]="SwitchToBankAccount",e[e.UnEnroll=2]="UnEnroll"}(S||(S={})),function(e){e[e.ChangeBankAccountInfo=0]="ChangeBankAccountInfo",e[e.SwitchToCreditCard=1]="SwitchToCreditCard",e[e.UnEnroll=2]="UnEnroll"}(O||(O={})),function(e){e[e.Regular=0]="Regular",e[e.CreditCard=1]="CreditCard",e[e.PreAuthBank=2]="PreAuthBank",e[e.PreAuthCreditCard=3]="PreAuthCreditCard",e[e.Invoice=4]="Invoice",e[e.ECoupon=5]="ECoupon",e[e.Certificate=6]="Certificate"}(M||(M={})),function(e){e[e.Unknown=0]="Unknown",e[e.Success=1]="Success",e[e.Failed=2]="Failed",e[e.NetworkError=3]="NetworkError"}(L||(L={})),function(e){e[e.Unknown=0]="Unknown",e[e.Success=1]="Success",e[e.Failed=2]="Failed",e[e.NetworkError=3]="NetworkError"}(D||(D={})),function(e){e[e.Unknown=0]="Unknown",e[e.Success=1]="Success",e[e.Failed=2]="Failed",e[e.NetworkError=3]="NetworkError"}(B||(B={})),function(e){e[e.Unknown=0]="Unknown",e[e.Success=1]="Success",e[e.Failed=2]="Failed",e[e.NetworkError=3]="NetworkError"}(P||(P={})),function(e){e[e.Unknown=0]="Unknown",e[e.Active=1]="Active",e[e.Suspended=2]="Suspended",e[e.Cancelled=3]="Cancelled"}(k||(k={})),function(e){e[e.OneBill=0]="OneBill",e[e.Mobility=1]="Mobility",e[e.TV=2]="TV",e[e.Internet=3]="Internet",e[e.HomePhone=4]="HomePhone",e[e.MobilityAndOneBill=5]="MobilityAndOneBill",e[e.SingleBan=6]="SingleBan"}(w||(w={})),function(e){e[e.DC=0]="DC",e[e.VI=1]="VI",e[e.MC=2]="MC",e[e.AX=3]="AX"}(U||(U={})),function(e){e[e.OneBill=0]="OneBill",e[e.Mobility=1]="Mobility",e[e.TV=2]="TV"}(F||(F={})),function(e){e[e.Unknown=0]="Unknown",e[e.Success=1]="Success",e[e.Failed=2]="Failed",e[e.NetworkError=3]="NetworkError"}(H||(H={})),function(e){e.CardNumber="CREDIT_CARD_NUMBER",e.CardHolderName="CREDIT_CARD_HOLDER_NAME",e.ExpirationDate="CREDIT_CARD_EXPIRE_DATE",e.SecurityCode="CREDIT_CARD_SECURITY_CODE",e.BankName="BANK_NAME",e.BankAccountHolderName="BANK_ACCOUNT_HOLDER_NAME",e.BankTransitCode="BANK_TRANSIT_CODE",e.BankAccountNumber="BANK_ACCOUNT_NUM",e.ServerValidation="SERVER_VALIDATION"}(Y||(Y={})),function(e){e[e.Default=0]="Default",e[e.SelectBills=1]="SelectBills",e[e.PaymentMethod=2]="PaymentMethod",e[e.CurrentBalance=3]="CurrentBalance",e[e.TermsAndCondition=4]="TermsAndCondition",e[e.Confirmation=5]="Confirmation"}(j||(j={})),function(e){e[e.Default=0]="Default",e.SelectBills="SelectBills",e.PaymentMethod="PaymentMethod",e.CurrentBalance="CurrentBalance",e.TermsAndCondition="TermsAndCondition",e.Confirmation="Confirmation"}(G||(G={})),U.VI,V=function(){},z={CreditCardNumber:"",CreditCardNumberMasked:"",CardholderName:"",ExpireYear:"",ExpireMonth:"",SecurityCode:""},function(e){e.ONCHANGE_CREDITCARD_NUMBER="ONCHANGE_CREDITCARD_NUMBER",e.ONCHANGE_CARDHOLDER_NAME="ONCHANGE_CARDHOLDER_NAME",e.ONCHANGE_EXPIRY_MONTH="ONCHANGE_EXPIRY_MONTH",e.ONCHANGE_EXPIRY_YEAR="ONCHANGE_EXPIRY_YEAR",e.ONCHANGE_EXPIRY_DATE="ONCHANGE_EXPIRY_DATE",e.ONCHANGE_SECURITY_CODE="ONCHANGE_SECURITY_CODE",e.SET_CREDIT_CARD_DEFAULT="SET_CREDIT_CARD_DEFAULT",e.SET_CREDIT_CARD_VALIDATION="SET_CREDIT_CARD_VALIDATION",e.RESET_CREDIT_CARD_VALIDATION="RESET_CREDIT_CARD_VALIDATION"}(q||(q={})),K={PaymentMethod:"",AccountHolder:"",BankName:"",TransitNumber:"",AccountNumber:""},W={cardNumber:"",cardType:"",cardName:"",expiryDate:""},x.Debit,X=(0,N.createAction)("FETCH_PREAUTHORIZED_PAYMENT"),Q=(0,N.createAction)("SET_PREAUTHORIZED_PAYMENT"),$=(0,N.createAction)("FETCH_PREAUTHORIZED_PAYMENT_FAILED"),Z=(0,N.createAction)("SET_CONFIG"),J=(0,N.createAction)("GET_CONFIG"),ee=(0,N.createAction)(q.ONCHANGE_CREDITCARD_NUMBER),te=(0,N.createAction)(q.ONCHANGE_CARDHOLDER_NAME),ne=(0,N.createAction)(q.ONCHANGE_SECURITY_CODE),re=(0,N.createAction)(q.ONCHANGE_EXPIRY_DATE),ae=(0,N.createAction)(q.SET_CREDIT_CARD_VALIDATION),ie=(0,N.createAction)(q.RESET_CREDIT_CARD_VALIDATION),oe=(0,N.createAction)("CREATE_PAYMENT"),le=(0,N.createAction)("CREATE_PAYMENT_COMPLETED"),ue=(0,N.createAction)("CREATE_PAYMENT_FAILED"),se=(0,N.createAction)("VALIDATE_ORDER_PAYMENT"),ce=(0,N.createAction)("VALIDATE_ORDER_PAYMENT_COMPLETED"),de=(0,N.createAction)("VALIDATE_ORDER_PAYMENT_FAILED"),me=(0,N.createAction)("SUBMIT_ORDER_PAYMENT"),pe=(0,N.createAction)("SUBMIT_ORDER_PAYMENT_COMPLETED"),be=(0,N.createAction)("SUBMIT_ORDER_PAYMENT_FAILED"),fe=(0,N.createAction)("CREATE_MULTI_PAYMENT"),Ee=(0,N.createAction)("CREATE_MULTI_PAYMENT_COMPLETED"),ye=(0,N.createAction)("CREATE_MULTI_PAYMENT_FAILED"),_e=(0,N.createAction)("VALIDATE_MULTI_ORDER_PAYMENT"),ge=(0,N.createAction)("VALIDATE_MULTI_ORDER_PAYMENT_COMPLETED"),ve=(0,N.createAction)("VALIDATE_MULTI_ORDER_PAYMENT_FAILED"),Ne=(0,N.createAction)("SUBMIT_MULTI_ORDER_PAYMENT"),Ae=(0,N.createAction)("SUBMIT_MULTI_ORDER_PAYMENT_COMPLETED"),Ce=(0,N.createAction)("SUBMIT_MULTI_ORDER_PAYMENT_FAILED"),he=(0,N.createAction)("TOKENIZE_AND_PROPAGE_FORM_VALUES"),Te=(0,N.createAction)("GET_PASSKEY"),Ie=(0,N.createAction)("SET_PASSKEY"),Re=(0,N.createAction)("SET_STATE",function(e){return e.cardNumberToken&&e.cardNumberToken.length>0?e.cardNumberToken.length>16?e.maskdCardNumber=e.creditCardNumber?e.creditCardNumber.replace(/\d(?=\d{4})/g,"*"):"":e.maskdCardNumber=e.cardNumberToken.replace(/\d(?=\d{4})/g,"*"):e.maskdCardNumber="",e.expiration&&e.expiration.indexOf("/")>-1&&(e.expirationMonth=e.expiration.split("/")[0],e.expirationYear=e.expiration.split("/")[1]),e}),xe=(0,N.createAction)("CLEAR_CARD_NUMBER"),Se=(0,N.createAction)("TOKENIZATION_ERROR"),Oe=(0,N.createAction)("TOKENIZATION_SUCCESS"),Me=(0,N.createAction)("GET_REDIRECT_URL"),Le=(0,N.createAction)("GET_REDIRECT_URL_SUCCESS"),De=(0,N.createAction)("GET_REDIRECT_URL_FAILED"),Be=(0,N.createAction)("GET_INTERAC_BANK_INFO"),Pe=(0,N.createAction)("GET_INTERAC_BANK_INFO_SUCCESS"),ke=(0,N.createAction)("GET_INTERAC_BANK_INFO_FAILED"),we=(0,N.createAction)("SET_IS_LOADING"),Ue=(0,N.createAction)("RESET_FAILED_INTERACT_BANK_INFO"),Fe=(0,N.createAction)("OMNITURE_ON_LOAD"),He=(0,N.createAction)("OMNITURE_ON_PAYMENT_SELECT"),Ye=(0,N.createAction)("OMNITURE_ON_CURRENT_BALANCE"),je=(0,N.createAction)("OMNITURE_ON_REVIEW"),Ge=(0,N.createAction)("OMNITURE_ON_CONFIRMATION"),Ve=(0,N.createAction)("OMNITURE_ON_FIND_TRANSACTION_LIGHT_BOX"),ze=(0,N.createAction)("OMNITURE_ON_BOX_NAME_LIGHT_BOX"),qe=(0,N.createAction)("OMNITURE_ON_SECURITY_CODE_LIGHT_BOX"),Ke=(0,N.createAction)("OMNITURE_ON_CONFIRMATION_FAILURE"),We=(0,N.createAction)("OMNITURE_ON_ONE_TIME_PAYMENT_FAILURE"),Xe=(0,N.createAction)("OMNITURE_ON_VALIDATION_FAILURE"),Qe=(0,N.createAction)("OMNITURE_ON_INTERAC_FAILURE"),function(e){e[e.IDLE=0]="IDLE",e[e.PENDING=1]="PENDING",e[e.COMPLETED=2]="COMPLETED",e[e.FAILED=3]="FAILED"}($e||($e={})),Ze=e(7),Je=function(e,t){return e},et=function(e,t){return t.payload},tt=function(e,t){var n=t.payload;return e&&n?b(b({},e),{CreditCardNumber:n.CreditCardNumber}):e},nt=function(e,t){var n=t.payload;return e&&n?b(b({},e),{CardholderName:n.CardholderName}):e},rt=function(e,t){var n=t.payload;return e&&n?b(b({},e),{SecurityCode:n.SecurityCode}):e},at=function(e,t){var n=t.payload;return e&&n?b(b({},e),{ExpireMonth:n.ExpireMonth,ExpireYear:n.ExpireYear}):e},it=function(e,t){switch(t.type){case q.SET_CREDIT_CARD_VALIDATION:return e.errors.map(function(e){return!!t.payload.errors.some(function(t){return t.field===e.field})}).filter(function(e){return!0===e}).length>0?(e.errors.map(function(e){return t.payload.errors.find(function(t){return t.field===e.field})?b(b({},e),t.payload.errors):e}),e):b(b({},e),{errors:l(l([],o(e.errors),!1),o(t.payload.errors),!1)});case q.RESET_CREDIT_CARD_VALIDATION:return b(b({},e),{errors:[]});default:return e}},ot=function(){return function(e,t){var n=t.payload;return b(b({},e),n)}},lt=function(e){return function(t,n){return n.payload,e||$e.IDLE}},ut=function(){return function(e,t){var n=t.payload;return b(b({},e),n)}},st=function(e){return function(t,n){return n.payload,e||$e.IDLE}},ct=function(){return function(e,t){var n=t.payload;return b(b({},e),n)}},dt=function(e){return function(t,n){return n.payload,e||$e.IDLE}},mt=function(){return function(e,t){var n=t.payload;return b(b({},e),n)}},pt=function(){return function(e,t){var n=t.payload;return b(b({},e),n)}},bt=function(e,t){return t.payload},ft=function(){return function(e,t){var n=t.payload;return b(b({},e),n)}},Et=function(e){return function(t,n){return n.payload,e||$e.IDLE}},yt=function(){return function(e,t){var n=t.payload;return b(b({},e),n)}},_t=function(e){return function(t,n){return n.payload,e||$e.IDLE}},gt=function(){return function(e,t){var n=t.payload;return b(b({},e),n)}},vt=function(e){return function(t,n){return n.payload,e||$e.IDLE}},Nt=function(){return function(e,t){var n=t.payload;return b(b({},e),n)}},At=function(e){return function(t,n){return n.payload,e||$e.IDLE}},Ct=e(8),ht=g.CommonFeatures.BaseClient,Tt=function(e){function t(t,n){var r=e.call(this,t)||this;return r.config=n,r}return n(t,e),Object.defineProperty(t.prototype,"options",{get:function(){return{cache:!1,credentials:"include",headers:{"Content-Type":"application/json",brand:this.config.brand,channel:this.config.channel,Province:this.config.province,userID:this.config.userID,"accept-language":this.config.language,"X-CSRF-TOKEN":this.config.CSRFToken}}},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"optionsOneBill",{get:function(){return{cache:!1,credentials:"include",headers:{"Content-Type":"application/json",brand:this.config.brand,channel:this.config.channel,Province:this.config.province,userID:this.config.userID,"accept-language":this.config.language,"X-CSRF-TOKEN":this.config.CSRFToken,PM:!0}}},enumerable:!1,configurable:!0}),t.prototype.createMultiOrderFormData=function(e,t,n,r){var a=t?this.config.createMultiPaymentURL.replace("Onebill","".concat(e,"/").concat(null)):this.config.createMultiPaymentURL.replace("Onebill","".concat(e,"/").concat(r)),i=this.getBanSpecificTransactionId(e),o=a+"?TransactionId=".concat(i,"&province=").concat(this.config.province),l=b({},t?this.optionsOneBill:this.options);return this.post(o,{AccountInputValues:n},l)},t.prototype.validateMultiOrderForm=function(e,t,n,r,a,i,o){var l=t?this.config.createMultiPaymentURL.replace("Onebill","".concat(e,"/").concat(null)):this.config.createMultiPaymentURL.replace("Onebill","".concat(e,"/").concat(i)),u=this.getBanSpecificTransactionId(e),s=l+"/ValidatePayment?TransactionId=".concat(u,"&province=").concat(this.config.province),c=b({},t?this.optionsOneBill:this.options);return this.post(s,a?{AccountInputValues:r,ValidatePADPACInput:{SelectedPaymentMethod:n.SelectedPaymentMethod,BankName:n.BankName,AccountNumber:n.AccountNumber,HolderName:n.HolderName,BankCode:n.BankCode,TransitCode:n.TransitCode}}:{AccountInputValues:r,ValidatePADPACInput:{SelectedPaymentMethod:n.SelectedPaymentMethod,CardholderName:n.CardholderName,CreditCardToken:o,CreditCardType:n.CreditCardType,ExpiryYear:n.ExpiryYear,ExpiryMonth:n.ExpiryMonth,SecurityCode:n.SecurityCode}},c)},t.prototype.submitMultiOrderForm=function(e,t,n,r,a,i,o){var l,u,s,c=t?this.config.createMultiPaymentURL.replace("Onebill","".concat(e,"/").concat(null)):this.config.createMultiPaymentURL.replace("Onebill","".concat(e,"/").concat(o)),d=this.getBanSpecificTransactionId(e),m="".concat(c,"/Submit?TransactionId=").concat(d,"&province=").concat(this.config.province),p=b({},t?this.optionsOneBill:this.options),f=n,E=f?a:r,y=f?this.config.debitCardAutopayOffers:this.config.creditCardAutopayOffers,_=f?"D":"C";if(i&&Array.isArray(i))for(u=function(e){var t,n=i[e];E?n.incentiveDiscountDetails=[]:(t=(null!==(l=null==y?void 0:y.filter(function(e){return e.Ban===n.accountNumber}))&&void 0!==l?l:[]).reduce(function(e,t){var n;return e.concat(null!==(n=t.AutopayEligibleSubscribers)&&void 0!==n?n:[])},[]).map(function(e){var t,n,r;return{mdn:null===(t=e.subscriberTelephoneNumber)||void 0===t?void 0:t.replace(/\D/g,""),autopayOffers:null!==(r=null===(n=e.autopayOffers)||void 0===n?void 0:n.map(function(e){var t,n,r;return{newDiscountAmount:null!==(t=e.currentdiscountAmount)&&void 0!==t?t:0,currentDiscountAmount:null!==(n=e.discountAmount)&&void 0!==n?n:0,offerImpact:null!==(r=e.action)&&void 0!==r?r:""}}))&&void 0!==r?r:[]}}),n.incentiveDiscountDetails=[{autopayEligibleSubscribers:t,selectedPaymentMethod:_}])},s=0;s<i.length;s++)u(s);return this.post(m,{AccountInputValues:i},p)},t.prototype.getPassKeyRepsonse=function(e){var t=this.config.paymentApiUrl+"".concat(e.payload.ban,"/").concat(e.payload.sub,"/payment/CreditCard/PassKey"),n=b({},this.options);return this.get(t,null,n)},t.prototype.getBanSpecificTransactionId=function(e){var t=this.config.transactionIdArray.filter(function(t){return t.Ban===e});return t&&t[0].TransactionId},t.prototype.getRedirectUrl=function(){var e=this.config.RedirectUrl,t=b({},this.options);return this.post(e,{OneTimeCode:"",RedirectUrl:this.config.currentUrl},t)},t.prototype.getInteracBankInfo=function(e){var t=this.config.BankInfoUrl,n=b({},this.options);return this.post(t,{RedirectUrl:this.config.currentUrl,OneTimeCode:e},n)},t.prototype.createOrderFormData=function(e,t,n){var r=t?this.config.createPaymentURL.replace("Onebill","".concat(e)):this.config.createPaymentURL.replace("Onebill","".concat(e,"/").concat(n)),a=this.getBanSpecificTransactionId(e),i=r+"?TransactionId=".concat(a,"&province=").concat(this.config.province),o=b({},t?this.optionsOneBill:this.options);return this.post(i,null,o)},t.prototype.validateOrderForm=function(e,t,n,r,a,i){var o=t?this.config.createPaymentURL.replace("Onebill","".concat(e)):this.config.createPaymentURL.replace("Onebill","".concat(e,"/").concat(a)),l=this.getBanSpecificTransactionId(e),u=o+"/ValidatePayment?TransactionId=".concat(l,"&province=").concat(this.config.province),s=b({},t?this.optionsOneBill:this.options);return this.post(u,r?{SelectedPaymentMethod:n.SelectedPaymentMethod,BankName:n.BankName,AccountNumber:n.AccountNumber,HolderName:n.HolderName,BankCode:n.BankCode,TransitCode:n.TransitCode}:{SelectedPaymentMethod:n.SelectedPaymentMethod,CardholderName:n.CardholderName,CreditCardToken:i,CreditCardType:n.CreditCardType,ExpiryYear:n.ExpiryYear,ExpiryMonth:n.ExpiryMonth,SecurityCode:n.SecurityCode},s)},t.prototype.submitOrderForm=function(e,t,n){var r=t?this.config.createPaymentURL.replace("Onebill","".concat(e)):this.config.createPaymentURL.replace("Onebill","".concat(e,"/").concat(n)),a=this.getBanSpecificTransactionId(e),i=r+"/Submit?TransactionId=".concat(a,"&province=").concat(this.config.province),o=b({},t?this.optionsOneBill:this.options);return this.post(i,null,o)},a([g.Injectable,i("design:paramtypes",[g.AjaxServices,R])],t)}(ht),It=function(){function e(e,t){this.client=e,this.config=t}return e.prototype.combineEpics=function(){return(0,Ze.combineEpics)(this.createPaymentEpic,this.validateOrderPaymentEpic,this.submitOrderPaymentEpic,this.createMultiPaymentEpic,this.validateMultiOrderPaymentEpic,this.submitMultiOrderPaymentEpic,this.tokenizeAndPropagateFormValues,this.fetchPassKey,this.getRedirectUrl,this.getInteracBankInfo)},Object.defineProperty(e.prototype,"tokenizeAndPropagateFormValues",{get:function(){var e=this;return function(t,n){return t.pipe((0,Ze.ofType)(he.toString()),(0,Ct.mergeMap)(function(t){return(r=e.config.DTSTokenization,a=n.value.passKey,i=new DTSTokenizationPlugin,o=r.consumerId,l=r.applicationId,u=r.systemTransactionID,s=r.userID,c=r.timeout,i.setUserID(s),i.setSystemTransactionID(u),i.setApplicationID(l),i.setConsumerID(o),i.setPassKey(a),i.setPanElementID("card-number"),i.setTimeout(c),Ct.Observable.create(function(e){i.setSuccessHandler(function(t,n){e.next(t),e.complete()}),i.setErrorHandler(function(t){e.error(t),e.complete()}),i.tokenize()})).pipe((0,Ct.mergeMap)(function(e){return[Te({ban:t.payload.BillName,sub:t.payload.subscriberId}),Oe(e.token)]}),(0,Ct.catchError)(function(e){return(0,Ct.of)(Se("string"==typeof e&&e.length>0?e:"TOKENIZATIONERROR"))}));var r,a,i,o,l,u,s,c}),(0,Ct.catchError)(function(e){return(0,Ct.of)(Se("TOKENIZATIONERROR"))}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"fetchPassKey",{get:function(){var e=this;return function(t,n){return t.pipe((0,Ze.ofType)(Te.toString()),(0,Ct.mergeMap)(function(t){return(0,Ct.from)(e.client.getPassKeyRepsonse(t)).pipe((0,Ct.map)(function(e){var t;return Ie(null===(t=null==e?void 0:e.data)||void 0===t?void 0:t.PassKey)}),(0,Ct.catchError)(function(){return(0,Ct.of)(Se("TOKENIZATIONERROR"))}))}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"createMultiPaymentEpic",{get:function(){var e=this;return function(t,n){return t.pipe((0,Ze.ofType)(fe.toString()),(0,Ct.mergeMap)(function(t){var n=t.payload;return(0,Ct.from)(e.client.createMultiOrderFormData(null==n?void 0:n.ban,null==n?void 0:n.type,null==n?void 0:n.details,null==n?void 0:n.sub)).pipe((0,Ct.map)(function(e){var t=e.data;return Ee(t)}),(0,Ct.catchError)(function(e){return(0,Ct.of)(b(b({},ye(e)),{error:!0}))}))}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"validateMultiOrderPaymentEpic",{get:function(){var e=this;return function(t,n){return t.pipe((0,Ze.ofType)(_e.toString()),(0,Ct.mergeMap)(function(t){var r=t.payload;return(0,Ct.from)(e.client.validateMultiOrderForm(null==r?void 0:r.ban,null==r?void 0:r.type,null==r?void 0:r.details,null==r?void 0:r.accountInputValue,null==r?void 0:r.isBankPaymentSelected,null==r?void 0:r.sub,n.value.cardTokenizationSuccess)).pipe((0,Ct.map)(function(e){var t=e.data;return ge(t)}),(0,Ct.catchError)(function(e){return(0,Ct.of)(b(b({},ve(e)),{error:!0}))}))}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"submitMultiOrderPaymentEpic",{get:function(){var e=this;return function(t,n){return t.pipe((0,Ze.ofType)(Ne.toString()),(0,Ct.mergeMap)(function(t){var n=t.payload;return(0,Ct.from)(e.client.submitMultiOrderForm(null==n?void 0:n.ban,null==n?void 0:n.type,null==n?void 0:n.isbankSelected,null==n?void 0:n.sorryCredit,null==n?void 0:n.sorryDebit,null==n?void 0:n.details,null==n?void 0:n.sub)).pipe((0,Ct.map)(function(e){var t=e.data;return t.length>0&&t.find(function(e){return"Confirmation"===e.OrderFormStatus})?Ae(t):Ce({error:!0})}),(0,Ct.catchError)(function(e){return(0,Ct.of)(b(b({},Ce(e)),{error:!0}))}))}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"getRedirectUrl",{get:function(){var e=this;return function(t,n){return t.pipe((0,Ze.ofType)(Me.toString()),(0,Ct.mergeMap)(function(t){return(0,Ct.from)(e.client.getRedirectUrl()).pipe((0,Ct.map)(function(e){var t=e.data;return Le(t)}),(0,Ct.catchError)(function(e){return(0,Ct.of)(b(b({},De(e)),{error:!0}))}))}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"getInteracBankInfo",{get:function(){var e=this;return function(t,n){return t.pipe((0,Ze.ofType)(Be.toString()),(0,Ct.mergeMap)(function(t){var r=t.payload;return n.dispatch(we(!0)),(0,Ct.from)(e.client.getInteracBankInfo(null==r?void 0:r.code)).pipe((0,Ct.map)(function(e){var t=e.data;return n.dispatch(we(!1)),Pe(t)}),(0,Ct.catchError)(function(e){return n.dispatch(we(!1)),(0,Ct.of)(b(b({},ke(e)),{error:!0}))}))}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"createPaymentEpic",{get:function(){var e=this;return function(t,n){return t.pipe((0,Ze.ofType)(oe.toString()),(0,Ct.mergeMap)(function(t){var n=t.payload;return(0,Ct.from)(e.client.createOrderFormData(null==n?void 0:n.ban,null==n?void 0:n.type,null==n?void 0:n.sub)).pipe((0,Ct.map)(function(e){var t=e.data;return le(t)}),(0,Ct.catchError)(function(e){return(0,Ct.of)(b(b({},ue(e)),{error:!0}))}))}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"validateOrderPaymentEpic",{get:function(){var e=this;return function(t,n){return t.pipe((0,Ze.ofType)(se.toString()),(0,Ct.mergeMap)(function(t){var r=t.payload;return(0,Ct.from)(e.client.validateOrderForm(null==r?void 0:r.ban,null==r?void 0:r.type,null==r?void 0:r.details,null==r?void 0:r.isBankPaymentSelected,null==r?void 0:r.sub,n.value.cardTokenizationSuccess)).pipe((0,Ct.map)(function(e){var t=e.data;return ce(t)}),(0,Ct.catchError)(function(e){return(0,Ct.of)(b(b({},de(e)),{error:!0}))}))}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"submitOrderPaymentEpic",{get:function(){var e=this;return function(t,n){return t.pipe((0,Ze.ofType)(me.toString()),(0,Ct.mergeMap)(function(t){var n=t.payload;return(0,Ct.from)(e.client.submitOrderForm(null==n?void 0:n.ban,null==n?void 0:n.type,null==n?void 0:n.sub)).pipe((0,Ct.map)(function(e){var t=e.data;return pe(t)}),(0,Ct.catchError)(function(e){return(0,Ct.of)(b(b({},be(e)),{error:!0}))}))}))}},enumerable:!1,configurable:!0}),a([g.Injectable,i("design:paramtypes",[Tt,R])],e)}(),Rt=g.CommonFeatures.BaseStore,xt=(0,g.CommonFeatures.actionsToComputedPropertyName)(m),St=xt.setConfig,Ot=xt.getConfig,Mt=xt.onCreditCardNumberChange,Lt=xt.onCardHolderNameChange,Dt=xt.onCreditCardExpiryDateChange,Bt=xt.onSecurityCodeChange,Pt=xt.setValidationErrors,kt=xt.resetValidationErrors,wt=xt.createPaymentAction,Ut=xt.createPaymentCompleted,Ft=xt.createPaymentFailed,Ht=xt.validateOrderPaymentAction,Yt=xt.validateOrderPaymentActionCompleted,jt=xt.validateOrderPaymentActionFailed,Gt=xt.submitOrderPaymentAction,Vt=xt.submitOrderPaymentActionCompleted,zt=xt.submitOrderPaymentActionFailed,qt=xt.createMultiPaymentAction,Kt=xt.createMultiPaymentCompleted,Wt=xt.createMultiPaymentFailed,Xt=xt.validateMultiOrderPaymentAction,Qt=xt.validateMultiOrderPaymentActionCompleted,$t=xt.validateMultiOrderPaymentActionFailed,Zt=xt.submitMultiOrderPaymentAction,Jt=xt.submitMultiOrderPaymentActionCompleted,en=xt.submitMultiOrderPaymentActionFailed,tn=xt.setPassKey,nn=xt.cardTokenizationError,rn=xt.cardTokenizationSuccess,an=xt.tokenizeAndPropagateFormValues,on=xt.redirectUrlSuccess,ln=xt.interacBankInfoSuccess,un=xt.setIsLoading,sn=xt.interacBankInfoFailure,cn=function(e){function t(t,n,r,a){var i=e.call(this,t)||this;return i.config=n,i.localization=r,i.epics=a,i}return n(t,e),Object.defineProperty(t.prototype,"reducer",{get:function(){var e,t,n,r,a,i,o,l,u,s,c,d,m,p,b,f,E,y,_,g,A,C,h;return(0,v.combineReducers)({localization:this.localization.createReducer(),config:(0,N.handleActions)((e={},e[St]=et,e[Ot]=Je,e),this.config),creditCardDetails:(0,N.handleActions)((t={},t[Mt]=tt,t[Lt]=nt,t[Bt]=rt,t[Dt]=at,t),z),validationErrors:(0,N.handleActions)((n={},n[Pt]=it,n[kt]=it,n),{errors:[]}),createPaymentStatus:(0,N.handleActions)((r={},r[wt]=lt($e.PENDING),r[Ft]=lt($e.FAILED),r[Ut]=lt($e.COMPLETED),r),$e.IDLE),createPayment:(0,N.handleActions)((a={},a[Ut]=ot(),a),{}),validateOrderFormStatus:(0,N.handleActions)((i={},i[Ht]=st($e.PENDING),i[jt]=st($e.FAILED),i[Yt]=st($e.COMPLETED),i),$e.IDLE),validateOrderPayment:(0,N.handleActions)((o={},o[Yt]=ut(),o),{}),submitOrderFormStatus:(0,N.handleActions)((l={},l[Gt]=dt($e.PENDING),l[zt]=dt($e.FAILED),l[Vt]=dt($e.COMPLETED),l),$e.IDLE),submitOrderPayment:(0,N.handleActions)((u={},u[Vt]=ct(),u),{}),createMultiPaymentStatus:(0,N.handleActions)((s={},s[qt]=Et($e.PENDING),s[Wt]=Et($e.FAILED),s[Kt]=Et($e.COMPLETED),s),$e.IDLE),createMultiPayment:(0,N.handleActions)((c={},c[Kt]=ft(),c),{}),validateMultiOrderFormStatus:(0,N.handleActions)((d={},d[Xt]=_t($e.PENDING),d[$t]=_t($e.FAILED),d[Qt]=_t($e.COMPLETED),d),$e.IDLE),validateMultiOrderPayment:(0,N.handleActions)((m={},m[Yt]=yt(),m),{}),submitMultiOrderFormStatus:(0,N.handleActions)((p={},p[Zt]=vt($e.PENDING),p[en]=vt($e.FAILED),p[Jt]=vt($e.COMPLETED),p),$e.IDLE),submitMultiOrderPayment:(0,N.handleActions)((b={},b[Jt]=gt(),b),{}),passKey:(0,N.handleActions)((f={},f[tn]=function(e,t){return t.payload||e},f),""),cardTokenizationError:(0,N.handleActions)((E={},E[nn]=function(e,t){return t.payload},E),""),cardTokenizationSuccess:(0,N.handleActions)((y={},y[rn]=function(e,t){return t.payload},y),""),tokenizeAndPropagateFormValuesStatus:(0,N.handleActions)((_={},_[an]=At($e.PENDING),_[nn]=At($e.FAILED),_[rn]=At($e.COMPLETED),_),$e.IDLE),redirectUrl:(0,N.handleActions)((g={},g[on]=mt(),g),{status:"",externalRedirectUrl:""}),interacBankInfo:(0,N.handleActions)((A={},A[ln]=pt(),A),{status:"",bankAccountNumber:"",transitNumber:"",bankCode:"",accountHolderName:""}),interactBankFailureInfo:(0,N.handleActions)((C={},C[sn]=Nt(),C),{}),isLoading:(0,N.handleActions)((h={},h[un]=bt,h),!1)})},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"middlewares",{get:function(){return(0,Ze.combineEpics)(this.epics.combineEpics())},enumerable:!1,configurable:!0}),a([g.Injectable,i("design:paramtypes",[g.Store,R,C,It])],t)}(Rt),dn=cn,mn=e(9),pn=e(10),bn=(0,E.forwardRef)(function(e,t){var n=e.id,r=e.name,a=e.defaultChecked,i=e.label,o=e.headingLevel,l=void 0===o?"h4":o,u=e.children,s=e.ariaDescribe,c=e.onChange;return y().createElement(y().Fragment,null,y().createElement("div",{className:"brui-rounded-20 brui-border brui-border-gray-8 transition-all brui-mb-15 brui-drop-shadow-none has-[input[type=radio].brui-size-full:checked]:payment-shadow-3sm"},y().createElement("label",{className:"payment-peer/paymentradio payment-group/paymentradio brui-flex brui-items-center brui-p-30 payment-pl-15 sm:payment-pl-30 brui-cursor-pointer"},y().createElement(pn.RadioButton,{id:n,name:r,value:i,variant:"default",defaultChecked:a,"aria-describedby":s||void 0,ref:t,onChange:c},y().createElement("div",{className:"brui-relative brui-flex brui-justify-between"},y().createElement(pn.Heading,{level:l,variant:"default",id:n+"-label",className:"brui-font-sans brui-mb-0 brui-text-16 brui-leading-20 brui-mt-3 sm:brui-mr-64 payment-text-gray group-has-[:checked]/paymentradio:payment-text-black"},i)))),y().createElement("div",{className:"payment-hidden peer-has-[input[type=radio]:checked]/paymentradio:payment-block payment-px-15 payment-pb-30 sm:payment-px-30"},y().createElement(pn.Divider,{direction:"horizontal",width:1,className:"brui-mb-30 brui-bg-gray-4"}),u)))}),fn=(0,E.forwardRef)(function(e,t){var n=e.id,r=e.label,a=e.name,i=e.describe,o=e.defaultChecked,l=void 0!==o&&o,u=e.isInterac,s=e.headingLevel,c=void 0===s?"h4":s,d=e.children,m=e.onChange,p=e.interactIconPath;return y().createElement(y().Fragment,null,y().createElement(pn.RadioCard,{className:"payment-manage-radio-bank brui-border brui-border-gray-3 payment-py-30 sm:!brui-px-30 payment-group/radiocard",id:n,name:a,defaultChecked:l,"aria-labelledby":"label-"+n,"aria-describedby":i?"desc-"+n:"",radioPlacement:"topLeft",ref:t,onChange:m},y().createElement("div",{className:"payment-pl-[34px] sm:payment-pr-[30px] brui-relative brui-flex brui-justify-between"},y().createElement(pn.Heading,{level:c,variant:"default",className:"brui-font-sans group-has-[input[type=radio]:checked]/radiocard:payment-font-bold brui-mb-10 sm:brui-mb-5 brui-text-16 brui-leading-20 payment-mt-5",id:"label-"+n},r),u&&y().createElement("img",{alt:"",className:"payment-ml-5 payment-relative sm:payment-absolute payment-right-0 payment-top-0 sm:payment-w-40 sm:payment-h-40 payment-w-32 payment-h-32",src:p})),y().createElement("div",{className:"sm:payment-pl-[34px] sm:payment-pr-[45px]"},i&&y().createElement(pn.Text,{id:"desc-"+n,elementType:"p",className:"brui-text-14 brui-leading-18 brui-text-gray"},i),y().createElement("div",{className:"brui-z-10 brui-relative"},y().createElement("div",{className:"payment-hidden group-has-[input[type=radio]:checked]/radiocard:payment-block"},d)))))}),En=function(e){var t=e.intl,n=e.setOmnitureOnFindTransactionLightBox,r=o((0,E.useState)(!1),2),a=r[0],i=r[1],l=function(e){i(e)},u=t.formatMessage({id:"MODAL_BANK_STATEMENT_DESC"}),s=t.formatMessage({id:"MODAL_TRANSIT_NUMBER_DESC"}),c=t.formatMessage({id:"MODAL_ACCOUNT_NUMBER_DESC"});return y().createElement("div",null,y().createElement(pn.Button,{variant:"textBlue",onClick:function(e){e.preventDefault(),l(!0),setTimeout(function(){n()},1e3)},size:"small",className:"payment-text-14 payment-leading-18 payment-flex"},t.formatMessage({id:"MODAL_TRANSIT_ACCOUNT_NUMBERS_TITLE"})),a&&y().createElement(pn.Modal,{id:"transit-and-account","aria-labelledby":"transit-and-account-title",onEscapeKeyPressed:function(){return l(!1)},onOverlayClick:function(){return l(!1)}},y().createElement(pn.ModalContent,{useDefaultRadius:!1,className:"payment-rounded-10"},y().createElement(pn.ModalHeader,{variant:"lightGrayBar",rightButtonIcon:"default",title:t.formatMessage({id:"MODAL_TRANSIT_ACCOUNT_NUMBERS_TITLE"}),isDefaultPadding:!1,className:"payment-px-15 sm:payment-px-30 payment-py-25",onRightButtonClicked:function(){return l(!1)},rightButtonLabel:t.formatMessage({id:"CTA_CLOSE"})}),y().createElement(pn.ModalBody,{isDefaultPadding:!1,className:"payment-px-15 sm:payment-px-30 payment-py-30"},y().createElement("div",{className:"payment-text-gray payment-text-14 payment-leading-18"},y().createElement("div",{className:"payment-flex payment-flex-col payment-justify-between"},y().createElement("div",{className:"payment-flex payment-flex-col sm:payment-flex-row"},y().createElement("div",{className:"payment-w-full sm:payment-max-w-[293px]"},y().createElement(pn.Heading,{level:"h3",variant:"default",className:"payment-font-sans payment-text-black payment-font-bold"},t.formatMessage({id:"MODAL_BANK_STATEMENT_TITLE"})),y().createElement("div",{className:"payment-pt-15",dangerouslySetInnerHTML:{__html:u}}),y().createElement("div",{className:"payment-pt-15"},y().createElement("div",{dangerouslySetInnerHTML:{__html:s}}),y().createElement("div",{dangerouslySetInnerHTML:{__html:c}}))),y().createElement("div",{className:"payment-w-full payment-pt-30 sm:payment-pt-0 sm:payment-ml-30"},y().createElement("img",{src:t.formatMessage({id:"TRANSIT_ACC_NO_PNG"}),alt:"",className:"payment-w-full"})))))))))},yn=function(e){return{setOmnitureOnFindTransactionLightBox:function(t){return e(Ve({data:t}))}}},_n=(0,_.connect)(null,yn)((0,mn.injectIntl)(En)),gn=function(e){var t=e.intl,n=e.setOmnitureOnBoxNameLightBox,r=o((0,E.useState)(!1),2),a=r[0],i=r[1],l=function(e){i(e)};return y().createElement("div",null,y().createElement(pn.Button,{variant:"textBlue",onClick:function(e){e.preventDefault(),l(!0),setTimeout(function(){n()},1e3)},size:"small",className:"payment-text-14 payment-leading-18"},t.formatMessage({id:"MODAL_NO_NAME"})),a&&y().createElement(pn.Modal,{id:"no-name-on-card","aria-labelledby":"no-name-on-card-title",onEscapeKeyPressed:function(){return l(!1)},onOverlayClick:function(){return l(!1)}},y().createElement(pn.ModalContent,{useDefaultRadius:!1,className:"payment-rounded-10"},y().createElement(pn.ModalHeader,{variant:"lightGrayBar",rightButtonIcon:"default",title:t.formatMessage({id:"MODAL_NO_NAME_TITLE"}),isDefaultPadding:!1,className:"payment-px-15 sm:payment-px-30 payment-py-25",onRightButtonClicked:function(){return l(!1)},rightButtonLabel:t.formatMessage({id:"CTA_CLOSE"})}),y().createElement(pn.ModalBody,{isDefaultPadding:!1,className:"payment-px-15 sm:payment-px-30 payment-py-30"},y().createElement("div",{className:"payment-text-gray payment-text-14 payment-leading-18"},t.formatMessage({id:"MODAL_NO_NAME_DESC"}))))))},vn=function(e){return{setOmnitureOnBoxNameLightBox:function(t){return e(ze({data:t}))}}},Nn=(0,_.connect)(null,vn)((0,mn.injectIntl)(gn)),An=function(e){var t=e.intl,n=e.setOmnitureOnSecurityCodeLightBox,r=o((0,E.useState)(!1),2),a=r[0],i=r[1],l=function(e){i(e)};return y().createElement("div",null,y().createElement(pn.Button,{variant:"textBlue",onClick:function(e){e.preventDefault(),l(!0),setTimeout(function(){n()},1e3)},size:"small",className:"payment-text-14 payment-leading-18"},t.formatMessage({id:"MODAL_SECURITY_CODE"})),a&&y().createElement(pn.Modal,{id:"what-is-security-code","aria-labelledby":"what-is-security-code-title",onEscapeKeyPressed:function(){return l(!1)},onOverlayClick:function(){return l(!1)}},y().createElement(pn.ModalContent,{useDefaultRadius:!1,className:"payment-rounded-10"},y().createElement(pn.ModalHeader,{variant:"lightGrayBar",rightButtonIcon:"default",title:t.formatMessage({id:"MODAL_SECURITY_CODE_TITLE"}),isDefaultPadding:!1,className:"payment-px-15 sm:payment-px-30 payment-py-25",onRightButtonClicked:function(){return l(!1)},rightButtonLabel:t.formatMessage({id:"CTA_CLOSE"})}),y().createElement(pn.ModalBody,{isDefaultPadding:!1,className:"payment-px-15 sm:payment-px-30 payment-py-30"},y().createElement("div",{className:"payment-text-gray payment-text-14 payment-leading-18"},y().createElement("p",null,t.formatMessage({id:"MODAL_SECURITY_CODE_DESC"})),y().createElement("div",{className:"payment-flex payment-flex-col sm:payment-flex-row payment-justify-between payment-pt-15"},y().createElement("div",{className:"payment-w-full sm:payment-max-w-[262.5px]"},y().createElement(pn.Heading,{level:"h3",variant:"default",className:"payment-font-sans payment-text-black payment-font-bold"},t.formatMessage({id:"CARD_TYPE_VISA_MASTERCARD"})),y().createElement("div",{className:"payment-pt-15"},t.formatMessage({id:"CARD_TYPE_VISA_MASTERCARD_DESC"})),y().createElement("div",{className:"payment-pt-15"},y().createElement("img",{src:t.formatMessage({id:"BACK_CC_PNG"}),alt:"",className:"payment-h-[132px]"}))),y().createElement(pn.Divider,{direction:"vertical",width:1,className:"payment-mx-30 payment-hidden sm:payment-block"}),y().createElement(pn.Divider,{direction:"horizontal",width:1,className:"payment-my-30 payment-block sm:payment-hidden"}),y().createElement("div",{className:"payment-w-full sm:payment-max-w-[262.5px]"},y().createElement(pn.Heading,{level:"h3",variant:"default",className:"payment-font-sans payment-text-black payment-font-bold"},t.formatMessage({id:"CARD_TYPE_AMERICAN_EXP"})),y().createElement("div",{className:"payment-pt-15"},t.formatMessage({id:"CARD_TYPE_AMERICAN_EXP_DESC"})),y().createElement("div",{className:"payment-pt-15"},y().createElement("img",{src:t.formatMessage({id:"FRONT_CC_PNG"}),alt:"",className:"payment-h-[130px]"})))))))))},Cn=function(e){return{setOmnitureOnSecurityCodeLightBox:function(t){return e(qe({data:t}))}}},hn=(0,_.connect)(null,Cn)((0,mn.injectIntl)(An)),Tn=function(e){var t=e.children,n=e.legends,r=e.srOnly,a=o((0,E.useState)(function(){var e=!1;return E.Children.forEach(t,function(t){if(E.isValidElement(t)){var n=t.props;n.defaultChecked&&n.showBankFieldsOnChange&&(e=!0)}}),e}),2),i=a[0],l=a[1],u=function(e){l(e)};return E.createElement("fieldset",null,E.createElement("legend",{className:"brui-sr-only"},r),E.createElement("div",{className:"brui-flex brui-flex-col"},E.createElement("div",{className:"brui-flex brui-flex-col sm:brui-flex-row"},E.createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] sm:payment-mr-30 sm:payment-text-right payment-pt-13 payment-pb-13 sm:payment-pb-0 payment-mb-10 sm:payment-mb-0"},E.createElement(pn.Label,{className:"brui-block",required:!0},n)),E.createElement("div",{className:"brui-flex brui-flex-col payment-gap-15"},E.Children.map(t,function(e,t){return E.isValidElement(e)?E.cloneElement(e,{showOnChange:u,childIndex:t}):e})))),i)},In=(0,E.forwardRef)(function(e,t){var n=e.name,r=void 0===n?"bank-payment-radio":n,a=e.value,i=e.hasError,o=(e.showOnChange,e.showBankFieldsOnChange,e.childIndex),l=e.defaultChecked,u=e.className,s=e.label,c=e.onChange,d=e.idPrefix,m=void 0===d?"":d,p=e.getExistingBankPaymentDetails,b=e.paymentDetails;return E.useEffect(function(){b&&l&&p(b||[])},[]),E.createElement(pn.RadioButton,{className:[u,"brui-flex brui-items-center brui-absolute brui-size-full brui-opacity-0 enabled:brui-cursor-pointer disabled:brui-cursor-default brui-z-10"].join(" ").trim(),id:m+"bank-payment-radio-id-"+o,name:r,value:a,variant:"boxedInMobile",hasError:i,defaultChecked:l,ref:t,onChange:c,onClick:function(){"function"==typeof p&&p(b||[])}},E.createElement("div",{className:"brui-text-14 brui-leading-18 brui-mt-3",dangerouslySetInnerHTML:{__html:s}}))}),Rn=function(e){var t;if(13===e.length||16===e.length){if(t=new RegExp("^4"),null!=e.match(t))return"VI";if(t=new RegExp("^(5[1-5]|2(22[1-9]|2[3-9][0-9]|[3-6][0-9]{2}|7[01][0-9]|720))"),null!=e.match(t)&&16===e.length)return"MC"}else if(15===e.length&&(t=new RegExp("^(34|37)"),null!=e.match(t)))return"AX";return""},xn={VISA:/^4/,MASTERCARD:/^(5[1-5]|2[2-7])/,AMEX:/^3[47]/},Sn=/^(?:[A-Za-zÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇàâäèéêëîïôœùûüÿç'-]{2,}\.?\s)+(?:[A-Za-zÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇàâäèéêëîïôœùûüÿç'-]{1}\.?\s)*[A-Za-zÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇàâäèéêëîïôœùûüÿç'-]{2,}\.?$/,On=function(e){return xn.VISA.test(e)?"VISA":xn.MASTERCARD.test(e)?"MASTERCARD":xn.AMEX.test(e)?"AMEX":"default"},Mn={mobile:{maxHeight:"374px"},tablet:{maxHeight:"259px"},desktop:{maxHeight:"259px"}},Ln="INTERNAL_SERVER_ERROR",Dn=(0,E.forwardRef)(function(e,t){var n=e.intl,r=e.isCreditCardPayment,a=void 0!==r&&r,i=e.isBankPayment,o=void 0!==i&&i,l=e.isPreAuth,u=void 0!==l&&l,s=e.paymentItems,d=e.onChange,m=e.getExistingBankPaymentDetails,p=a?n.formatMessage({id:"EXISTING_CC_TITLE"}):o?n.formatMessage({id:"EXISTING_BANK_TITLE"}):null,b="".concat(n.formatMessage({id:"SELECT_REQUIRED_LEGEND"})," ").concat(p," ");return y().createElement(y().Fragment,null,a?y().createElement(y().Fragment,null,y().createElement(Tn,{isCreditCardPayment:!0,legends:p,srOnly:b},s.filter(function(e){return e.IsOnPreauthorizedPayments&&null!=e.CreditCardDetails}).map(function(e,r){return e.CreditCardDetails&&y().createElement(In,{idPrefix:"PACC",name:"credit-card-radio",value:c(e.CreditCardDetails.CreditCardType),label:"".concat(c(e.CreditCardDetails.CreditCardType),"<br>************").concat(e.CreditCardDetails.CreditCardNumberMasked,", ").concat(n.formatMessage({id:"CREDIT_CARD_VALID"})," ").concat(e.CreditCardDetails.ExpireYear),defaultChecked:!!u||void 0,ref:t,onChange:d})}),y().createElement(In,{idPrefix:"PACC",name:"credit-card-radio",value:n.formatMessage({id:"NEW_CREDIT_ACCOUNT_LABEL"}),label:n.formatMessage({id:"NEW_CREDIT_ACCOUNT_LABEL"}),onChange:d,defaultChecked:!u&&void 0}))):o?y().createElement(y().Fragment,null,y().createElement(Tn,{isBankPayment:!0,legends:p,srOnly:"srOnlyStr"},s.filter(function(e){return e.IsOnPreauthorizedPayments&&null!=e.BankAccountDetails}).map(function(e,n){return e.BankAccountDetails&&y().createElement(In,{idPrefix:"PAD",name:"bank-card-details-radio",value:e.BankAccountDetails.BankName,label:"".concat(e.BankAccountDetails.BankName,"<br>(").concat(e.BankAccountDetails.AccountNumberMaskedDisplayView,")"),defaultChecked:n<=0||void 0,ref:t,onChange:d,getExistingBankPaymentDetails:m,paymentDetails:new Array(e)})}),y().createElement(In,{idPrefix:"PAD",name:"bank-card-details-radio",value:n.formatMessage({id:"BANK_NEW_BANK_ACCOUNT_LABEL"}),label:n.formatMessage({id:"BANK_NEW_BANK_ACCOUNT_LABEL"}),onChange:d,getExistingBankPaymentDetails:m}))):null)}),Bn=(0,mn.injectIntl)(Dn),Pn=function(e){e.target.value=e.target.value.replace(/[^0-9]/gi,"")},kn=function(){return Array.from({length:12},function(e,t){var n=t+1;return t<9&&(n="0"+(n=t+1)),n})},wn=function(){var e,t=parseInt((new Date).getFullYear().toString().substring(2)),n=t+9,r=[];for(e=t;e<=n;e++)r.push(e);return r},Un={greatNews:{className:"brui-text-15 brui-text-blue",iconClass:"bi_brui",iconName:"bi_tag_note-big"},notifCardWarning:{className:"brui-text-20 brui-text-yellow",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"},notifCardInfoAlert:{className:"brui-text-20 brui-text-blue",iconClass:"bi_brui",iconName:"bi_info_notif_small"}},Fn=function(e){var t=e.hasNotifCard,n=void 0!==t&&t,r=e.children,a=e.label,i=e.label1,o=e.label2,l=e.label3,u=e.variant;return y().createElement(pn.Card,{variant:"gray",radius:!0,className:["payment-flex payment-flex-col sm:payment-flex-row payment-p-15 payment-gap-15 payment-rounded-[16px] payment-mb-30",n?"":"payment-hidden"].join(" ").trim()},y().createElement("div",{className:"payment-flex payment-size-20 payment-items-center payment-justify-center payment-self-start sm:payment-self-start "},y().createElement(pn.Icon,{className:["",Un[u].className].join(" ").trim(),iconClass:["",Un[u].iconClass].join(" ").trim(),iconName:["",Un[u].iconName].join(" ").trim()})),y().createElement("div",{id:n?"discount-offer":"",className:"payment-flex-grow"},y().createElement("p",{className:"brui-text-14 brui-leading-18 brui-text-gray brui-mb-10"},y().createElement("span",{className:"payment-font-bold payment-text-black"},a," "),y().createElement("span",{className:"payment-text-black"},i)),r,y().createElement("div",{className:"payment-text-12 payment-text-gray payment-leading-14"},y().createElement("strong",null,o),l)))},Hn=(0,E.forwardRef)(function(e,t){var n,r,a,i,l,u,s,c,d,m,p,f,E,_,g,v,N,A,C,h=e.intl,T=e.Checked,I=e.onChange,R=e.errorBankAccountHolderName,x=e.isInteracSelected,S=e.radioCardRef,O=e.handleBankRadioManualDetailsChange,M=e.isBankManualEnterDetails,L=e.isPreauth,D=e.hasBankAccountDetails,B=e.bankitems,P=e.handleBankRadioChange,k=e.bankListInterac,w=(e.handleInteracSubmit,e.isBankChecked),U=e.inputRefs,F=e.errorBankName,H=e.errorBankTransit,Y=e.errorBankAccountNumber,j=e.radioRef,G=e.bankList,V=e.redirectUrl,z=e.interacBankInfo,q=e.checkedBillItems,K=e.interactBankFailureInfo,W=e.creditCardAutopayOffers,X=e.debitCardAutopayOffers,Q=e.language,$=e.isInteractEnabled,Z=e.IsAutopayCreditEnabled,J=o(y().useState({bankAccountHolderName:z.accountHolderName,bankAccountNumber:z.bankAccountNumber,bankTransitCode:z.transitNumber,bankCode:z.bankCode}),2),ee=J[0],te=J[1],ne=h.formatMessage({id:"BANK_ACCOUNT_LABEL"}),re=function(){var e=[];return X&&(null==X||X.map(function(t){q&&q.map(function(n){t.Ban===n.Ban&&e.push(t)})})),e},ae={label:h.formatMessage({id:"PAYMENT_METHOD"}),credits:B&&B.length>1?re():X};return y().useEffect(function(){null!=z&&"SUCCESS"===z.status&&te({bankAccountHolderName:z.accountHolderName,bankAccountNumber:z.bankAccountNumber,bankTransitCode:z.transitNumber,bankCode:z.bankCode})},[z]),y().useEffect(function(){null!=K&&"error"===K.dataType&&K.data.includes(Ln)&&(S.manualDetails&&S.manualDetails.current&&(S.manualDetails.current.checked=!0),S.interac&&S.interac.current&&(S.interac.current.checked=!1))},[K]),r=function(){return B&&B.length>1?re().reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0):X&&X.reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0)},a=function(){return B&&B.length>1?re().reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0):W&&W.reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0)},i=o(y().useState({bankAccountHolder:"",bankAccountNumber:"",bankAccountNumberMasked:"",bankTransit:"",bankCode:""}),2),l=i[0],u=i[1],s=o(y().useState([]),2),c=s[0],d=s[1],m=function(e){d(e)},y().useEffect(function(){c.length>0&&!x?c.map(function(e){u(function(t){var n,r,a,i,o;return b(b({},t),{bankAccountHolder:(null===(n=e.BankAccountDetails)||void 0===n?void 0:n.CardHolder)||"",bankAccountNumber:(null===(r=e.BankAccountDetails)||void 0===r?void 0:r.AccountNumber)||"",bankAccountNumberMasked:(null===(a=e.BankAccountDetails)||void 0===a?void 0:a.AccountNumberMasked)||"",bankTransit:(null===(i=e.BankAccountDetails)||void 0===i?void 0:i.TransitCode)||"",bankCode:(null===(o=e.BankAccountDetails)||void 0===o?void 0:o.BankCode)||""})})}):0!==c.length||x||u(function(e){return b(b({},e),{bankAccountHolder:"",bankAccountNumber:"",bankAccountNumberMasked:"",bankTransit:"",bankCode:""})})},[c]),p=function(e){var t=e.target.value;u(b(b({},l),{bankAccountHolder:t}))},f=function(e){var t=e.target.value;u(b(b({},l),{bankTransit:t}))},E=function(e){var t=e.target.value;u(b(b({},l),{bankAccountNumber:t}))},_=function(){var e;return(null===(e=null==ae?void 0:ae.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){var t,n;return(null===(t=e.eligiblePaymentMethods)||void 0===t?void 0:t.includes("C"))&&(null===(n=e.eligiblePaymentMethods)||void 0===n?void 0:n.includes("D"))})})}))||!1},g=function(e){var t=e.target.value;u(b(b({},l),{bankAccountNumberMasked:t,bankAccountNumber:t}))},v=Object.values(z).every(function(e){return""===e}),N=h.formatMessage({id:"CTA_INTERAC"}),A=h.formatMessage({id:"CTA_INTERAC_SR"}),C="ON"===$,y().createElement("div",{className:"brui-mb-15"},y().createElement(bn,{id:"payment-radio-bank",name:"payment-radio",label:ne,headingLevel:"h3",defaultChecked:!!T||void 0,ref:t,onChange:I},Z&&y().createElement("div",null,y().createElement(Fn,{hasNotifCard:r()>0,variant:"greatNews",label:h.formatMessage({id:"GREAT_NEWS"}),label1:_()?r()>1?h.formatMessage({id:"LABEL_LOADED_OFFERS_TITLE_TRUEAUTOPAY"}):h.formatMessage({id:"LABEL_LOADED_OFFER_TITLE_TRUEAUTOPAY"}):r()>1?h.formatMessage({id:"LABEL_LOADED_OFFERS_DEBIT_TITLE"}):h.formatMessage({id:"LABEL_LOADED_OFFER_DEBIT_TITLE"}),label2:h.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE_HEADING"}),label3:r()>1?h.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE"}):h.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE"})},null===(n=null==ae?void 0:ae.credits)||void 0===n?void 0:n.map(function(e,t){var n;return y().createElement(y().Fragment,{key:e.Ban||t},B&&B.length>1&&y().createElement("p",{className:"payment-text-14 payment-text-gray"},e.banInfo&&e.banInfo.nickName,":"),null===(n=e.AutopayEligibleSubscribers)||void 0===n?void 0:n.map(function(e,t){var n;return null===(n=null==e?void 0:e.autopayOffers)||void 0===n?void 0:n.map(function(t,n){return y().createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10",key:"".concat(e.subscriberTelephoneNumber,"-").concat(n)},y().createElement(pn.ListItem,{className:"payment-text-14 payment-text-gray payment-leading-18"},e.subscriberTelephoneNumber," - ",y().createElement(pn.Price,{className:"payment-text-blue payment-text-darkblue payment-inline brui-lowercase !payment-text-14 payment-font-normal",showZeroDecimalPart:!1,price:t.discountAmount,variant:"defaultPrice",suffixText:"perMonth",language:Q})))})}))})),X&&X.length>0&&0===r()&&a()>0?y().createElement(pn.Text,{role:"alert",className:"payment-bg-gray-3 payment-flex payment-flex-col sm:payment-flex-row payment-p-15 payment-gap-15 payment-rounded-[16px] payment-mb-10",elementType:"div"},y().createElement("div",{className:"payment-flex payment-size-20 payment-items-center payment-justify-center"},y().createElement(pn.Icon,{className:"payment-text-20 payment-text-blue",iconClass:"bi_brui",iconName:"bi_info_notif_small"})),y().createElement(pn.Text,{id:0===r()?"discount-offer":"",className:"payment-text-14 payment-leading-20 payment-text-gray",elementType:"p"}," ",h.formatMessage({id:"SORRY_MESSAGE"}))):""),y().createElement("div",null,y().createElement("div",{role:"radiogroup","aria-labelledby":"payment-radio-bank"},y().createElement("form",{noValidate:!0},y().createElement(pn.FormControl,null,!x&&y().createElement("div",null,C&&y().createElement("div",null,y().createElement(fn,{id:"radio-1",name:"bank-details-radio",label:h.formatMessage({id:"BANK_ACCOUNT_AUTOMATIC_LABEL"}),describe:h.formatMessage({id:"BANK_ACCOUNT_AUTOMATIC_DESCRIPTION"}),isInterac:!0,ref:S.interac,defaultChecked:!0,interactIconPath:h.formatMessage({id:"INTERAC_BOX_LOGO"}),onChange:O},y().createElement("div",null,y().createElement("ul",{className:"max-318:payment-w-auto max-318:payment-h-auto payment-gap-x-15 sm:payment-gap-x-45 payment-w-1/2 sm:payment-w-[250px] payment-flex payment-flex-wrap payment-flex-col payment-h-[90px] payment-list-disc payment-list-inside payment-mt-15 payment-ml-10"},k.map(function(e){return y().createElement(pn.ListItem,{className:"brui-text-gray brui-leading-18"},y().createElement(pn.Text,{className:"payment-mt-10 brui-text-14"}," ",e))})),y().createElement("div",{className:"payment-mt-15"},y().createElement("a",{href:V.externalRedirectUrl,onClick:function(){var e,t=new Array(q.length);q&&q.map(function(e){t.includes(e.Ban)||t.push(e.Ban)}),e=t.filter(function(e){return null!==e}),window.sessionStorage.setItem("itemsChecked",JSON.stringify(e))},className:"brui-text-15 brui-leading-17 brui-py-7 brui-px-30 brui-inline-block brui-rounded-30 \r\n                            brui-bg-blue-1 brui-text-white brui-border-blue-1 brui-border-2 enabled:hover:brui-bg-blue enabled:hover:brui-border-blue focus:brui-outline-blue-2 \r\n                            focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 disabled:brui-bg-gray-2 \r\n                            disabled:brui-text-white disabled:brui-border-gray-2",role:"button"},y().createElement("span",{"aria-hidden":"true",dangerouslySetInnerHTML:{__html:N}}),y().createElement("span",{className:"payment-sr-only",dangerouslySetInnerHTML:{__html:A}})))))),y().createElement("div",{className:"payment-mt-15"},y().createElement(fn,{id:"radio-2",name:"bank-details-radio",label:c.length>0?h.formatMessage({id:"EXISTING_BANK_ACCOUNT_MANUAL_DETAILS_LABEL"}):h.formatMessage({id:"BANK_ACCOUNT_MANUAL_DETAILS_LABEL"}),describe:h.formatMessage({id:"BANK_ACCOUNT_MANUAL_DETAILS_DESCRIPTION"}),ref:S.manualDetails,onChange:O,defaultChecked:!C||void 0},y().createElement(pn.Divider,{direction:"horizontal",width:1,className:"payment-my-30 payment-bg-gray-4"}),y().createElement("div",{className:"brui-flex brui-flex-row payment-gap-4 brui-items-center payment-mb-30 md:payment-mb-45"},y().createElement("div",{className:"brui-text-14 brui-text-gray brui-leading-18"},h.formatMessage({id:"BANK_NEED_HELP"})),y().createElement(_n,null)),y().createElement("div",null,(M||!v||!C||"error"===(null==K?void 0:K.dataType))&&y().createElement("form",{noValidate:!0},L&&D&&y().createElement(pn.FormControl,{className:"sm:brui-flex-row payment-mt-30"},y().createElement(Bn,{isCreditCardPayment:!1,isBankPayment:!0,isPreAuth:L,paymentItems:B,ref:j,onChange:P,getExistingBankPaymentDetails:m})),(w||!L&&!w||L||z)&&y().createElement(y().Fragment,null,y().createElement(pn.FormControl,{className:"sm:brui-flex-row payment-mt-30"},y().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},y().createElement(pn.Label,{id:"bank-name-label",htmlFor:"bank-name",isError:F,required:!0,className:F?"payment-error-required":""},h.formatMessage({id:"BANK_NAME_LABEL"}))),""!==l.bankCode&&y().createElement(pn.Select,{className:"sm:!payment-w-[280px] brui-text-gray",id:"bank-name",name:"select-bank-name",hasError:F,errorMessage:h.formatMessage({id:"BANK_NAME_ERROR_LABEL"}),"aria-labelledby":"bank-name-label","aria-describedby":" ",ref:U.inputBankName,defaultValue:l.bankCode,dropDownHeight:Mn,placeHolder:h.formatMessage({id:"SELECT_BANK_PLACEHOLDER"})},G.map(function(e){return""!==e.Text&&y().createElement(pn.SelectOption,{value:e.Value,id:"option-bank-".concat(e.Value),displayName:e.Text})})),""===l.bankCode&&y().createElement(pn.Select,{className:"sm:!payment-w-[280px] brui-text-gray",id:"bank-name",name:"select-bank-name",hasError:F,errorMessage:h.formatMessage({id:"BANK_NAME_ERROR_LABEL"}),"aria-labelledby":"bank-name-label","aria-describedby":" ",ref:U.inputBankName,dropDownHeight:Mn,placeHolder:h.formatMessage({id:"SELECT_BANK_PLACEHOLDER"})},G.map(function(e){return""!==e.Text&&y().createElement(pn.SelectOption,{value:e.Value,id:"option-bank-".concat(e.Value),displayName:e.Text})}))),y().createElement(pn.FormControl,{className:"sm:brui-flex-row payment-mt-30"},y().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},y().createElement(pn.Label,{htmlFor:"bank-holder-name",isError:R,required:!0,className:R?"payment-error-required":""},h.formatMessage({id:"BANK_HOLDER_NAME_LABEL"}))),y().createElement(pn.InputText,{className:"sm:!payment-w-[280px] !brui-text-gray",id:"bank-holder-name",required:!0,isError:R,errorMessage:h.formatMessage({id:"BANK_HOLDER_NAME_ERROR_LABEL"}),minLength:5,maxLength:70,ref:U.inputBankAccountHolder,value:l.bankAccountHolder,onChange:p})),y().createElement(pn.FormControl,{className:"sm:brui-flex-row payment-mt-30"},y().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},y().createElement(pn.Label,{htmlFor:"bank-transit-number",isError:H,required:!0,className:H?"payment-error-required":""},h.formatMessage({id:"BANK_TRANSIT_NUMBER_LABEL"})),y().createElement(pn.Text,{elementType:"div",className:"brui-text-12 leading-14 sm:payment-ml-14 payment-text-gray"},h.formatMessage({id:"BANK_TRANSIT_NUMBER_DESCRIPTION"}))),y().createElement(pn.InputText,{className:"!payment-w-[140px]",id:"bank-transit-number",required:!0,isError:H,errorMessage:h.formatMessage({id:"BANK_TRANSIT_ERROR_LABEL"}),minLength:5,maxLength:5,onInput:function(e){return Pn(e)},ref:U.inputTransitNumber,value:l.bankTransit,onChange:f})),y().createElement(pn.FormControl,{className:"sm:brui-flex-row payment-mt-30"},y().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},y().createElement(pn.Label,{htmlFor:"bank-account-number",isError:Y,required:!0,className:Y?"payment-error-required":""},h.formatMessage({id:"BANK_ACCOUNT_NUMBER_LABEL"})),y().createElement(pn.Text,{elementType:"div",className:"brui-text-12 leading-14 sm:payment-ml-14 payment-text-gray"},h.formatMessage({id:"BANK_ACCOUNT_NUMBER_DESCRIPTION"}))),y().createElement("div",null,c.length>0&&y().createElement(pn.InputText,{className:"!payment-w-[140px]",id:"bank-account-number",required:!0,isError:Y,minLength:5,maxLength:12,errorMessage:h.formatMessage({id:"BANK_ACCOUNT_HOLDER_NUMBER_ERROR_LABEL"}),onInput:function(e){return Pn(e)},value:l.bankAccountNumberMasked,onChange:g}),y().createElement(pn.InputText,{className:"!payment-w-[140px]",id:"bank-account-number",required:!0,isError:!(c.length>0)&&Y,minLength:5,maxLength:12,errorMessage:h.formatMessage({id:"BANK_ACCOUNT_HOLDER_NUMBER_ERROR_LABEL"}),onInput:function(e){return Pn(e)},ref:U.inputBankAccountNumber,value:l.bankAccountNumber,onChange:E,type:c.length>0?"hidden":"text"}))),y().createElement(pn.Text,{className:"brui-text-14 brui-leading-18 brui-text-gray payment-mt-30 sm:payment-mt-45 brui-inline-block"},h.formatMessage({id:"REQUIRED_LABEL"}))))))))))),x&&C&&y().createElement("div",null,y().createElement("form",{noValidate:!0},y().createElement(pn.FormControl,{className:"sm:brui-flex-row payment-mt-30"},y().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},y().createElement(pn.Label,{id:"bank-name-label",htmlFor:"bank-name",isError:!1,required:!0},h.formatMessage({id:"BANK_NAME_LABEL"}))),y().createElement(pn.Select,{className:"sm:!payment-w-[280px] brui-text-gray",id:"bank-name",name:"select-bank-name",hasError:!1,errorMessage:"Please select at least one option","aria-labelledby":"bank-name-label",ref:U.inputBankName,defaultValue:ee.bankCode,dropDownHeight:Mn},G.map(function(e){return""!==e.Text&&y().createElement(pn.SelectOption,{value:e.Value,id:"option-bank-".concat(e.Value),displayName:e.Text})}))),y().createElement(pn.FormControl,{className:"sm:brui-flex-row payment-mt-30"},y().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},y().createElement(pn.Label,{htmlFor:"bank-holder-name",isError:!1,required:!0},h.formatMessage({id:"BANK_HOLDER_NAME_LABEL"}))),y().createElement(pn.InputText,{className:"sm:!payment-w-[280px] !brui-text-gray",id:"bank-holder-name",required:!0,isError:R,errorMessage:h.formatMessage({id:"BANK_HOLDER_NAME_ERROR_LABEL"}),minLength:5,maxLength:70,ref:U.inputBankAccountHolder,value:ee.bankAccountHolderName,onChange:function(e){var t=e.target.value;te(b(b({},ee),{bankAccountHolderName:t}))}})),y().createElement(pn.FormControl,{className:"sm:brui-flex-row payment-mt-30"},y().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},y().createElement(pn.Label,{htmlFor:"bank-transit-number",isError:!1,required:!0},h.formatMessage({id:"BANK_TRANSIT_NUMBER_LABEL"})),y().createElement(pn.Text,{elementType:"div",className:"brui-text-12 leading-14 sm:payment-ml-14 payment-text-gray"},h.formatMessage({id:"BANK_TRANSIT_NUMBER_DESCRIPTION"}))),y().createElement(pn.InputText,{className:"!payment-w-[140px]",id:"bank-transit-number",required:!0,isError:!1,errorMessage:"This is required field",minLength:7,maxLength:12,onInput:function(e){return Pn(e)},ref:U.inputTransitNumber,value:ee.bankTransitCode,onChange:function(e){var t=e.target.value;te(b(b({},ee),{bankTransitCode:t}))}})),y().createElement(pn.FormControl,{className:"sm:brui-flex-row payment-mt-30"},y().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},y().createElement(pn.Label,{htmlFor:"bank-account-number",isError:!1,required:!0},h.formatMessage({id:"BANK_ACCOUNT_NUMBER_LABEL"})),y().createElement(pn.Text,{elementType:"div",className:"brui-text-12 leading-14 sm:payment-ml-14 payment-text-gray"},h.formatMessage({id:"BANK_ACCOUNT_NUMBER_DESCRIPTION"}))),y().createElement("div",null,y().createElement(pn.InputText,{className:"!payment-w-[140px]",id:"bank-account-number",required:!0,isError:!1,errorMessage:"This is required field",onInput:function(e){return Pn(e)},ref:U.inputBankAccountNumber,value:ee.bankAccountNumber,onChange:function(e){var t=e.target.value;te(b(b({},ee),{bankAccountNumber:t}))}}),y().createElement("div",{style:{marginTop:"5px"},className:"brui-flex brui-gap-10 brui-mt-5"},y().createElement("span",{style:{marginRight:"8px"},className:"bi_small_checkmark_full bi_brui brui-text-green",role:"img","aria-hidden":"true","aria-label":" "}),y().createElement("div",{id:"account-fetched",className:"brui-flex brui-flex-col brui-text-12 brui-leading-14"},y().createElement("span",{id:"account-fetched-message",className:"brui-text-black brui-font-bold"},h.formatMessage({id:"INTERAC_FETCHED_LABEL"})),y().createElement("span",{className:"brui-text-gray"},h.formatMessage({id:"INTERAC_FETCHED_SUBTITLE"})))))),y().createElement(pn.Text,{className:"brui-text-14 brui-leading-18 brui-text-gray payment-mt-30 sm:payment-mt-45 brui-inline-block"},h.formatMessage({id:"REQUIRED_LABEL"})))))))}),Yn=function(e){return{redirectUrlAction:function(){e(Me({}))}}},jn=function(e){return{redirectUrl:e.redirectUrl,interacBankInfo:e.interacBankInfo,interactBankFailureInfo:e.interactBankFailureInfo}},Gn=(0,_.connect)(jn,Yn)((0,mn.injectIntl)(Hn)),Vn=(0,E.forwardRef)(function(e,t){var n,r,a,i,l=e.intl,u=e.Checked,s=e.onChange,c=e.isPreauth,d=(e.hasCreditCardDetails,e.bankitems),m=(e.radioRef,e.handleBankRadioChange,e.isBankChecked),p=e.cardNumber,b=e.handleCreditCardChange,f=e.inputRefs,E=e.cardIcons,_=e.cardType,g=e.errorCardNumber,v=e.errorCardName,N=e.errorExpiryDate,A=e.errorSecurityCode,C=e.handleMaskCVV,h=e.CVV,T=e.creditCardAutopayOffers,I=e.debitCardAutopayOffers,R=e.checkedBillItems,x=e.language,S=e.IsAutopayCreditEnabled,O=l.formatMessage({id:"CREDIT_CARD_LABEL"}),M=o(y().useState(l.formatMessage({id:"CREDIT_CARD_YEAR_TEXT"})),2),L=M[0],D=M[1],B=o(y().useState(l.formatMessage({id:"CREDIT_CARD_MONTH_TEXT"})),2),P=B[0],k=B[1],w=l.formatMessage({id:"CREDIT_CARD_EXPIRY_DATE_SR_LABEL"},{month:P,year:L}),U=function(){var e=[];return T&&(null==T||T.map(function(t){R&&(null==R||R.map(function(n){t.Ban===n.Ban&&e.push(t)}))})),e},F={label:l.formatMessage({id:"PAYMENT_METHOD"}),credits:d&&d.length>1?U():T},H=function(){return d&&d.length>1?U().reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0):T&&T.reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0)};return y().createElement("div",{className:"brui-mb-15"},y().createElement(bn,{id:"payment-radio-credit",name:"payment-radio",label:O,headingLevel:"h3",defaultChecked:!!u||void 0,ref:t,onChange:s},S&&y().createElement("div",null,y().createElement(Fn,{hasNotifCard:H()>0,variant:"greatNews",label:l.formatMessage({id:"GREAT_NEWS"}),label1:function(){var e;return(null===(e=null==F?void 0:F.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){var t,n;return(null===(t=e.eligiblePaymentMethods)||void 0===t?void 0:t.includes("C"))&&(null===(n=e.eligiblePaymentMethods)||void 0===n?void 0:n.includes("D"))})})}))||!1}()?H()>1?l.formatMessage({id:"LABEL_LOADED_OFFERS_TITLE_TRUEAUTOPAY"}):l.formatMessage({id:"LABEL_LOADED_OFFER_TITLE_TRUEAUTOPAY"}):H()>1?l.formatMessage({id:"LABEL_LOADED_OFFERS_CREDIT_TITLE"}):l.formatMessage({id:"LABEL_LOADED_OFFER_CREDIT_TITLE"}),label2:l.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE_HEADING"}),label3:H()>1?l.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE"}):l.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE"})},null===(n=null==F?void 0:F.credits)||void 0===n?void 0:n.map(function(e,t){var n;return y().createElement(y().Fragment,{key:e.Ban||t},d&&d.length>1&&y().createElement("p",{className:"payment-text-14 payment-text-gray"},e.banInfo&&e.banInfo.nickName,":"),null===(n=e.AutopayEligibleSubscribers)||void 0===n?void 0:n.map(function(e,t){var n;return null===(n=null==e?void 0:e.autopayOffers)||void 0===n?void 0:n.map(function(t,n){return y().createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10",key:"".concat(e.subscriberTelephoneNumber,"-").concat(n)},y().createElement(pn.ListItem,{className:"payment-text-14 payment-text-gray payment-leading-18"},e.subscriberTelephoneNumber," - ",y().createElement(pn.Price,{className:"brui-text-18 brui-text-blue brui-text-darkblue payment-text-blue payment-text-darkblue payment-inline brui-lowercase !payment-text-14 payment-font-normal",showZeroDecimalPart:!1,price:t.discountAmount,variant:"defaultPrice",suffixText:"perMonth",language:x})))})}))})),T&&T.length>0&&0===H()&&(d&&d.length>1?(i=[],I&&(null==I||I.map(function(e){R&&(null==R||R.map(function(t){e.Ban===t.Ban&&i.push(e)}))})),i).reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0):I&&I.reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0))>0?y().createElement(pn.Text,{role:"alert",className:"payment-bg-gray-3 payment-flex payment-flex-col sm:payment-flex-row payment-p-15 payment-gap-15 payment-rounded-[16px] payment-mb-10",elementType:"div"},y().createElement("div",{className:"payment-flex payment-size-20 payment-items-center payment-justify-center"},y().createElement(pn.Icon,{className:"payment-text-20 payment-text-blue",iconClass:"bi_brui",iconName:"bi_info_notif_small"})),y().createElement(pn.Text,{className:"payment-text-14 payment-leading-20 payment-text-gray",elementType:"p"}," ",l.formatMessage({id:"SORRY_MESSAGE_CREDIT"}))):""),y().createElement("div",null,y().createElement("form",{noValidate:!0},(m||!c&&!m||c)&&y().createElement(y().Fragment,null,y().createElement(pn.FormControl,{className:"sm:brui-flex-row payment-mt-30"},y().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-self-start sm:payment-text-right payment-mr-0 sm:payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},y().createElement("div",null,y().createElement(pn.Label,{htmlFor:"card-number",isError:g,required:!0,className:g?"payment-error-required":""},y().createElement("span",{id:"cc-number-label",className:"brui-sr-only"},l.formatMessage({id:"CREDIT_CARD_NUMBER_SR_LABEL"})),y().createElement("span",{"aria-hidden":!0},l.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL"})))),y().createElement(pn.Text,{"aria-hidden":!!(null===(a=null===(r=f.inputCreditCardNumber)||void 0===r?void 0:r.current)||void 0===a?void 0:a.value),elementType:"div",className:"brui-text-12 leading-14 sm:brui-ml-14 brui-text-gray payment-mt-5"},l.formatMessage({id:"CREDIT_CARD_NUMBER_DESC_INPUT_LABEL"}))),y().createElement("div",{className:"brui-flex brui-flex-col sm:brui-flex-row brui-flex-wrap"},y().createElement(pn.InputText,{value:p,onChange:b,onInput:function(e){return Pn(e)},className:"sm:!payment-w-[280px] payment-mr-30",id:"card-number",required:!0,isError:g,errorMessage:l.formatMessage({id:"ERROR_CREDIT_CARD_NUMBER_INPUT_LABEL"}),ref:f.inputCreditCardNumber,"aria-labelledby":"cc-number-label"}),y().createElement("div",{className:"sm:payment-h-44 payment-ml-0 payment-mt-10 sm:payment-mt-[6px] brui-flex payment-items-baseline brui-gap-15","aria-label":l.formatMessage({id:"CC_IMAGE_SR_LABEL"}),role:"img"},Object.entries(E).map(function(e){var t=o(e,2),n=t[0],r=t[1];return y().createElement("img",{key:n,src:r,alt:"".concat(n," card"),className:"brui-h-32 payment-mr-15 brui-object-contain","aria-hidden":"true",style:{opacity:_===n?1:.5,transition:"opacity 0.3s ease-in-out"}})})))),y().createElement(pn.FormControl,{className:"sm:brui-flex-row payment-mt-30"},y().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex sm:payment-self-start brui-flex-col sm:payment-text-right payment-mr-0 sm:payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},y().createElement("div",null,y().createElement(pn.Label,{htmlFor:"text-2",isError:v,required:!0,className:v?"payment-error-required":""},y().createElement("span",{id:"cc-name-label",className:"brui-sr-only"},l.formatMessage({id:"CREDIT_CARD_NAME_SR_LABEL"})),y().createElement("span",{"aria-hidden":!0},l.formatMessage({id:"CREDIT_CARD_NAME_LABEL"})))),y().createElement(pn.Text,{elementType:"div",className:"brui-text-12 leading-14 sm:brui-ml-14 brui-text-gray"},l.formatMessage({id:"CREDIT_CARD_NAME_DESC_INPUT_LABEL"}))),y().createElement("div",{className:"brui-flex brui-flex-col sm:brui-flex-row brui-flex-wrap"},y().createElement(pn.InputText,{className:"sm:!payment-w-[280px]",id:"text-2","aria-labelledby":"cc-name-label",required:!0,isError:v,errorMessage:l.formatMessage({id:"ERROR_CREDIT_CARD_NAME_INPUT_LABEL"}),minLength:5,maxLength:70,ref:f.inputCreditCardHolderName}),y().createElement("div",{className:"brui-flex payment-items-baseline sm:payment-h-44 sm:payment-ml-10 payment-mt-5 sm:payment-mt-7"},y().createElement(Nn,null)))),y().createElement(pn.FormControl,{className:"sm:brui-flex-row payment-mt-30"},y().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-0 sm:payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},y().createElement(pn.Label,{id:"label-3",isError:N,required:!0,className:N?"payment-error-required":""},y().createElement("span",{id:"expiry-month-label",className:"brui-sr-only"},w),y().createElement("span",{"aria-hidden":!0},l.formatMessage({id:"CREDIT_CARD_EXPIRY_LABEL"})))),y().createElement("div",{className:"brui-flex-col"},y().createElement(pn.FormGroup,{className:"brui-flex brui-flex-wrap",hasError:N,errorMessage:l.formatMessage({id:"ERROR_CREDIT_CARD_EXPIRY_INPUT_LABEL"})},y().createElement("div",{className:"payment-w-[75px]"},y().createElement(pn.Select,{name:"month",id:"select-12",defaultValue:"",placeHolder:l.formatMessage({id:"CREDIT_CARD_MONTH_PLACEHOLDER"}),onChange:function(e){k(e.target.value)},disableDropdownIcon:!0,className:"brui-text-gray",ref:f.inputCreditCardExpiryMonth,"aria-required":!0,"aria-labelledby":"expiry-month-label"},kn().map(function(e,t){return y().createElement(pn.SelectOption,{value:e.toString(),id:"option-"+t,displayName:e.toString()})}))),y().createElement("div",{className:"payment-w-[75px] payment-ml-10"},y().createElement(pn.Select,{name:"year",id:"select-2",defaultValue:"",placeHolder:l.formatMessage({id:"CREDIT_CARD_YEAR_PLACEHOLDER"}),onChange:function(e){D(e.target.value)},disableDropdownIcon:!0,className:"brui-text-gray",ref:f.inputCreditCardExpiryYear,"aria-required":!0,"aria-labelledby":"expiry-year-label"},wn().map(function(e,t){return y().createElement(pn.SelectOption,{value:e.toString(),id:"option-year-"+t,displayName:e.toString()})})))))),y().createElement(pn.FormControl,{className:"sm:brui-flex-row payment-mt-30"},y().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-0 sm:payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},y().createElement(pn.Label,{htmlFor:"text-4",isError:A,required:!0,className:A?"payment-error-required":""},y().createElement("span",{id:"cc-security-code-label",className:"brui-sr-only"},l.formatMessage({id:"CREDIT_CARD_SECURITY_CODE_INPUT_SR_LABEL"})),y().createElement("span",{"aria-hidden":!0},l.formatMessage({id:"CREDIT_CARD_SECURITY_CODE_INPUT_LABEL"})))),y().createElement("div",{className:"brui-flex sm:brui-flex-row brui-flex-wrap"},y().createElement(pn.InputText,{onInput:function(e){return Pn(e)},className:"!payment-w-[75px] payment-mr-10",id:"text-4",required:!0,isError:A,errorMessage:l.formatMessage({id:"ERROR_CREDIT_CARD_SECURITY_CODE_INPUT_LABEL"}),errorMessageClassName:"payment-w-[75px] payment-text-nowrap",type:"password",onChange:C,ref:f.inputCreditCardSecurityCode,"aria-labelledby":"cc-security-code-label"}),y().createElement(pn.InputText,{type:"hidden",id:"text-hidden",value:h}),y().createElement("div",{className:"brui-flex brui-items-center brui-h-44"},y().createElement(hn,null)))))),y().createElement(pn.Text,{className:"brui-text-14 brui-leading-18 brui-text-gray payment-mt-45 sm:payment-mt-45 brui-inline-block"},l.formatMessage({id:"REQUIRED_LABEL"})))))}),zn=(0,mn.injectIntl)(Vn),qn=function(e){var t=e.label,n=e.value,r=e.className,a=e.needSRText,i=e.srText,o=e.role,l=e.isMultiBan;return E.createElement("div",{className:[r,"payment-mb-5 last:payment-mb-0"].join(" ").trim(),role:o},E.createElement(pn.Text,{elementType:"div",className:l?"brui-flex payment-justify-between sm:payment-justify-normal brui-text-14 brui-leading-18 brui-ml-10":"brui-flex payment-justify-between sm:payment-justify-normal brui-text-14 brui-leading-18"},E.createElement("label",{className:l?"sm:payment-w-[165px] payment-mr-30":"sm:payment-w-[175px] payment-mr-30"},l?t:E.createElement("strong",{className:"payment-leading-18"},t)),E.createElement(pn.Text,{elementType:"div",className:"brui-text-gray brui-text-right sm:brui-text-left","aria-hidden":a},n),a&&E.createElement("span",{className:"brui-sr-only"},i)))},Kn=function(e){var t=e.intl,n=e.isActive,r=(e.className,e.onEditClick),a=[{label:t.formatMessage({id:"CREDIT_CARD_TYPE_LABEL"}),value:"Visa"},{label:t.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL"}),value:"************1234"},{label:t.formatMessage({id:"CREDIT_CARD_NAME_LABEL"}),value:"Jane Doe"},{label:t.formatMessage({id:"CREDIT_CARD_EXPIRY_LABEL"}),value:"00/0000"}];return E.createElement("div",{className:n?"sm:payment-mb-60 payment-block":"sm:payment-mb-15 payment-border-b payment-border-gray-4 payment-hidden"},E.createElement("div",{className:"payment-flex payment-items-center payment-justify-between"},E.createElement(pn.HeadingStep,{disableSrOnlyText:!0,status:n?"complete":"inactive",subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",title:t.formatMessage({id:"SELECT_PAYMENT_METHOD_HEADING"}),id:"pre-auth-select-payment_method"}),E.createElement("div",{className:"payment-pt-45"},E.createElement(pn.IconLink,{icon:E.createElement(pn.Icon,{iconClass:"bi_brui",iconName:"bi_edit_pencil",className:"brui-text-16"}),text:t.formatMessage({id:"CTA_EDIT"}),variant:"textBlue",size:"regular",href:"",position:"right",className:["payment-flex payment-items-center !payment-text-14 !payment-leading-18",n?"":"payment-hidden"].join(" ").trim(),"aria-describedby":"pre-auth-select-payment_method",onClick:r,"data-test":"test"}))),E.createElement("div",{className:["brui-pb-45",n?"":"brui-hidden"].join(" ").trim()},E.createElement("div",{className:"brui-mt-15"},a.map(function(e,t){return E.createElement(qn,{className:t>0?"brui-mt-5":"",label:e.label,value:e.value})}))))},Wn=(0,mn.injectIntl)(Kn),Xn=function(e){var t=e.label,n=e.className,a=e.labelClassName,i=e.listClassName,o=e.children,l=r(e,["label","className","labelClassName","listClassName","children"]);return y().createElement("div",b({className:["payment-pb-15 last:payment-pb-0 payment-pt-15 first:payment-pt-0 payment-border-b-1 payment-border-b-gray-4 last:payment-border-none",n].join(" ").trim()},l),t&&y().createElement(pn.Text,{className:["payment-block payment-leading-18 payment-text-14 payment-mb-15 payment-text-gray sm:payment-mt-15 payment-mt-5",a].join(" ").trim()},t),y().createElement("div",{role:"list",className:["payment-text-14 payment-leading-18",i].join(" ").trim()},o))},Qn=Xn,$n={priceList:"payment-flex payment-justify-between sm:payment-justify-normal payment-mb-5 last:payment-mb-0",errorList:"payment-mb-5 last:payment-mb-0 payment-text-red",accountList:"payment-mb-5 last:payment-mb-0 payment-text-gray payment-p-15 payment-pt-10 sm:payment-pt-15 payment-bg-gray-3 payment-rounded-10"},Zn=function(e){var t=e.cardDetails,n=e.label,a=e.labelDescription,i=e.priceSettings,o=void 0===i?{showZeroDecimalPart:!0,price:0}:i,l=e.variant,u=e.className,s=(e.children,e.inputRef),c=r(e,["cardDetails","label","labelDescription","priceSettings","variant","className","children","inputRef"]),d=n+" - "+a,m=function(e){e&&e.scrollIntoView({behavior:"smooth",block:"center"})};return y().createElement("div",b({role:"listitem",className:["",$n[l],u].join(" ").trim()},c),"priceList"===l&&y().createElement(y().Fragment,null,y().createElement(pn.Text,{className:"payment-text-14 payment-leading-18 sm:payment-min-w-[153px]"},y().createElement("strong",null,n)),y().createElement(pn.Price,{language:o.language?o.language:"en",showZeroDecimalPart:o.showZeroDecimalPart,price:"number"==typeof a?a:0,variant:"defaultPrice",className:"!payment-text-14 payment-leading-18  payment-font-normal"})),"errorList"===l&&y().createElement(y().Fragment,null,y().createElement("span",{className:"payment-text-14","aria-hidden":"true"},"•"),y().createElement(pn.Link,{variant:"textRed",size:"small",href:"javascript:void(0)","aria-label":d,className:"payment-font-bold payment-ml-5",onClick:function(){var e,t,n,r;if((null==s?void 0:s.current)instanceof HTMLSelectElement)for(n=null===(e=null==s?void 0:s.current)||void 0===e?void 0:e.previousElementSibling;n;){if("BUTTON"===n.tagName)return m(r=n),void r.focus();n=n.previousElementSibling}else m(null==s?void 0:s.current),null===(t=null==s?void 0:s.current)||void 0===t||t.focus()}},n),y().createElement("span",{className:"payment-text-gray payment-text-14"}," - ",a)),"accountList"===l&&y().createElement("div",{className:"payment-flex payment-flex-wrap payment-justify-between"},y().createElement(pn.Text,{className:"payment-mr-5 payment-mt-5 sm:payment-mt-0","aria-hidden":"true"},y().createElement("strong",{className:"payment-text-black"},n)," ",a),y().createElement(pn.Text,{elementType:"span",className:"payment-mt-5 sm:payment-mt-0"},t)))},Jn=Zn,er=[{label:"Account holder name",labelDescription:"This information is required."},{label:"Transit number",labelDescription:"This information is required."},{label:"Bank name",labelDescription:"This information is required."},{label:"Account number",labelDescription:"This information is required."}],(0,mn.injectIntl)(function(e){var t=e.intl;return E.createElement(pn.Alert,{variant:"error",className:"brui-block sm:brui-flex brui-px-0 sm:brui-px-30 payment-py-30 brui-border-x-0 sm:payment-border-x-1 payment-border-y-1 brui-relative brui-rounded-none sm:payment-rounded-20",iconSize:"36",id:"alert-3"},E.createElement(pn.Text,{elementType:"div",className:"payment-pl-0 payment-pt-10 sm:payment-pl-15 sm:payment-pt-[7px]"},E.createElement(pn.Heading,{level:"h2",variant:"xs",className:" sm:brui-mt-7 brui-mb-15 brui-font-sans brui-leading-22"},E.createElement("span",{"aria-hidden":"true"},t.formatMessage({id:"ALERT_ERROR_HEADING"})),E.createElement("span",{className:"payment-sr-only"},t.formatMessage({id:"ALERT_ERROR_HEADING_SR"}))),E.createElement("div",null,E.createElement(Qn,null,er.map(function(e){return E.createElement(Jn,{label:e.label,labelDescription:e.labelDescription,variant:"errorList"})})))))}),(0,mn.injectIntl)(function(e){var t=e.children,n=e.intl;return E.createElement(pn.Alert,{variant:"error",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36",id:"alert-2"},E.createElement(pn.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0 brui-flex-1"},E.createElement(pn.Heading,{level:"h2",variant:"xs",className:"brui-mb-5 brui-font-sans brui-text-red "},n.formatMessage({id:"ALERT_ERROR_HEADING_SOME_BALANCE"})),E.createElement("div",null,t),E.createElement(pn.Button,{className:"brui-mt-30",variant:"primary",size:"regular"},n.formatMessage({id:"ALERT_CONFIRMATION_INFO_BUTTON_DESC"}))))}),tr=(0,mn.injectIntl)(function(e){var t=e.children,n=e.intl;return E.createElement(pn.Alert,{variant:"error",className:"payment-block sm:payment-flex payment-px-0 sm:payment-px-30 payment-py-30 payment-border-x-0 sm:payment-border-x-1 payment-border-y-1 payment-relative payment-rounded-none sm:payment-rounded-20",iconSize:"36",id:"alert-3","aria-labelledby":"error-alert-1 error-alert-2 error-alert-3"},E.createElement(pn.Text,{elementType:"div",className:"payment-pl-0 payment-pt-10 sm:payment-pl-15 sm:payment-pt-0"},E.createElement(pn.Heading,{id:"error-alert-1",level:"h2",variant:"xs",className:" sm:payment-mt-7 payment-mb-15 payment-font-sans payment-leading-22"},E.createElement("span",{"aria-hidden":"true"},n.formatMessage({id:"ALERT_ERROR_HEADING"})),E.createElement("span",{className:"payment-sr-only"},n.formatMessage({id:"ALERT_ERROR_HEADING_SR"}))),E.createElement("div",null,t)))}),(0,mn.injectIntl)(function(e){return e.intl,E.createElement(pn.Alert,{variant:"error",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36",id:"alert-2"},E.createElement(pn.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0"},E.createElement(pn.Heading,{level:"h2",variant:"xs",className:"brui-mb-12 brui-text-red brui-font-sans"},E.createElement("strong",null,"Your balance of $195.45 was not paid")," due to an error processing your request."),E.createElement("p",{className:"brui-text-14 brui-my-15 brui-text-gray"},"A separate one-time payment must be made to pay this balance, or risk late fees."),E.createElement(pn.Button,{variant:"primary",size:"regular"},"Make a Payment")))}),(0,mn.injectIntl)(function(e){var t=e.intl;return E.createElement(pn.Alert,{variant:"info",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36",id:"alert-3"},E.createElement(pn.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0"},E.createElement(pn.Heading,{level:"h2",variant:"xs",className:"brui-mb-12 brui-font-sans brui-font-bold brui-leading-22"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_HEADING"})),E.createElement("p",{className:"brui-text-14 brui-my-15 brui-text-gray"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_DESC"})),E.createElement(pn.Text,{elementType:"div",className:"sm:brui-flex brui-block"},E.createElement(pn.Text,{elementType:"div",className:"brui-pr-0 sm:brui-pr-10"},E.createElement(pn.Button,{variant:"primary",size:"regular"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_BUTTON_DESC"}))))))}),(0,mn.injectIntl)(function(e){var t=e.intl;return E.createElement(pn.Alert,{variant:"success",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36",id:"alert-3"},E.createElement(pn.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0"},E.createElement(pn.Heading,{level:"h2",variant:"xs",className:"brui-mb-15 brui-font-sans brui-leading-22"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_HEADING"})),E.createElement(pn.Text,{elementType:"div",role:"list",className:"brui-text-14 brui-mb-30 brui-text-black brui-ml-5"},E.createElement(pn.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start brui-mb-15"},E.createElement(pn.Icon,{className:"brui-text-20 brui-text-blue",iconClass:"bi_check_light",iconName:"icon.bi_check_small_flat_fin"}),E.createElement("span",{className:"brui-text-16 brui-leading-20 brui-ml-10"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE"}))),E.createElement(pn.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start brui-mb-15 brui-text-black"},E.createElement(pn.Icon,{className:"brui-text-20 brui-text-blue",iconClass:"bi_check_light",iconName:"icon.bi_check_small_flat_fin"}),E.createElement("span",{className:"brui-text-16 brui-leading-20 brui-ml-10"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})))),E.createElement("p",{className:"brui-text-14 brui-mb-5 brui-text-gray brui-leading-18"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})," ",E.createElement("strong",null,"000011")),E.createElement("p",{className:"brui-text-14 brui-text-gray brui-leading-18"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC"}),E.createElement(pn.Link,{variant:"textBlue",size:"small",href:"https://www.bell.ca/",className:""}," ",t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC_LINK"})))))}),(0,mn.injectIntl)(function(e){var t=e.intl;return E.createElement(pn.Alert,{variant:"success",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36",id:"alert-3"},E.createElement(pn.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0"},E.createElement(pn.Heading,{level:"h2",variant:"xs",className:"brui-mb-15 brui-font-sans brui-leading-22"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_HEADING"})),E.createElement(pn.Text,{elementType:"div",role:"list",className:"brui-text-14 brui-mb-30 brui-text-black brui-ml-5"},E.createElement(pn.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start brui-mb-15"},E.createElement(pn.Icon,{className:"brui-text-20 brui-text-blue",iconClass:"bi_check_light",iconName:"icon.bi_check_small_flat_fin"}),E.createElement("span",{className:"brui-text-16 brui-leading-20 brui-ml-10"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE"})))),E.createElement("p",{className:"brui-text-14 brui-mb-5 brui-text-gray brui-leading-18"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})," ",E.createElement("strong",null,"000011")),E.createElement("p",{className:"brui-text-14 brui-text-gray brui-leading-18"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC"}),E.createElement(pn.Link,{variant:"textBlue",size:"small",href:"https://www.bell.ca/",className:""}," ",t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC_LINK"})))))}),(0,mn.injectIntl)(function(e){var t=e.intl;return E.createElement(pn.Alert,{variant:"success",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36",id:"alert-3"},E.createElement(pn.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0"},E.createElement(pn.Heading,{level:"h2",variant:"xs",className:"brui-mb-15 brui-font-sans brui-leading-22"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_HEADING"})),E.createElement(pn.Text,{elementType:"div",role:"list",className:"brui-text-14 brui-mb-30 brui-text-black brui-ml-5"},E.createElement(pn.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start brui-mb-15"},E.createElement(pn.Icon,{className:"brui-text-12 brui-text-blue brui-mt-3",iconClass:"bi_check_light",iconName:"bi_check_light"}),E.createElement("span",{className:"brui-text-16 brui-leading-20 brui-ml-10"},t.formatMessage({id:"ALERT_CONFIRMATION_CURRENT_BALANCE"}))),E.createElement(pn.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start brui-mb-15"},E.createElement(pn.Icon,{className:"brui-text-20 brui-text-blue",iconClass:"bi_check_light",iconName:"icon.bi_check_small_flat_fin"}),E.createElement("span",{className:"brui-text-16 brui-leading-20 brui-ml-10"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE"})))),E.createElement("p",{className:"brui-text-14 brui-mb-5 brui-text-gray brui-leading-18"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})," ",E.createElement("strong",null,"000011")),E.createElement("p",{className:"brui-text-14 brui-text-gray brui-leading-18"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC"}),E.createElement(pn.Link,{variant:"textBlue",size:"small",href:"https://www.bell.ca/",className:""}," ",t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC_LINK"})))))}),(0,mn.injectIntl)(function(e){var t=e.intl;return E.createElement(pn.Alert,{variant:"success",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36",id:"alert-3"},E.createElement(pn.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0"},E.createElement(pn.Heading,{level:"h2",variant:"xs",className:"brui-mb-15 brui-font-sans brui-leading-22"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_HEADING"})),E.createElement(pn.Text,{elementType:"div",role:"list",className:"brui-text-14 brui-mb-30 brui-text-black brui-ml-5"},E.createElement(pn.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start brui-mb-15"},E.createElement(pn.Icon,{className:"brui-text-20 brui-text-blue",iconClass:"bi_check_light",iconName:"icon.bi_check_small_flat_fin"}),E.createElement("span",{className:"brui-text-16 brui-leading-20 brui-ml-10"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE"}))),E.createElement(pn.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start brui-mb-15 brui-text-black"},E.createElement(pn.Icon,{className:"brui-text-20 brui-text-blue",iconClass:"bi_check_light",iconName:"icon.bi_check_small_flat_fin"}),E.createElement("span",{className:"brui-text-16 brui-leading-20 brui-ml-10"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})))),E.createElement("p",{className:"brui-text-14 brui-mb-5 brui-text-gray brui-leading-18"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})," ",E.createElement("strong",null,"000011")),E.createElement("p",{className:"brui-text-14 brui-text-gray brui-leading-18"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC"}),E.createElement(pn.Link,{variant:"textBlue",size:"small",href:"https://www.bell.ca/",className:""}," ",t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC_LINK"})))))}),(0,mn.injectIntl)(function(e){var t=e.intl,n=e.children;return E.createElement(pn.Alert,{variant:"warning",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36"},E.createElement(pn.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0"},E.createElement(pn.Heading,{level:"h2",variant:"xs",className:"brui-mb-5 brui-font-sans brui-font-bold"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_HEADING"})),E.createElement("div",null,n),E.createElement(pn.Button,{className:"brui-mt-30",variant:"primary",size:"regular"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_BUTTON_DESC"}))))}),(0,mn.injectIntl)(function(e){var t=e.intl;return E.createElement(pn.Alert,{variant:"warning",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36"},E.createElement(pn.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0"},E.createElement(pn.Heading,{level:"h2",variant:"xs",className:"brui-mb-0 sm:brui-mb-12 brui-font-sans brui-font-bold brui-leading-22"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_HEADING"})),E.createElement("p",{className:"brui-leading-18 brui-text-14 brui-mt-5 brui-mb-15 sm:brui-mt-0 sm:brui-mb-0 sm:brui-my-15 brui-text-gray"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_DESC_1"}),E.createElement(pn.Price,{language:"en",showZeroDecimalPart:!0,price:195.45,variant:"defaultPrice",className:"!brui-text-14 brui-leading-14 brui-m-5 brui-font-normal brui-inline-block"}),E.createElement("span",{className:"brui-sr-only"},"195 point 45 dollars")," ",t.formatMessage({id:"ALERT_CONFIRMATION_INFO_DESC_2"})),E.createElement(pn.Text,{elementType:"div",className:"sm:brui-flex brui-block"},E.createElement(pn.Text,{elementType:"div",className:"brui-pr-0 sm:brui-pr-10"},E.createElement(pn.Button,{variant:"primary",size:"regular"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_BUTTON_DESC"}))))))}),(0,mn.injectIntl)(function(e){var t=e.intl;return E.createElement(pn.Alert,{variant:"warning",className:"payment-border payment-relative sm:payment-py-30 sm:payment-px-30 payment-rounded-20 payment-p-16 payment-px-15 payment-py-30 sm:payment-flex payment-block",iconSize:"36"},E.createElement(pn.Text,{elementType:"div",className:"payment-pl-0 payment-pt-15 sm:payment-pl-16 sm:payment-pt-0"},E.createElement(pn.Heading,{level:"h2",variant:"xs",className:"payment-mb-15 payment-font-sans payment-font-bold"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_HEADING"})),E.createElement("p",{className:"payment-text-14 payment-mb-10 payment-text-gray"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_DESC"})),E.createElement(pn.Text,{elementType:"div",className:"payment-mb-30 payment-mt-15"},E.createElement(pn.Text,{elementType:"div",className:"payment-flex payment-justify-between sm:payment-justify-normal"},E.createElement("label",{className:"payment-text-14 sm:payment-basis-1/4"},E.createElement("strong",null,"**********")),E.createElement(pn.Price,{language:"en",showZeroDecimalPart:!0,price:206.98,variant:"defaultPrice",className:"!payment-text-14 payment-leading-14 payment-m-5 payment-font-normal"}))),E.createElement(pn.Button,{variant:"primary",size:"regular"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_BUTTON_DESC"}))))}),nr=function(e){var t,n=e.intl,r=e.submitMultiOrderPayment,a=e.accountInputValue,i=e.isBankPayment,o=e.checkedBillItems,l=e.language,u=e.paymentItem,s=e.creditCardAutopayOffers,c=e.debitCardAutopayOffers,d=n.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE"}),m=i?"fr"===l?"ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD_FR":"ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD":"ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PACC",p={label:n.formatMessage({id:"PAYMENT_METHOD"}),debits:i?u&&u.length>1?(t=[],c&&(null==c||c.map(function(e){o&&o.map(function(n){e.Ban===n.Ban&&t.push(e)})})),t):c:null,credits:i?null:u&&u.length>1?function(){var e=[];return s&&(null==s||s.map(function(t){o&&o.map(function(n){t.Ban===n.Ban&&e.push(t)})})),e}():s},b=p&&p.credits&&p.credits.length>0&&p.credits[0].AutopayEligibleSubscribers&&p.credits[0].AutopayEligibleSubscribers.length>0&&p.credits[0].AutopayEligibleSubscribers[0].autopayOffers&&p.credits[0].AutopayEligibleSubscribers[0].autopayOffers.length>0||p&&p.debits&&p.debits.length>0&&p.debits[0].AutopayEligibleSubscribers&&p.debits[0].AutopayEligibleSubscribers.length>0&&p.debits[0].AutopayEligibleSubscribers[0].autopayOffers&&p.debits[0].AutopayEligibleSubscribers[0].autopayOffers.length>0;return E.createElement(pn.Alert,{variant:"success",className:"brui-border brui-relative sm:payment-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 payment-py-30 sm:payment-flex brui-block",iconSize:"36",id:"alert-3",role:""},E.createElement(pn.Text,{elementType:"div",className:"payment-pl-0 sm:payment-pl-16 payment-mt-15 sm:payment-mt-0 sm:brui-pt-0"},E.createElement(pn.Heading,{level:"h3",variant:"xs",className:"brui-mb-15 brui-font-sans brui-leading-22"},n.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_HEADING"})),E.createElement(pn.Text,{elementType:"div",role:"list",className:"brui-text-14 brui-mb-30 brui-text-black brui-ml-5"},r.map(function(e,t){var r,i,l=null===(r=a.find(function(t){return t.transactionID===e.OrderFormId}))||void 0===r?void 0:r.accountNumber,u=(null===(i=o.find(function(e){return e.Ban===l}))||void 0===i?void 0:i.NickName)||l,s=n.formatMessage({id:m},{account:u});return E.createElement(E.Fragment,null,null!=(null==e?void 0:e.otp)&&(null==e?void 0:e.otp.isSuccess)&&E.createElement(pn.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start payment-mb-15"},E.createElement(pn.Icon,{className:"brui-text-12 brui-text-blue payment-mt-3",iconClass:"bi_brui",iconName:"bi_check_light"}),E.createElement("span",{className:"brui-text-16 brui-leading-20 payment-ml-10",dangerouslySetInnerHTML:{__html:s}})))}),E.createElement(pn.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start payment-mb-15"},E.createElement(pn.Icon,{className:"brui-text-12 brui-text-blue payment-mt-3 ",iconClass:"bi_brui",iconName:"bi_check_light"}),E.createElement("span",{className:"brui-text-16 brui-leading-20 payment-ml-10"},E.createElement("div",{dangerouslySetInnerHTML:{__html:d}}))),b?E.createElement(pn.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start payment-mb-15"},E.createElement(pn.Icon,{className:"brui-text-12 brui-text-blue payment-mt-3",iconClass:"bi_brui",iconName:"bi_check_light"}),E.createElement("span",{className:"brui-text-16 brui-leading-20 payment-ml-10"},n.formatMessage({id:"AUTOPAY_ALERT"}))):""),E.createElement("p",{className:"brui-text-14 brui-mb-5 brui-text-gray brui-leading-18"},n.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_NUMBER"})," ",E.createElement("strong",null,r[0].PaymentConfirmationNumber)),E.createElement("p",{className:"brui-text-14 brui-text-gray brui-leading-18"},E.createElement(mn.FormattedMessage,{id:"ALERT_CONFIRMATION_SUCCESS_DESC",values:{email:E.createElement("strong",null,r[0].ConfirmationEmailAddress)}})," ",E.createElement(pn.Link,{variant:"textBlue",size:"small",href:"/MyProfile/EditProfile?editField=EMAIL_ADDRESS",className:""},n.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC_LINK"})),".")))},rr=(0,mn.injectIntl)(nr),ar={greatNews:{className:"brui-text-15 brui-text-blue",iconClass:"bi_brui",iconName:"bi_tag_note-big"},notifCardWarning:{className:"brui-text-20 brui-text-yellow",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"}},ir=function(e){var t=e.intl,n=e.hasNotifCard,r=void 0!==n&&n,a=e.children,i=e.label,o=e.variant,l=t.formatMessage({id:"ALERT_GREAT_NEWS_NOTE"}),u=t.formatMessage({id:"ALERT_GREAT_NEWS_NOTE_DESC"});return E.createElement(pn.Card,{variant:"gray",radius:!0,className:["brui-flex brui-flex-col sm:brui-flex-row brui-p-15 brui-gap-15 brui-rounded-[16px]",r?"":"brui-hidden"].join(" ").trim()},E.createElement("div",{className:"brui-flex brui-size-20 brui-items-start payment-pb-15 payment-pr-15"},E.createElement(pn.Icon,{className:["",ar[o].className].join(" ").trim(),iconClass:["",ar[o].iconClass].join(" ").trim(),iconName:["",ar[o].iconName].join(" ").trim()})),E.createElement("div",{className:"brui-flex-grow"},E.createElement("p",{className:"brui-text-14 brui-leading-18 brui-text-gray brui-mb-10"},i),a,E.createElement("div",{className:"brui-text-12 brui-text-gray brui-leading-14"},E.createElement("strong",null,l),u)))},(0,mn.injectIntl)(ir),or=function(e){var t=e.intl,n=e.isErrorCardNumber,r=e.isErrorCardName,a=e.isErrorExpiryDate,i=e.isErrorSecurityCode,o=e.isErrorBankAccountHolderName,l=e.isErrorBankAccountNumber,u=e.isErrorBankName,s=e.iserrorBankTransit,c=e.inputRefs;return E.createElement(pn.Alert,{variant:"error",className:"payment-block sm:payment-flex brui-px-0 sm:payment-px-30 payment-py-30 brui-border-x-0 sm:payment-border-x-1 payment-border-y-1 payment-relative brui-rounded-none sm:payment-rounded-20",iconSize:"36",id:"alert-3"},E.createElement(pn.Text,{elementType:"div",className:"payment-pl-0 payment-pt-10 sm:payment-pl-15 sm:payment-pt-[7px]"},E.createElement(pn.Heading,{level:"h2",variant:"xs",className:" sm:brui-mt-7 brui-mb-15 brui-font-sans brui-leading-22"},E.createElement("span",{id:"error-1","aria-hidden":"true"},t.formatMessage({id:"ALERT_ERROR_HEADING"})),E.createElement("span",{className:"payment-sr-only"},t.formatMessage({id:"ALERT_ERROR_HEADING_SR"}))),E.createElement("div",null,E.createElement(Qn,null,n&&E.createElement(Jn,{id:"error-2",label:t.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL"}),labelDescription:t.formatMessage({id:"ALERT_ERROR_GENERAL_DESC"}),variant:"errorList",inputRef:null==c?void 0:c.inputCreditCardNumber}),r&&E.createElement(Jn,{id:"error-3",label:t.formatMessage({id:"CREDIT_CARD_NAME_LABEL"}),labelDescription:t.formatMessage({id:"ALERT_ERROR_GENERAL_DESC"}),variant:"errorList",inputRef:null==c?void 0:c.inputCreditCardHolderName}),a&&E.createElement(Jn,{id:"error-4",label:t.formatMessage({id:"CREDIT_CARD_EXPIRY_LABEL"}),labelDescription:t.formatMessage({id:"ALERT_ERROR_GENERAL_DESC"}),variant:"errorList",inputRef:null==c?void 0:c.inputCreditCardExpiryMonth}),i&&E.createElement(Jn,{id:"error-5",label:t.formatMessage({id:"CREDIT_CARD_SECURITY_CODE_INPUT_LABEL"}),labelDescription:t.formatMessage({id:"ALERT_ERROR_GENERAL_DESC"}),variant:"errorList",inputRef:null==c?void 0:c.inputCreditCardSecurityCode}),u&&E.createElement(Jn,{id:"error-2",label:t.formatMessage({id:"BANK_NAME_LABEL"}),labelDescription:t.formatMessage({id:"ALERT_ERROR_GENERAL_DESC"}),variant:"errorList",inputRef:null==c?void 0:c.inputBankName}),o&&E.createElement(Jn,{id:"error-3",label:t.formatMessage({id:"BANK_HOLDER_NAME_LABEL"}),labelDescription:t.formatMessage({id:"ALERT_ERROR_GENERAL_DESC"}),variant:"errorList",inputRef:null==c?void 0:c.inputBankAccountHolder}),s&&E.createElement(Jn,{id:"error-4",label:t.formatMessage({id:"BANK_TRANSIT_NUMBER_LABEL"}),labelDescription:t.formatMessage({id:"ALERT_ERROR_GENERAL_DESC"}),variant:"errorList",inputRef:null==c?void 0:c.inputTransitNumber}),l&&E.createElement(Jn,{id:"error-5",label:t.formatMessage({id:"BANK_ACCOUNT_NUMBER_LABEL"}),labelDescription:t.formatMessage({id:"ALERT_ERROR_GENERAL_DESC"}),variant:"errorList",inputRef:null==c?void 0:c.inputBankAccountNumber})))))},lr=(0,mn.injectIntl)(or),ur=function(e){var t=e.intl,n=e.interact;return E.createElement(pn.Alert,{variant:"error",className:"payment-block sm:payment-flex brui-px-0 sm:payment-px-30 payment-py-30 brui-border-x-0 sm:payment-border-x-1 payment-border-y-1 payment-relative brui-rounded-none sm:payment-rounded-20",iconSize:"36",id:"alert-3"},E.createElement(pn.Text,{elementType:"div",className:"payment-pl-0 payment-pt-10 sm:payment-pl-15 sm:payment-pt-0 md:payment-pt-7"},E.createElement(pn.Heading,{level:"h2",variant:"xs",className:" sm:brui-mt-7 brui-mb-15 md:payment-mb-7 brui-font-sans brui-leading-22"},E.createElement("span",{"aria-hidden":"true"},(n.includes(Ln),t.formatMessage({id:"ALERT_ERROR_HEADING_INTERAC"}))),E.createElement("span",{className:"payment-sr-only"},(n.includes(Ln),t.formatMessage({id:"ALERT_ERROR_HEADING_INTERAC_SR"})))),E.createElement("div",null,E.createElement("p",{className:"brui-text-14 brui-my-15 brui-text-gray"},(n.includes(Ln),t.formatMessage({id:"ALERT_ERROR_HEADING_INTERAC_DESC"}))))))},sr=(0,mn.injectIntl)(ur),cr=function(e){var t=e.intl,n=e.checkedCurrentBalanceItems,r=e.submitMultiOrderPayment,a=e.accountInputValue,i=e.language,o=e.notOptedBalanceItems,l=e.setOmnitureOnOneTimePaymentFailure,u=t.formatMessage({id:"ALERT_ERROR_OTP_ALL_BALANCE"}),s=t.formatMessage({id:"ALERT_ERROR_HEADING_SOME_BALANCE"}),c=t.formatMessage({id:"ALERT_ERROR_OTP_BALANCE_DESC"}),d=t.formatMessage({id:"ALERT_ERROR_OPTED_NOT_TO_PAY_SINGULAR"}),m=t.formatMessage({id:"ALERT_ERROR_OPTED_NOT_TO_PAY_PLURAL"}),p=r.filter(function(e){return(null==e?void 0:e.otp)&&!(null==e?void 0:e.otp.isSuccess)}),b=p.map(function(e){return null==e?void 0:e.OrderFormId}),f=a.filter(function(e){return b.includes(e.transactionID)}).map(function(e){return e.accountNumber}),y=null==n?void 0:n.filter(function(e){return f.includes(e.Ban)}),_=n,g=_.length>y.length,v=_.length===y.length&&p.length>1,N=1===_.length?t.formatMessage({id:"ALERT_ERROR_OTP_BALANCE"},{balance:_[0].DueStr}):"",A=1===_.length?t.formatMessage({id:"ALERT_ERROR_OTP_BALANCE_SR"},{balance:_[0].DueStr}):"";return E.useEffect(function(){setTimeout(function(){(v||g)&&l({s_oPYM:"",s_oCCDT:""})},1e3)},[g,v]),E.createElement(pn.Alert,{variant:"error",className:"payment-block payment-border payment-rounded-20 sm:payment-flex payment-px-15 sm:payment-px-30 payment-py-30 sm:payment-pb-45 payment-border-y-1 brui-relative brui-rounded-none sm:payment-rounded-20",iconSize:"36",id:"alert-otp-fail"},E.createElement(pn.Text,{elementType:"div",className:"payment-pl-0 payment-pt-15 sm:payment-pl-15 sm:payment-pt-0"},E.createElement(pn.Heading,{level:"h3",variant:"xs",className:"payment-mb-5 payment-font-sans brui-text-red"},1===_.length&&E.createElement(E.Fragment,null,E.createElement("span",{"aria-hidden":"true",dangerouslySetInnerHTML:{__html:N}}),E.createElement("span",{className:"payment-sr-only"},A)),g&&E.createElement("span",{dangerouslySetInnerHTML:{__html:s}}),v&&E.createElement("span",{dangerouslySetInnerHTML:{__html:u}})),E.createElement(pn.Text,{elementType:"div"},E.createElement("span",{className:"payment-text-14 payment-leading-18 payment-text-gray",dangerouslySetInnerHTML:{__html:c}})),(g||v)&&E.createElement("div",{className:"!payment-border-none payment-mt-15"},E.createElement(Qn,{label:""},y.map(function(e){return E.createElement(Jn,{label:e.NickName,labelDescription:e.Due,variant:"priceList",priceSettings:{language:i,showZeroDecimalPart:!0}})})),g&&o.length>0&&E.createElement("div",{className:"payment-border-t-gray-4 payment-mt-15"},E.createElement(Qn,{label:1===o.length?d:m},o.map(function(e){return E.createElement(Jn,{label:e.NickName,labelDescription:e.Due,variant:"priceList",priceSettings:{language:i,showZeroDecimalPart:!0}})})))),E.createElement(pn.Button,{className:"payment-mt-30",variant:"primary",size:"regular",onClick:function(){location.href="".concat(t.formatMessage({id:"CTA_MAKE_PAYMENT_LINK"}))}},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_BUTTON_DESC"}))))},dr=(0,mn.injectIntl)(cr),mr=function(e){var t,n=e.intl,r=e.className,a=e.paymentItem,i=e.isPreauth,o=e.inputValue,l=e.inputBankValue,u=e.isBankPaymentSelected,s=e.isNewbank,d=e.onEditClick,m=e.showHeading,p=(e.isSingleClickEnable,e.bankList),b=e.debitCardAutopayOffers,f=e.creditCardAutopayOffers,y=e.checkedBillItems,_=e.bankitems,g=e.isConfirmation,v=e.IsAutopayCreditEnabled,N=e.isShow,A=a.filter(function(e){return!0===e.IsOnPreauthorizedPayments&&e.CreditCardDetails}),C=A.length>0?A[0].CreditCardDetails:null,h=function(){var e;if(s||!1===i)return!0===u?[{label:n.formatMessage({id:"PAYMENT_METHOD"}),value:null==l?void 0:l.PaymentMethod},{label:n.formatMessage({id:"ACCOUNT_HOLDER"}),value:null==l?void 0:l.AccountHolder},{label:n.formatMessage({id:"BANK_NAME"}),value:null===(e=p.filter(function(e){return e.Value===(null==l?void 0:l.BankName)})[0])||void 0===e?void 0:e.Text},{label:n.formatMessage({id:"TRANSIT_NUMER"}),value:null==l?void 0:l.TransitNumber},{label:n.formatMessage({id:"ACCOUNT_NUMBER"}),value:(null==l?void 0:l.AccountNumber)?"*******".concat(String(l.AccountNumber).slice(-3)):null==l?void 0:l.AccountNumber}]:[{label:n.formatMessage({id:"CREDIT_CARD_TYPE_LABEL"}),value:o.cardType},{label:n.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL_V2"}),value:s&&o.cardNumber?"*******".concat(String(o.cardNumber).slice(-4)):o.cardNumber},{label:n.formatMessage({id:"CREDIT_CARD_NAME_LABEL_V2"}),value:o.cardName},{label:n.formatMessage({id:"CREDIT_CARD_EXPIRY_LABEL"}),value:o.expiryDate}];if(!1===s||!0===i){if(!0===u)return[{label:n.formatMessage({id:"PAYMENT_METHOD"}),value:null==l?void 0:l.PaymentMethod},{label:n.formatMessage({id:"ACCOUNT_HOLDER"}),value:null==l?void 0:l.AccountHolder},{label:n.formatMessage({id:"BANK_NAME"}),value:null==l?void 0:l.BankName},{label:n.formatMessage({id:"TRANSIT_NUMER"}),value:null==l?void 0:l.TransitNumber},{label:n.formatMessage({id:"ACCOUNT_NUMBER"}),value:(null==l?void 0:l.AccountNumber)?"*******".concat(String(l.AccountNumber).slice(-3)):null==l?void 0:l.AccountNumber}];if(o)return[{label:n.formatMessage({id:"CREDIT_CARD_TYPE_LABEL"}),value:o.cardType},{label:n.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL_V2"}),value:s&&o.cardNumber?"*******".concat(String(o.cardNumber).slice(-4)):o.cardNumber},{label:n.formatMessage({id:"CREDIT_CARD_NAME_LABEL_V2"}),value:o.cardName},{label:n.formatMessage({id:"CREDIT_CARD_EXPIRY_LABEL"}),value:o.expiryDate}];if(C)return[{label:n.formatMessage({id:"CREDIT_CARD_TYPE_LABEL"}),value:c(C.CreditCardType)},{label:n.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL_V2"}),value:null==C?void 0:C.CreditCardNumberMasked},{label:n.formatMessage({id:"CREDIT_CARD_NAME_LABEL_V2"}),value:null==C?void 0:C.CardholderName},{label:n.formatMessage({id:"CREDIT_CARD_EXPIRY_LABEL"}),value:null==C?void 0:C.ExpirationDateDisplayViewA}]}return[]};return E.createElement("div",{className:r},E.createElement("div",{className:g?"payment-border-gray-4":"payment-border-b payment-border-gray-4"},E.createElement("div",{className:m?"payment-flex payment-items-center payment-justify-between payment-mt-0":"payment-hidden"},E.createElement(pn.HeadingStep,{disableSrOnlyText:!0,status:"complete",subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",title:n.formatMessage({id:"PAYMENT_SUMMARY_TITLE"}),id:N?"payment-setup-heading":void 0,"aria-hidden":N?"true":void 0}),E.createElement("div",{className:"payment-pt-45"},E.createElement(pn.IconLink,{icon:E.createElement(pn.Icon,{iconClass:"bi_brui",iconName:"bi_edit_pencil",className:"brui-text-16"}),text:n.formatMessage({id:"CTA_EDIT"}),variant:"textBlue",size:"regular",href:"javascript:void(0);",position:"right",className:["payment-flex payment-items-center !payment-text-14 !payment-leading-18"].join(" ").trim(),"aria-describedby":"pre-auth-payment-summary",onClick:d}))),!g&&v&&(_&&_.length>1?(t=[],u?b&&(null==b||b.map(function(e){y&&y.map(function(n){e.Ban===n.Ban&&t.push(e)})})):f&&(null==f||f.map(function(e){y&&(null==y||y.map(function(n){e.Ban===n.Ban&&t.push(e)}))})),t).reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0):u?b&&b.reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0):f&&f.reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0))>0?E.createElement(pn.Text,{elementType:"div",className:"payment-flex payment-items-center payment-mt-5"},E.createElement(pn.Icon,{className:"paymnet-text-15 payment-text-blue payment-mr-5",iconClass:"bi_brui",iconName:"bi_tag_note-big"}),E.createElement("p",{className:"payment-text-gray payment-text-14"},n.formatMessage({id:"REVIEW_PAGE_AUTOPAY_CREDIT"})," ")):null,u?E.createElement("div",{className:g?"payment-block payment-relative":"payment-block payment-relative payment-pb-45"},E.createElement("div",{className:"brui-mt-15"},h().map(function(e,t){return E.createElement(qn,{className:"",label:e.label,value:e.value,"data-index":t,srText:e.label===n.formatMessage({id:"ACCOUNT_NUMBER"})?n.formatMessage({id:"BANK_ACCOUNT_SR_TEXT"},{Account:String(e.value).slice(-3)}):"",needSRText:e.label===n.formatMessage({id:"ACCOUNT_NUMBER"})})}))):E.createElement("div",{className:g?"payment-block payment-relative":"payment-block payment-relative payment-pb-45"},E.createElement("div",{className:"brui-mt-15"},h().map(function(e,t){return E.createElement(qn,{className:"",label:e.label,value:e.label===n.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL_V2"})?"*******".concat(String(e.value).slice(-4)):e.value,"data-index":t,srText:e.label===n.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL_V2"})?n.formatMessage({id:"CREDIT_CARD_SR_TEXT"},{Account:String(e.value).slice(-4)}):"",needSRText:e.label===n.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL_V2"})})})))))},pr=(0,mn.injectIntl)(mr),n(function(e,t){void 0===e&&(e=""),void 0===t&&(t=br.None);var n=da.call(this,e)||this;return n.errorType=t,n},da=g.ApplicationError),function(e){n(function(t,n,r,a,i){void 0===i&&(i=br.WebAPIError);var o=e.call(this,n)||this;return o.statusCode=t,o.message=n,o.serverMessage=r,o.apiName=a,o.errorType=i,o},e)}(g.ApplicationError),function(e){e[e.None=0]="None",e[e.NoDataError=1]="NoDataError",e[e.WebAPIError=2]="WebAPIError",e[e.GetPreviousBillAPIError=3]="GetPreviousBillAPIError",e[e.Exception=4]="Exception",e[e.ApplicationError=5]="ApplicationError",e[e.NoTooltipDataError=6]="NoTooltipDataError",e[e.GetOverageAPIError=7]="GetOverageAPIError"}(br||(br={})),function(e){e.Empty="EMPTY",e.CreditCardExpireDate="CREDIT_CARD_EXPIRY",e.Invalid="INVALID"}(fr||(fr={})),Er=function(){},yr=function(e){var t,n,r,a,i,l,u,d,m,p,b,f,y,_,g,v,N,A,C,h,T,I=e.intl,R=e.isHeadingStepActive,x=e.paymentItem,S=e.creditcardDetails,O=e.onCreditCardNumberChange,M=e.onCardHolderNameChange,L=e.onCreditCardExpiryDateChange,D=e.onSecurityCodeChange,B=e.resetValidationErrors,P=e.validationErrors,k=e.isSingleClickEnableForPAD,U=e.isSingleClickEnableForPACC,F=e.setHeadingSteps,H=e.setCurrentSection,G=e.currentSection,V=e.setInputValue,z=e.inputValue,q=e.setInputBankValue,K=e.inputBankValue,W=e.setIsBankSelected,X=e.validateFormOrder,Q=e.checkedBillItems,$=e.tokenizeAndPropagateFormValues,Z=e.bankList,J=e.validatBankDetails,ee=e.cardTokenizationSuccess,te=e.redirectUrl,ne=e.interacBankInfo,re=e.accountInputValues,ae=e.interactBankFailureInfo,ie=e.creditCardAutopayOffers,oe=e.debitCardAutopayOffers,le=e.language,ue=e.setOmnitureOnPaymentSelect,se=e.isInteractEnabled,ce=e.IsAutopayCreditEnabled,de=e.InteracCode,me=e.setOmnitureOnInteracFailure,pe=o(E.useState(null==S?void 0:S.CreditCardNumber),2),be=pe[0],fe=pe[1],Ee=o(E.useState("default"),2),ye=Ee[0],_e=Ee[1],ge=o(E.useState(""),2),ve=ge[0],Ne=ge[1],Ae=o(E.useState(!1),2),Ce=Ae[0],he=Ae[1],Te=o(E.useState(!1),2),Ie=Te[0],Re=Te[1],xe=o(E.useState(!1),2),Se=xe[0],Oe=xe[1],Me=o(E.useState(!1),2),Le=Me[0],De=Me[1],Be=o(E.useState(!1),2),Pe=Be[0],ke=Be[1],we=o(E.useState(!1),2),Ue=we[0],Fe=we[1],He=o(E.useState(!1),2),Ye=He[0],je=He[1],Ge=o(E.useState(!1),2),Ve=Ge[0],ze=Ge[1],qe=o(E.useState(!1),2),Ke=qe[0],We=qe[1],Xe=o(E.useState(""),2),Qe=Xe[0],$e=Xe[1],Ze=o(E.useState(!1),2),Je=Ze[0],et=Ze[1],tt=o(E.useState(!1),2),nt=tt[0],rt=tt[1],at=o(E.useState(!0),2),it=at[0],ot=at[1],lt=o(E.useState(!1),2),ut=lt[0],st=lt[1],ct=o(E.useState(!1),2),dt=ct[0],mt=ct[1],pt=o(E.useState({SelectedPaymentMethod:"",CardholderName:"",CreditCardToken:"",CreditCardType:"",ExpiryYear:"",ExpiryMonth:"",SecurityCode:""}),2),bt=pt[0],ft=pt[1],Et=o(E.useState({SelectedPaymentMethod:"",BankName:"",HolderName:"",TransitCode:"",AccountNumber:"",BankCode:""}),2),yt=Et[0],_t=Et[1],gt=o(E.useState(I.formatMessage({id:"BANK_ACCOUNT_LABEL"})),2),vt=gt[0],Nt=gt[1],At=o(E.useState(!0),2),Ct=At[0],ht=At[1],Tt=E.useRef(null),It={interac:E.useRef(null),manualDetails:E.useRef(null)},Rt={inputCreditCardNumber:E.useRef(null),inputCreditCardHolderName:E.useRef(null),inputCreditCardSecurityCode:E.useRef(null),inputCreditCardExpiryMonth:E.useRef(null),inputCreditCardExpiryYear:E.useRef(null),inputBankName:E.useRef(null),inputBankAccountHolder:E.useRef(null),inputTransitNumber:E.useRef(null),inputBankAccountNumber:E.useRef(null)},xt=I.formatMessage({id:"InteracSupportedFinancialInstitutions"}).split(","),St=function(e){$e(e.target.value)};return E.useEffect(function(){Qe===I.formatMessage({id:"NEW_CREDIT_ACCOUNT_LABEL"})||Qe===I.formatMessage({id:"BANK_NEW_BANK_ACCOUNT_LABEL"})?We(!0):We(!1)},[Qe]),t=function(e){Ne(e.target.value)},n={VISA:I.formatMessage({id:"VISA_CC_PNG"}),MASTERCARD:I.formatMessage({id:"MASTER_CC_PNG"}),AMEX:I.formatMessage({id:"AMEX_CC_PNG"})},r=function(){he(!1),Re(!1),De(!1),Oe(!1),ze(!1),je(!1),ke(!1),Fe(!1)},a=I.formatMessage({id:"PAYMENT_METHOD_DEBIT"}),i=function(e){var t,n,i,o,u,c,d,m,p,b,f,E,y,_,v,A,C,h,T,I,R,x,S,B,P,k,w,U,F,H,Y,j,G,V;e.preventDefault(),r(),Ct?(_t({SelectedPaymentMethod:a,BankName:null===(h=Z.filter(function(e){var t;return e.Value===(null===(t=Rt.inputBankName.current)||void 0===t?void 0:t.value)})[0])||void 0===h?void 0:h.Text,HolderName:(null===(T=Rt.inputBankAccountHolder.current)||void 0===T?void 0:T.value)?null===(I=Rt.inputBankAccountHolder.current)||void 0===I?void 0:I.value:(null==N?void 0:N.CardHolder)||"",TransitCode:(null===(R=Rt.inputTransitNumber.current)||void 0===R?void 0:R.value)?null===(x=Rt.inputTransitNumber.current)||void 0===x?void 0:x.value:(null==N?void 0:N.TransitCode)||"",AccountNumber:(null===(S=Rt.inputBankAccountNumber.current)||void 0===S?void 0:S.value)?null===(B=Rt.inputBankAccountNumber.current)||void 0===B?void 0:B.value:(null==N?void 0:N.AccountNumber)||"",BankCode:(null===(P=Rt.inputBankName.current)||void 0===P?void 0:P.value)?null===(k=Rt.inputBankName.current)||void 0===k?void 0:k.value.slice(-3):""}),q({PaymentMethod:a,AccountHolder:(null===(w=Rt.inputBankAccountHolder.current)||void 0===w?void 0:w.value)?null===(U=Rt.inputBankAccountHolder.current)||void 0===U?void 0:U.value:(null==N?void 0:N.CardHolder)||"",BankName:(null===(F=Rt.inputBankName.current)||void 0===F?void 0:F.value)?null===(H=Rt.inputBankName.current)||void 0===H?void 0:H.value:(null==N?void 0:N.BankName)||"",TransitNumber:(null===(Y=Rt.inputTransitNumber.current)||void 0===Y?void 0:Y.value)?null===(j=Rt.inputTransitNumber.current)||void 0===j?void 0:j.value:(null==N?void 0:N.TransitCode)||"",AccountNumber:(null===(G=Rt.inputBankAccountNumber.current)||void 0===G?void 0:G.value)?null===(V=Rt.inputBankAccountNumber.current)||void 0===V?void 0:V.value:(null==N?void 0:N.AccountNumber)||""}),J(l())):(ft({SelectedPaymentMethod:"CreditCard",CardholderName:(null===(t=Rt.inputCreditCardHolderName.current)||void 0===t?void 0:t.value)?null===(n=Rt.inputCreditCardHolderName.current)||void 0===n?void 0:n.value:(null==g?void 0:g.CardholderName)||"",CreditCardToken:(null===(i=Rt.inputCreditCardNumber.current)||void 0===i?void 0:i.value)?null===(o=Rt.inputCreditCardNumber.current)||void 0===o?void 0:o.value:(null==g?void 0:g.CreditCardNumber)||"",CreditCardType:(null===(u=Rt.inputCreditCardNumber.current)||void 0===u?void 0:u.value)?Rn(null===(c=Rt.inputCreditCardNumber.current)||void 0===c?void 0:c.value):((null==g?void 0:g.CreditCardType)?s(g.CreditCardType):"")||"",ExpiryYear:(null===(d=Rt.inputCreditCardExpiryYear.current)||void 0===d?void 0:d.value)?null===(m=Rt.inputCreditCardExpiryYear.current)||void 0===m?void 0:m.value:(null==g?void 0:g.ExpireYear)||"",ExpiryMonth:(null===(p=Rt.inputCreditCardExpiryMonth.current)||void 0===p?void 0:p.value)?null===(b=Rt.inputCreditCardExpiryMonth.current)||void 0===b?void 0:b.value:(null==g?void 0:g.ExpireMonth)||"",SecurityCode:(null===(f=Rt.inputCreditCardSecurityCode.current)||void 0===f?void 0:f.value)?null===(E=Rt.inputCreditCardSecurityCode.current)||void 0===E?void 0:E.value:(null==g?void 0:g.SecurityCode)||""}),O(null===(y=Rt.inputCreditCardNumber.current)||void 0===y?void 0:y.value),M(null===(_=Rt.inputCreditCardHolderName.current)||void 0===_?void 0:_.value),L(null===(v=Rt.inputCreditCardExpiryMonth.current)||void 0===v?void 0:v.value,null===(A=Rt.inputCreditCardExpiryYear.current)||void 0===A?void 0:A.value),D(null===(C=Rt.inputCreditCardSecurityCode.current)||void 0===C?void 0:C.value)),ot(!1),et(!0)},E.useEffect(function(){var e,t,n,r,a,i,o,l,s,d;Je&&(null==P||P.errors.map(function(e){switch(e.field){case Y.CardNumber:he(!0);break;case Y.CardHolderName:Re(!0);break;case Y.ExpirationDate:De(!0);break;case Y.SecurityCode:Oe(!0);break;case Y.BankAccountHolderName:ze(!0);break;case Y.BankName:ke(!0);break;case Y.BankTransitCode:Fe(!0);break;case Y.BankAccountNumber:je(!0)}}),(o=!!(null===(e=null==P?void 0:P.errors)||void 0===e?void 0:e.length)&&P.errors.length>0)&&mt(!0),o?!o&&m&&!Ke&&f&&(o||u()):(V({cardNumber:(null===(t=Rt.inputCreditCardNumber.current)||void 0===t?void 0:t.value)||"",cardType:On((null===(n=Rt.inputCreditCardNumber.current)||void 0===n?void 0:n.value)||""),cardName:(null===(r=Rt.inputCreditCardHolderName.current)||void 0===r?void 0:r.value)||"",expiryDate:"".concat((null===(a=Rt.inputCreditCardExpiryMonth.current)||void 0===a?void 0:a.value)||"","/").concat((null===(i=Rt.inputCreditCardExpiryYear.current)||void 0===i?void 0:i.value)||"")}),u()),o||(l=function(){var e,t,n,r,a,i,o,l,u,s,d,m,p,b;return{cardHolderName:(null===(e=Rt.inputCreditCardHolderName.current)||void 0===e?void 0:e.value)?null===(t=Rt.inputCreditCardHolderName.current)||void 0===t?void 0:t.value:(null==g?void 0:g.CardholderName)||"",creditCardNumber:(null===(n=Rt.inputCreditCardNumber.current)||void 0===n?void 0:n.value)?null===(r=Rt.inputCreditCardNumber.current)||void 0===r?void 0:r.value:(null==g?void 0:g.CreditCardNumber)||"",creditCardToken:(null===(a=Rt.inputCreditCardNumber.current)||void 0===a?void 0:a.value)?null===(i=Rt.inputCreditCardNumber.current)||void 0===i?void 0:i.value:(null==g?void 0:g.CreditCardNumber)||"",expirationMonth:(null===(o=Rt.inputCreditCardExpiryMonth.current)||void 0===o?void 0:o.value)?null===(l=Rt.inputCreditCardExpiryMonth.current)||void 0===l?void 0:l.value:(null==g?void 0:g.ExpireMonth)||"",expirationYear:(null===(u=Rt.inputCreditCardExpiryYear.current)||void 0===u?void 0:u.value)?null===(s=Rt.inputCreditCardExpiryYear.current)||void 0===s?void 0:s.value:(null==g?void 0:g.ExpireYear)||"",securityCode:(null===(d=Rt.inputCreditCardSecurityCode.current)||void 0===d?void 0:d.value)?null===(m=Rt.inputCreditCardSecurityCode.current)||void 0===m?void 0:m.value:(null==g?void 0:g.SecurityCode)||"",cardType:(null===(p=Rt.inputCreditCardHolderName.current)||void 0===p?void 0:p.value)?Rn(null===(b=Rt.inputCreditCardHolderName.current)||void 0===b?void 0:b.value):((null==g?void 0:g.CreditCardType)?c(g.CreditCardType):"")||""}}(),1===p.length&&(s=p[0],Ct?X(s.Ban,s.AccountType===w.OneBill,yt,re,Ct,s.subscriberId):$(l,s.Ban,s.AccountType===w.OneBill,bt,Ct,s.subscriberId)),p.length>1&&Q&&Q.length>0&&(Ct?X(Q[0].Ban,Q[0].AccountType===w.OneBill,yt,re,Ct,Q[0].subscriberId):$(l,Q[0].Ban,Q[0].AccountType===w.OneBill,bt,Ct,Q[0].subscriberId))),d=new Er,B(d),et(!1))},[Je]),E.useEffect(function(){ee&&Q&&Q.length>0&&X(Q[0].Ban,Q[0].AccountType===w.OneBill,bt,re,Ct,Q[0].subscriberId)},[ee]),l=function(){var e,t,n,r,a,i,o,l,u,s={isValid:!0,validationForm:{bankNameError:{isEmpty:!1,isInvalid:!1},bankAccountHolderError:{isEmpty:!1,isInvalid:!1},transitNumberError:{isEmpty:!1,isInvalid:!1},bankAccountNumberError:{isEmpty:!1,isInvalid:!1}}};return(null===(e=Rt.inputBankName.current)||void 0===e?void 0:e.value)||(s.isValid=!1,s.validationForm.bankNameError.isEmpty=!0),(null===(t=Rt.inputBankAccountHolder.current)||void 0===t?void 0:t.value)?(null===(n=Rt.inputBankAccountHolder.current)||void 0===n?void 0:n.value)&&(Sn.test(null===(r=Rt.inputBankAccountHolder.current)||void 0===r?void 0:r.value.trim())&&(null===(a=Rt.inputBankAccountHolder.current)||void 0===a?void 0:a.value.trim().length)<=70||(s.isValid=!1,s.validationForm.bankAccountHolderError.isInvalid=!0)):(s.isValid=!1,s.validationForm.bankAccountHolderError.isEmpty=!0),(null===(i=Rt.inputTransitNumber.current)||void 0===i?void 0:i.value)?(null===(o=Rt.inputTransitNumber.current)||void 0===o?void 0:o.value.length)<5&&(s.isValid=!1,s.validationForm.transitNumberError.isInvalid=!0):(s.isValid=!1,s.validationForm.transitNumberError.isEmpty=!0),(null===(l=Rt.inputBankAccountNumber.current)||void 0===l?void 0:l.value)?(null===(u=Rt.inputBankAccountNumber.current)||void 0===u?void 0:u.value.length)<7&&(s.isValid=!1,s.validationForm.bankAccountNumberError.isInvalid=!0):(s.isValid=!1,s.validationForm.bankAccountNumberError.isEmpty=!0),s},u=function(){U||k?H(j.CurrentBalance):(H(j.TermsAndCondition),F(!1))},d=function(){H(j.PaymentMethod)},m=null==x?void 0:x.some(function(e){var t;return null!==(t=e.IsOnPreauthorizedPayments)&&void 0!==t&&t}),p=x,b=x.find(function(e){return e.BankAccountDetails}),f=x.find(function(e){return e.CreditCardDetails}),y=ae&&ae.data?ae.data:null,_=x.filter(function(e){return!0===e.IsOnPreauthorizedPayments&&e.CreditCardDetails}),g=_.length>0?_[0].CreditCardDetails:null,v=x.filter(function(e){return!0===e.IsOnPreauthorizedPayments&&e.BankAccountDetails}),N=v.length>0?v[0].BankAccountDetails:null,A=function(e){fe(e.target.value);var t=On(e.target.value);_e(t)},C=function(){window.location.href=te.externalRedirectUrl},h=function(e){var t,n;(null===(n=null===(t=It.manualDetails)||void 0===t?void 0:t.current)||void 0===n?void 0:n.checked)?st(!0):st(!1)},T=function(e){Nt(e.target.value)},E.useEffect(function(){vt===I.formatMessage({id:"BANK_ACCOUNT_LABEL"})?(ht(!0),W(!0)):(ht(!1),W(!1))},[vt]),E.useEffect(function(){null!=ne&&"SUCCESS"===ne.status?rt(!0):rt(!1)},[ne]),E.useEffect(function(){G!==j.PaymentMethod||it&&y||""===de&&ue({error:"",s_oILI:""})},[G]),E.useEffect(function(){Q.length>0&&G===j.PaymentMethod&&(!de||""===de||it&&y?it&&y&&me():ue({error:"",s_oILI:de}))},[de,G,Q]),E.useEffect(function(){G===j.PaymentMethod&&dt&&(Ct?(ue({error:"BANKERROR",s_oILI:de}),mt(!1)):(ue({error:"CREDITERROR",s_oILI:de}),mt(!1)))},[dt]),E.createElement(E.Fragment,null,E.createElement(E.Fragment,null,E.createElement("div",{className:["payment-border-b payment-border-gray-4",G>j.PaymentMethod?"payment-hidden":""].join(" ").trim()},E.createElement("div",null,E.createElement(pn.HeadingStep,{disableSrOnlyText:!0,status:R,subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",title:I.formatMessage({id:"SELECT_PAYMENT_METHOD_HEADING"}),"aria-hidden":G===j.PaymentMethod?"true":void 0,id:G===j.PaymentMethod?"payment-setup-heading":void 0})),Ce||Ie||Le||Se||Ve||Ye||Pe||Ue?E.createElement("div",{className:"payment-pt-30"},E.createElement(lr,{isErrorCardNumber:Ce,isErrorCardName:Ie,isErrorSecurityCode:Se,isErrorExpiryDate:Le,isErrorBankAccountHolderName:Ve,isErrorBankAccountNumber:Ye,isErrorBankName:Pe,iserrorBankTransit:Ue,inputRefs:Rt})):E.createElement(E.Fragment,null),it&&y&&E.createElement("div",{className:"".concat("active"===R?"":"payment-hidden"," payment-pt-30")},E.createElement(sr,{interact:y})),E.createElement("div",{role:"radiogroup","aria-labelledby":"payment-method"},E.createElement("div",{className:"".concat("active"===R?"":"payment-hidden"," payment-pt-30")},E.createElement(Gn,{Checked:!0,isInteracSelected:nt,checkedBillItems:Q,radioCardRef:It,handleBankRadioManualDetailsChange:h,isBankManualEnterDetails:ut,isPreauth:m,hasBankAccountDetails:b,bankitems:p,handleBankRadioChange:St,bankListInterac:xt,handleInteracSubmit:C,isBankChecked:Ke,inputRefs:Rt,errorBankName:Pe,errorBankTransit:Ue,errorBankAccountNumber:Ye,errorBankAccountHolderName:Ve,radioRef:Tt,bankList:Z,onChange:T,creditCardAutopayOffers:ie,debitCardAutopayOffers:oe,language:le,isInteractEnabled:se,IsAutopayCreditEnabled:ce}),E.createElement(zn,{isPreauth:m,hasCreditCardDetails:f,bankitems:p,radioRef:Tt,handleBankRadioChange:St,isBankChecked:Ke,cardNumber:be,handleCreditCardChange:A,inputRefs:Rt,cardIcons:n,cardType:ye,errorCardNumber:Ce,errorCardName:Ie,errorExpiryDate:Le,errorSecurityCode:Se,handleMaskCVV:t,CVV:ve,onChange:T,checkedBillItems:Q,creditCardAutopayOffers:ie,debitCardAutopayOffers:oe,language:le,IsAutopayCreditEnabled:ce}),E.createElement("div",{className:"payment-pt-15 payment-pb-45 sm:payment-pb-60"},E.createElement(pn.Button,{variant:"primary",onClick:i,disabled:!1},I.formatMessage({id:"CTA_NEXT"})))))),E.createElement(Wn,{className:"payment-hidden",onEditClick:d}),E.createElement(pr,{paymentItem:p,className:G>j.PaymentMethod?"":"payment-hidden",isPreauth:m,inputValue:z,inputBankValue:K,isNewbank:null!=Ke&&Ke,onEditClick:d,showHeading:!0,isBankPaymentSelected:Ct,isSingleClickEnable:U||k,bankList:Z,debitCardAutopayOffers:oe,creditCardAutopayOffers:ie,checkedBillItems:Q,bankitems:p,isConfirmation:!1,IsAutopayCreditEnabled:ce,isShow:G>j.PaymentMethod})))},_r=function(e){return{creditcardDetails:e.creditCardDetails,validationErrors:e.validationErrors,cardTokenizationSuccess:e.cardTokenizationSuccess,redirectUrl:e.redirectUrl,interacBankInfo:e.interacBankInfo,interactBankFailureInfo:e.interactBankFailureInfo}},gr=function(e){return{onCreditCardNumberChange:function(t){var n,r,a,i,o=new V;o.CreditCardNumber=t,t||(r=t,(i=new Er).errors=new Array,a=new Array,r||a.push(fr.Empty),n=a&&a.length>0?(i.errors.push({valErrors:a,field:Y.CardNumber}),i):[],e(ae(n))),(!n||n.length<=0||!n.errors||n.errors.length<=0)&&e(ee(o))},onCardHolderNameChange:function(t){var n,r,a,i,o=new V,l=(r=t,(i=new Er).errors=new Array,a=new Array,r&&/^(?:[A-Za-zÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇàâäèéêëîïôœùûüÿç'-]{2,}\.?\s)+(?:[A-Za-zÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇàâäèéêëîïôœùûüÿç'-]{1}\.?\s)*[A-Za-zÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇàâäèéêëîïôœùûüÿç'-]{2,}\.?$/.test(r.trim())&&r.trim().length<=70||a.push(fr.Empty),a&&a.length>0?(i.errors.push({valErrors:a,field:Y.CardHolderName}),i):[]);o.CardholderName=t,(null===(n=null==l?void 0:l.errors)||void 0===n?void 0:n.length)>0&&e(ae(l)),(!l||l.length<=0||!l.errors||l.errors.length<=0)&&e(te(o))},onCreditCardExpiryDateChange:function(t,n){var r,a=new V;a.ExpireMonth=t,a.ExpireYear=n,r=function(e,t){var n,r,a,i,o=new Er;return o.errors=new Array,n=new Array,r=""===t?t:parseInt(t),(a=""===e?e:parseInt(e))>=12&&(a=0,r+=1),i=new Date(r<=49?2e3+r:1900+r,a,0),""===r.toString()||""===a.toString()?n.push(fr.Empty):i<new Date&&n.push(fr.CreditCardExpireDate),n&&n.length>0?(o.errors.push({valErrors:n,field:Y.ExpirationDate}),o):[]}(t,n),(r.length>0||r.errors)&&e(ae(r)),(!r||r.length<=0||!r.errors||r.errors.length<=0)&&e(re(a))},onSecurityCodeChange:function(t){var n,r,a,i,o=new V;o.SecurityCode=t,t||(r=t,(i=new Er).errors=new Array,a=new Array,r||a.push(fr.Empty),n=a&&a.length>0?(i.errors.push({valErrors:a,field:Y.SecurityCode}),i):[],e(ae(n))),(!n||n.length<=0||!n.errors||n.errors.length<=0)&&e(ne(o))},validatBankDetails:function(t){var n=function(e){var t,n=new Er;return n.errors=new Array,(t=e.validationForm).bankNameError&&(t.bankNameError.isEmpty&&n.errors.push({valErrors:[fr.Empty],field:Y.BankName}),t.bankNameError.isInvalid&&n.errors.push({valErrors:[fr.Invalid],field:Y.BankName})),t.bankAccountHolderError&&(t.bankAccountHolderError.isEmpty&&n.errors.push({valErrors:[fr.Empty],field:Y.BankAccountHolderName}),t.bankAccountHolderError.isInvalid&&n.errors.push({valErrors:[fr.Invalid],field:Y.BankAccountHolderName})),t.transitNumberError&&(t.transitNumberError.isEmpty&&n.errors.push({valErrors:[fr.Empty],field:Y.BankTransitCode}),t.transitNumberError.isInvalid&&n.errors.push({valErrors:[fr.Invalid],field:Y.BankTransitCode})),t.bankAccountNumberError&&(t.bankAccountNumberError.isEmpty&&n.errors.push({valErrors:[fr.Empty],field:Y.BankAccountNumber}),t.bankAccountNumberError.isInvalid&&n.errors.push({valErrors:[fr.Invalid],field:Y.BankAccountNumber})),e.isValid?[]:n}(t);t.isValid?e(ie({errors:[]})):e(ae(n))},resetValidationErrors:function(t){e(ie(t))},validateFormOrder:function(t,n,r,a,i,o){e(_e({ban:t,type:n,details:r,accountInputValue:a,isBankPaymentSelected:i,sub:o}))},tokenizeAndPropagateFormValues:function(t,n,r,a,i,o){e(he({form:t,ban:n,type:r,details:a,isBankPaymentSelected:i,sub:o}))},setOmnitureOnPaymentSelect:function(t){e(He({data:t}))},setOmnitureOnInteracFailure:function(t){e(Qe(t))}}},vr=(0,_.connect)(_r,gr)((0,mn.injectIntl)(yr)),Nr=function(e){var t=e.collapseHeightDynamic,n=e.expandHeightDynamic,r=e.intl,a=e.onSubmitClick,i=e.onCancelClick,o=(e.province,e.language),l=e.userProfileProv,u=(0,pn.useWindowResize)(100).width,s=function(e){return"height"in e?e.height:""},c=s(t?(0,pn.useResponsiveHeight)(u,t):{height:"90px"}),d=s(n?(0,pn.useResponsiveHeight)(u,n):{height:"460px"}),m=r.formatMessage({id:"TERMS_AND_CONDITION_DISCLAIMER"}),p=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1"}),b=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_2"}),f=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_2_ITEM1"}),y=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_2_ITEM2"}),_=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM3"}),g=r.formatMessage({id:"TERMS_AND_CON_DESC_2"}),v=r.formatMessage({id:"TERMS_AND_CON_DESC_3"}),N=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_QC"}),A=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_2_QC"}),C=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_2_ITEM1_QC"}),h=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_2_ITEM2_QC"}),T=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM3_QC"}),I=r.formatMessage({id:"TERMS_AND_CON_DESC_2_QC"}),R=r.formatMessage({id:"TERMS_AND_CON_DESC_3_QC"});return E.createElement("div",null,E.createElement("div",null,E.createElement(pn.Accordion,{mode:"single"},E.createElement(pn.AccordionItem,{key:1,index:1},E.createElement(pn.AccordionContent,{"aria-labelledby":"terms-and-condition-trigger",id:"terms-and-condition-content",collapseHeight:c,expandHeight:d,className:"brui-text-14 brui-text-gray brui-leading-18 brui-pr-20 payment-overflow-y-scroll payment-scrollbar"},"QC"===l&&"en"===o?E.createElement("div",null,E.createElement("p",null,E.createElement("p",{className:"payment-mb-15"},r.formatMessage({id:"TERMS_AND_CON_DESC_1_QC"})),E.createElement("p",{dangerouslySetInnerHTML:{__html:N||""}}),E.createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10 payment-ml-10"},E.createElement("li",null,E.createElement("span",{className:"payment-ml-[-6px]"},r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM1_QC"}))),E.createElement("li",null,E.createElement("span",{className:"payment-ml-[-6px]"},r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM2_QC"}))),E.createElement("li",null,E.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:T||""}}))),E.createElement("p",{dangerouslySetInnerHTML:{__html:A||""}}),E.createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10"},E.createElement("li",null,E.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:C||""}})),E.createElement("li",null,E.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:h||""}}))),E.createElement("p",{className:"payment-mb-15",dangerouslySetInnerHTML:{__html:I||""}}),E.createElement("p",{className:"payment-mb-15",dangerouslySetInnerHTML:{__html:R||""}}),E.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_DESC_4_QC"})),E.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_ZIP_CODE_QC"})),E.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_REGION_QC"})),E.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_REGION_2_QC"})),E.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_TEL_QC"}))),E.createElement("p",{className:"payment-mt-10"},E.createElement("p",{className:"payment-mb-15"},r.formatMessage({id:"TERMS_AND_CON_DESC_1"})),E.createElement("p",{dangerouslySetInnerHTML:{__html:p||""}}),E.createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10 payment-ml-10"},E.createElement("li",null,E.createElement("span",{className:"payment-ml-[-6px]"},r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM1"}))),E.createElement("li",null,E.createElement("span",{className:"payment-ml-[-6px]"},r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM2"}))),E.createElement("li",null,E.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:_||""}}))),E.createElement("p",{dangerouslySetInnerHTML:{__html:b||""}}),E.createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10 payment-ml-10"},E.createElement("li",null,E.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:f||""}})),E.createElement("li",null,E.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:y||""}}))),E.createElement("p",{className:"payment-mb-15",dangerouslySetInnerHTML:{__html:g||""}}),E.createElement("p",{className:"payment-mb-15",dangerouslySetInnerHTML:{__html:v||""}}),E.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_DESC_4"})),E.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_ZIP_CODE"})),E.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_REGION"})),E.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_REGION_2"})),E.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_TEL"})))):E.createElement("div",null,E.createElement("p",null,E.createElement("p",{className:"payment-mb-15"},r.formatMessage({id:"TERMS_AND_CON_DESC_1"})),E.createElement("p",{dangerouslySetInnerHTML:{__html:p||""}}),E.createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10 payment-ml-10"},E.createElement("li",null,E.createElement("span",{className:"payment-ml-[-6px]"},r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM1"}))),E.createElement("li",null,E.createElement("span",{className:"payment-ml-[-6px]"},r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM2"}))),E.createElement("li",null,E.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:_||""}}))),E.createElement("p",{dangerouslySetInnerHTML:{__html:b||""}}),E.createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10 payment-ml-10"},E.createElement("li",null,E.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:f||""}})),E.createElement("li",null,E.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:y||""}}))),E.createElement("p",{className:"payment-mb-15",dangerouslySetInnerHTML:{__html:g||""}}),E.createElement("p",{className:"payment-mb-15",dangerouslySetInnerHTML:{__html:v||""}}),E.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_DESC_4"})),E.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_ZIP_CODE"})),E.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_REGION"})),E.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_REGION_2"})),E.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_TEL"}))))),E.createElement("div",{className:"payment-pt-15"},E.createElement(pn.AccordionTrigger,{id:"terms-and-condition-trigger","aria-controls":"terms-and-condition-content",className:"brui-text-14 brui-text-blue brui-underline brui-leading-18 hover:brui-text-blue-1 hover:brui-no-underline"},E.createElement(pn.AccordionToggleTitle,{titleExpand:r.formatMessage({id:"CTA_EXPAND_TERMS"}),titleCollapse:r.formatMessage({id:"CTA_COLLAPSE_TERMS"})})))))),E.createElement("div",{className:"payment-bg-gray-3 payment-mx-[-15px] sm:payment-mx-[-30px] md:payment-mx-[-15px] payment-mt-30"},E.createElement(pn.Divider,{width:1,direction:"horizontal"}),E.createElement("div",{className:"payment-px-15 sm:payment-px-30 md:payment-px-15 payment-pt-30 payment-pb-45"},E.createElement("div",{className:"brui-text-gray brui-text-14 brui-leading-18 payment-max-w-[500px]"},E.createElement("div",{dangerouslySetInnerHTML:{__html:m||""}})),E.createElement("div",{className:"brui-inline-flex brui-flex-wrap brui-items-center"},E.createElement("div",{className:"payment-pr-30 payment-pt-30"},E.createElement(pn.Button,{variant:"primary",size:"regular",onClick:a},r.formatMessage({id:"CTA_CONFIRM"}))),E.createElement("div",{className:"payment-pt-30"},E.createElement(pn.Button,{variant:"textBlue",size:"regular",className:"!brui-text-14 brui-leading-18",onClick:i},r.formatMessage({id:"CTA_CANCEL"})))))))},Ar=(0,mn.injectIntl)(Nr),Cr=function(e){var t=e.intl,n=e.variant,r=void 0===n?"default":n;return E.createElement("div",{className:"payment-bg-black payment-bg-opacity-60 payment-fixed payment-w-full payment-h-full  payment-z-20 payment-left-0 payment-top-0 payment-inline-flex payment-items-center payment-justify-center"},E.createElement("div",{id:"brf-page-loader",role:"alert","aria-busy":"true","aria-live":"assertive",className:"payment-inline-flex payment-items-center payment-py-15 payment-px-30 payment-shadow-md payment-bg-white"},E.createElement("svg",{className:"payment-animate-spin payment-size-[36px] payment-mr-10",xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",viewBox:"3 3 42 42"},E.createElement("defs",null,E.createElement("linearGradient",{id:"loadingIndicatorGradient1",x1:"0",x2:"0",y1:"10%",y2:"90%"},E.createElement("stop",{offset:"0","stop-color":"#04569b"}),E.createElement("stop",{offset:"1","stop-color":"#97b6d2"})),E.createElement("linearGradient",{id:"loadingIndicatorGradient2",x1:"0",x2:"0",y1:"90%",y2:"10%"},E.createElement("stop",{offset:"0","stop-color":"#97b6d2"}),E.createElement("stop",{offset:"1","stop-color":"#fff"}))),E.createElement("path",{fill:"url(#loadingIndicatorGradient1)",d:"M24,3C12,3,3,12,3,24s9,21,21,21l-0.1-2.5c-5.3,0-10.1-2.2-13.5-6C7.4,33.1,5.5,28.3,5.5,24\r\n        c0-4.4,1.9-9.5,5.3-12.9c3.5-3.6,8.6-5.6,13.2-5.6L24,3z"}),E.createElement("path",{fill:"url(#loadingIndicatorGradient2)",d:"M24,3l0,2.4c5.5,0,10.8,2.8,14.3,6.8c2.8,3.4,4.2,7.6,4.2,11.7c0,4.7-2,9.7-5.7,13.3c-3.3,3.3-8.1,5.3-12.9,5.3\r\n        l0,2.5c12,0,21-10,21-21S36,3,24,3z"})),"default"===r&&E.createElement(E.Fragment,null,t.formatMessage({id:"LOADER"})),"submitOrder"===r&&E.createElement("div",{className:"payment-text-12 payment-leading-14"},E.createElement("p",{className:"payment-font-bold"},t.formatMessage({id:"LOADER_SUBMIT"})),E.createElement("p",null,t.formatMessage({id:"LOADER_SUBMIT_DESC"})))))},hr=(0,mn.injectIntl)(Cr),Tr=function(e){var t=e.isActive,n=e.intl,r=(e.onCurrentSteps,e.setCurrentSection),a=e.currentSection,i=e.checkedBillItems,l=e.submitFormOrder,u=e.paymentItem,s=e.province,c=e.language,d=e.accountInputValues,m=e.setOmnitureOnReview,p=e.isBankSelected,b=e.validateMultiOrderFormStatus,f=e.tokenizeAndPropagateFormValuesStatus,y=e.setApiSatusIsFailed,_=e.userProfileProv,g=e.setOmnitureOnValidationFailure,v=e.creditCardAutopayOffers,N=e.debitCardAutopayOffers,A=e.bankitems,C=(e.sorryCredit,e.sorryDebit,o(E.useState($e.IDLE),2)),h=C[0],T=C[1],I=o(E.useState($e.IDLE),2),R=I[0],x=I[1],S=function(){var e=[];return N&&(null==N||N.map(function(t){i&&i.map(function(n){t.Ban===n.Ban&&e.push(t)})})),e},O=function(){return A&&A.length>1?S().reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0):N&&N.reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0)},M=function(){return A&&A.length>1?S().reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0):v&&v.reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0)},L=v&&v.length>0&&0===M()&&O()>0,D=N&&N.length>0&&0===O()&&M()>0,B=E.useRef(null);return E.useEffect(function(){var e=setTimeout(function(){if(B.current&&t){var e=B.current.querySelector("h2");e&&(e.scrollIntoView({behavior:"smooth"}),e.focus())}},500);return function(){return clearTimeout(e)}},[t]),E.useEffect(function(){a!==j.TermsAndCondition||h!==$e.COMPLETED&&R!==$e.COMPLETED||m()},[a,h,R]),E.useEffect(function(){p||(f!==$e.FAILED?x($e.PENDING):x(f))},[f]),E.useEffect(function(){p?(T(b),b===$e.FAILED&&(y(!0),g())):x(b)},[b]),E.useEffect(function(){R===$e.FAILED&&(y(!0),g())},[R]),E.createElement(E.Fragment,null,(h===$e.PENDING||R===$e.PENDING)&&E.createElement(hr,null),E.createElement(E.Fragment,null,E.createElement("div",{ref:B,className:"sm:payment-mb-[90px] payment-border-b payment-border-gray-4"},E.createElement("div",{id:"termsAndCondDivID",className:t?"focus-visible:payment-outline-none":""},E.createElement(pn.HeadingStep,{disableSrOnlyText:!0,tabIndex:-1,className:"focus-visible:payment-outline-none",status:t?"active":"inactive",subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",title:"QC"===s&&"en"===c?n.formatMessage({id:"TERMS_AND_CONDITION_HEADING_QC"}):n.formatMessage({id:"TERMS_AND_CONDITION_HEADING"})})),E.createElement("div",{className:["payment-pt-15 sm:payment-pt-5",t?"":"payment-hidden"].join(" ").trim()},E.createElement(Ar,{onSubmitClick:function(){u.length>1&&i&&i.length>0&&l(i[0].Ban,i[0].AccountType===w.OneBill,p,L,D,d,i[0].subscriberId),u&&1===u.length&&l(i[0].Ban,i[0].AccountType===w.OneBill,p,L,D,d,i[0].subscriberId),r(j.Confirmation)},onCancelClick:function(){r(j.PaymentMethod)},collapseHeightDynamic:{mobile:{height:"234px"},tablet:{height:"90px"},desktop:{height:"90px"}},expandHeightDynamic:{mobile:{height:"415px"},tablet:{height:"460px"},desktop:{height:"460px"}},province:s,language:c,userProfileProv:_})))))},Ir=function(e){return{submitMultiOrderPayment:e.submitMultiOrderPayment,validateMultiOrderFormStatus:e.validateMultiOrderFormStatus,tokenizeAndPropagateFormValuesStatus:e.tokenizeAndPropagateFormValuesStatus}},Rr=function(e){return{submitFormOrder:function(t,n,r,a,i,o,l){e(Ne({ban:t,type:n,isbankSelected:r,sorryCredit:a,sorryDebit:i,details:o,sub:l}))},setOmnitureOnReview:function(){e(je({}))},setOmnitureOnValidationFailure:function(t){return e(Xe({data:t}))}}},xr=(0,_.connect)(Ir,Rr)((0,mn.injectIntl)(Tr)),Sr=(0,E.forwardRef)(function(e,t){var n,r,a=e.className,i=e.isDisabled,u=e.isChecked,s=e.label,c=e.id,d=e.billType,m=e.billAccountNumber,p=e.idIndex,f=e.text,E=e.priceSettings,_=e.item,g=e.isCheckedItems,v=e.setIsCheckedItems,N=(e.onChange,e.isShowLabel),A=e.paymentItems,C=e.intl,h=o(y().useState([]),2),T=h[0],I=h[1],R=_.Ban;return y().useEffect(function(){var e,t=document.querySelectorAll("input[type='checkbox']"),n=[];t.forEach(function(e){var t,r,a;e.checked&&(t=e.getAttribute("data-bandetail"),a=(r=t&&JSON.parse(t))&&r.ban&&A.filter(function(e){return e.Ban===r.ban}),a&&a.length>0&&n.push(a[0]))}),n&&n.length>0&&n.map(function(e){return null!==e})&&(e=n.filter(function(e){return null!==e}),I(function(t){return t.concat(l([],o(e),!1))}))},[]),y().useEffect(function(){if(null!==T&&T.length>0){var e=T.reduce(function(e,t){return e.find(function(e){return t.Ban===e.Ban})||e.push(b({},t)),e},[]);v(e)}},[T]),n=E?C.formatMessage({id:"CHECKBOX_BALANCE"},{balance:E.price}):void 0,r=E?C.formatMessage({id:"CHECKBOX_BALANCE_SR"},{balance:E.price}):void 0,y().createElement(pn.CheckboxCard,{ref:t,id:c,"aria-labelledby":s,disabled:i,defaultChecked:u,className:["group-has-[:disabled]/inputcheckbox:payment-bg-gray-3 payment-chekcbox-disabled payment-group/checkboxcard payment-pt-30 payment-pl-30 brui-pr-30 payment-pb-40 sm:payment-pb-30 sm:payment-p30 payment-basis-unset sm:payment-basis-p48 md:payment-basis-p35 lg:payment-basis-1/3 brui-w-full payment-mr-15 brui-mb-15",a].join(" ").trim(),defaultPadding:!1,checkboxPlacement:"topLeft","data-banDetail":JSON.stringify({id:c,billAccountNumber:m,ban:R,billType:d,price:null==E?void 0:E.price}),onChange:function(e){return function(e,t){e.target.checked?v(l(l([],o(g),!1),[t],!1)):v(function(e){return e.filter(function(e){return e.BillName!==t.BillName})})}(e,_)}},y().createElement("div",{className:"sm:brui-flex payment-pl-[25px] sm:payment-pl-[35px] payment-pr-[15px] sm:payment-pr-[30px] payment-flex-col payment-relative payment-top-[5px] sm:payment-top-0 sm:payment-min-h-[48px]"},y().createElement("div",{className:"brui-flex brui-w-max brui-items-center",id:"checkboxBill".concat(p,"-label-").concat(p)},y().createElement(pn.Text,{elementType:"span",className:"brui-font-bold"},d," ",y().createElement(pn.Text,{elementType:"span",className:"brui-text-14 brui-text-gray !payment-font-normal"},m))),N&&y().createElement("div",{className:"brui-flex brui-w-fit brui-items-center payment-mr-[15px]",id:"checkboxBillBalance-".concat(p,"-label-").concat(p)},E?y().createElement(y().Fragment,null,y().createElement("span",{"aria-hidden":"true",className:"brui-text-14 brui-text-gray brui-break-words brui-flex brui-items-center brui-leading-18"},f),y().createElement("div",{className:"brui-font-bold brui-text-18 brui-text-blue brui-text-darkblue !payment-text-14 brui-leading-14 payment-m-5","aria-hidden":"true"},y().createElement("span",{className:""},n)),r&&y().createElement("span",{className:"brui-sr-only"},r)):y().createElement(pn.Text,{elementType:"span",className:"brui-text-14 brui-text-gray brui-break-words brui-flex brui-items-center brui-leading-18"},f)),y().createElement("div",{className:"payment-flex payment-w-fit payment-items-top",id:"checkboxBill".concat(p,"-label-").concat(p)},!N&&y().createElement(y().Fragment,null,y().createElement(pn.Icon,{className:"payment-text-yellow payment-mr-5",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"}),y().createElement(pn.Text,{elementType:"span",className:"payment-text-14 payment-text-gray payment-break-words payment-flex payment-items-center payment-leading-18"},C.formatMessage({id:"NOT_ON_PREAUTH"}))))))}),Or=function(e){var t=e.intl,n=e.isActive,r=e.onIconLinkClick,a=(e.banDetails,e.isCheckedItems),i=e.isShow,o={MyBill:t.formatMessage({id:"ACCOUNT_TYPENAME_MYBILL"}),Mobility:t.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY"}),OneBill:t.formatMessage({id:"ACCOUNT_TYPENAME_ONEBILL"}),TV:t.formatMessage({id:"ACCOUNT_TYPENAME_TV"}),Internet:t.formatMessage({id:"ACCOUNT_TYPENAME_INTERNET"}),HomePhone:t.formatMessage({id:"ACCOUNT_TYPENAME_HOMEPHONE"}),MobilityAndOneBill:t.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY_ONEBILL"}),SingleBan:t.formatMessage({id:"ACCOUNT_TYPENAME_SINGLEBAN"})},l=a.map(function(e){return{label:y().createElement(mn.FormattedMessage,{id:"SELECT_BILLS_ACCOUNT_TITLE",values:{accounttype:u(e.AccountType,e.IsNM1Account,o)}}),value:e.NickName}});return y().createElement("div",{className:n?"payment-mb-45 payment-block":"sm:payment-mb-15 payment-border-b payment-border-gray-4 payment-hidden"},y().createElement("div",{className:"payment-flex payment-items-center payment-justify-between"},y().createElement(pn.HeadingStep,{disableSrOnlyText:!0,status:n?"complete":"inactive",subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",title:l.length>1?t.formatMessage({id:"SELECT_BILLS_HEADING"}):t.formatMessage({id:"SELECT_BILLS_HEADING_SINGULAR"}),id:i?"payment-setup-heading":void 0,"aria-hidden":i?"true":void 0}),y().createElement("div",{className:"payment-pt-45"},y().createElement(pn.IconLink,{icon:y().createElement(pn.Icon,{iconClass:"bi_brui",iconName:"bi_edit_pencil",className:"brui-text-16"}),text:t.formatMessage({id:"CTA_EDIT"}),variant:"textBlue",size:"regular",href:"javascript:void(0);",position:"right","aria-label":t.formatMessage({id:"ARIA_EDIT_PAYMENT_METHOD"}),role:"button",className:["payment-flex payment-items-center !payment-text-14 !payment-leading-18",n?"":"payment-hidden"].join(" ").trim(),onClick:function(){return r&&r(1)}}))),y().createElement("div",{className:["brui-pb-45",n?"":"brui-hidden"].join(" ").trim()},y().createElement("div",{className:"brui-mt-15"},l.map(function(e,t){return y().createElement(qn,{className:t>0?"brui-mt-5":"",label:e.label,value:e.value})}))))},Mr=(0,mn.injectIntl)(Or),Lr=(0,E.forwardRef)(function(e,t){var n=e.className,r=e.isDisabled,a=e.isChecked,i=e.label,u=e.id,s=e.billType,c=e.billAccountNumber,d=e.idIndex,m=e.text,p=e.priceSettings,b=e.currentItem,f=e.isCheckedBalanceItems,E=e.setIsCheckedBalanceItems,_=e.intl,g=p?_.formatMessage({id:"PAY_MY_BALANCE_SR"},{balance:p.price}):void 0;return y().createElement(pn.CheckboxCard,{ref:t,id:u,"aria-labelledby":i,disabled:r,defaultChecked:a,className:["group-has-[:disabled]/inputcheckbox:payment-bg-gray-3 payment-chekcbox-disabled payment-group/checkboxcard payment-pt-30 payment-pl-30 brui-pr-30 payment-pb-30 sm:payment-p30 payment-basis-unset sm:payment-basis-p48 md:payment-basis-p35 brui-w-full payment-mr-15 brui-mb-15",n].join(" ").trim(),defaultPadding:!1,checkboxPlacement:"topLeft","data-banDetail":JSON.stringify({id:u,billAccountNumber:c,billType:s,price:null==p?void 0:p.price}),onChange:function(e){return t=b,void(e.target.checked?E(l(l([],o(f),!1),[t],!1)):E(function(e){return e.filter(function(e){return e.BillName!==t.BillName})}));var t}},y().createElement("div",{className:"sm:payment-flex sm:payment-pl-[33px] payment-pl-[24px]"},y().createElement("div",{className:"payment-mt-2 sm:payment-mt-1 payment-flex-1 sm:payment-text-left",id:"checkboxBalance-".concat(d,"-label-").concat(d)},y().createElement(pn.Text,{"aria-hidden":"true",elementType:"span",className:"sm:payment-pl-5 payment-text-16 sm:payment-text-18 payment-leading-20 sm:payment-leading-22 brui payment-font-bold payment-flex payment-flex-row payment-items-center payment-gap-5"},m,p&&y().createElement(pn.Price,{language:p.language?p.language:"en",negativeIndicator:p.negativeIndicator?p.negativeIndicator:"CR",price:p.price?p.price:0,variant:"defaultPrice",className:"!payment-text-[16px] !sm:payment-text-[18px] payment-leading-20 sm:payment-leading-22"})),g&&y().createElement("span",{className:"brui-sr-only"},g)),y().createElement("div",{className:"payment-mt-2 sm:payment-mt-1 payment-flex-2 sm:payment-text-right payment-leading-18",id:"checkboxBalance-".concat(d,"-label-").concat(d,"-info")},y().createElement(pn.Text,{elementType:"span",className:"payment-text-gray payment-text-14 sm:payment-text-14"}," ","on",y().createElement(pn.Text,{elementType:"span",className:"payment-font-bold payment-text-black"}," ",s," "),y().createElement("span",{"aria-hidden":"true"},c),y().createElement("span",{className:"payment-sr-only"},null==c?void 0:c.split("").join(" "))))))}),Dr=function(e){var t=e.accountinfo,n=e.className,r=e.role,a=e.childrole,i=e.children,o=e.isLabelOnError;return E.createElement("div",{className:n,role:r},E.createElement(pn.Text,{elementType:"div",className:o?"brui-font-bold brui-text-red brui-text-14 brui-leading-18 brui-mb-5":"brui-font-bold brui-text-black brui-text-14 brui-leading-18 brui-mb-5"},t),E.createElement("div",{role:a},i))},Br=function(e){var t=e.title,n=e.role,r=e.children;return E.createElement(E.Fragment,null,E.createElement("div",{className:"payment-border-b-1 payment-border-b-lightgray payment-pb-15 payment-mb-15"},E.createElement(pn.Heading,{level:"h4",variant:"default",className:"payment-block payment-font-sans payment-text-black payment-text-18 payment-leading-22"},t)),E.createElement("div",{role:n},r))},kr=function(e){var t,n=e.intl,r=e.isActive,a=e.onIconLinkClick,i=e.isCheckedBalanceItems,o=e.checkedBillItems,l=e.paymentItem,u=e.isBankPaymentSelected,s=e.currentSection,c=e.language,d=void 0===c?"en":c,m=u?n.formatMessage({id:"PAY_CURRENT_BALANCE_OPTED_IN"},{balance:new Intl.NumberFormat(d,{style:"currency",currency:"USD",currencyDisplay:"narrowSymbol"}).format(0)}):s===j.Confirmation?"":n.formatMessage({id:"PAY_CURRENT_BALANCE_OPTED_IN_PACC"}),p=n.formatMessage({id:"PAY_CURRENT_BALANCE_OPTED_OUT_SINGULAR"}),b=n.formatMessage({id:"PAY_CURRENT_BALANCE_OPTED_OUT_PLURAL"});return o.length>0&&(Pr=o.filter(function(e){return!i.some(function(t){return t.BanID===e.BanID})})),t=Pr.filter(function(e){return e.Due>0}),y().createElement("div",null,y().createElement("div",{className:r?"payment-block payment-border-gray-4 payment-border-b":"sm:payment-mb-15 payment-border-b payment-border-gray-4 payment-hidden"},y().createElement("div",null,y().createElement("div",{className:"payment-flex payment-items-center payment-justify-between payment-mt-0"},y().createElement(pn.HeadingStep,{disableSrOnlyText:!0,status:"complete",subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",title:n.formatMessage({id:"PAY_CURRENT_BALANCE_HEADING"}),id:"pre-auth-pay_curr_bal"}),y().createElement("div",{className:"payment-pt-45"},y().createElement(pn.IconLink,{icon:y().createElement(pn.Icon,{iconClass:"bi_brui",iconName:"bi_edit_pencil",className:"brui-text-16"}),text:n.formatMessage({id:"CTA_EDIT"}),variant:"textBlue",size:"regular",href:"javascript:void(0);",position:"right",className:"payment-flex payment-items-center !payment-text-14 !payment-leading-18",onClick:function(){return a&&a(1)},"aria-describedby":"pre-auth-pay_curr_bal"}))),y().createElement("div",{className:"payment-pb-45"},y().createElement("div",{className:"payment-text-gray payment-text-14 payment-mt-5"},y().createElement("p",{className:"payment-leading-18",dangerouslySetInnerHTML:{__html:0===i.length?o.length-i.length>1?b:p:i.every(function(e){return o.includes(e)})||i.some(function(e){return o.includes(e)})?m:void 0}})),i.length>0&&y().createElement("div",{className:"payment-pt-15"},l.length>1?y().createElement(y().Fragment,null,i.map(function(e){return y().createElement(Dr,{accountinfo:e.NickName,role:"list",childrole:"listitem",className:"payment-mb-15 last:payment-mb-0"},y().createElement(qn,{label:n.formatMessage({id:"PAYMENT_AMOUNT"}),value:y().createElement(pn.Price,{language:d,price:e.Due,variant:"ordinaryPrice",className:"!brui-text-14 brui-leading-18"}),needSRText:!0,srText:"".concat(e.Due," dollars"),className:"payment-text-black",isMultiBan:!0}))}),Pr.length>0&&o.length!==Pr.length&&y().createElement(y().Fragment,null,y().createElement("div",{className:"payment-text-gray payment-text-14 payment-mt-5"},y().createElement("p",{className:"payment-leading-18"},Pr.some(function(e){return e.Due>0})&&y().createElement(y().Fragment,null,t.length>1&&n.formatMessage({id:"PAY_CURRENT_BALANCE_SOME_BANS_OPTED_OUT_PLURAL"}),1===t.length&&n.formatMessage({id:"PAY_CURRENT_BALANCE_SOME_BANS_OPTED_OUT_SINGULAR"})))))):y().createElement(y().Fragment,null,i.map(function(e){return y().createElement(qn,{label:n.formatMessage({id:"PAYMENT_AMOUNT"}),value:y().createElement(pn.Price,{language:d,price:e.Due,variant:"ordinaryPrice",className:"!brui-text-14 brui-leading-18"}),needSRText:!0,srText:"".concat(e.Due," dollars"),className:"payment-text-black"})})))))))},wr=(0,mn.injectIntl)(kr),Ur=function(e){var t,n,r,a,i,l=e.intl,s=e.paymentItem,m=e.isShow,p=(e.onCurrentSteps,e.setCurrentSection),b=e.currentSection,f=e.setCheckedBillItems,_=e.paymentItems,g=e.createMultiPaymentData,v=e.accountInputValues,N=e.setAccountValues,A=e.transactionIds,C=e.createOmnitureOnLoad,h=o((0,E.useState)(!1),2),T=h[0],I=h[1],R=o((0,E.useState)(),2),x=R[0],S=R[1],O=o((0,E.useState)([]),2),M=O[0],L=O[1],D=o((0,E.useState)([]),2),B=D[0],P=D[1],k=o((0,E.useState)(!1),2),U=k[0],F=k[1],H=o((0,E.useState)(!1),2),Y=H[0],G=H[1],V=(0,E.useRef)([]),z=function(){var e,t,n;null!==M&&M.length>0&&g(M[0].Ban,M[0].AccountType===w.OneBill,v,M[0].subscriberId),!1===(n=!1,B&&B.length>0&&(t=(e=V.current.filter(function(e){return null==e?void 0:e.checked})).map(function(e){return null==e?void 0:e.getAttribute("data-banDetail")}).filter(function(e){return null!=e}),S(t),n=!(e.length<=0)),n)?I(!0):(I(!1),p(j.PaymentMethod))};return y().useEffect(function(){var e,t=sessionStorage.getItem("itemsChecked"),n=t&&JSON.parse(t);null!==n&&n.length>0?(F(!0),_.map(function(e){n.map(function(t){e.Ban===t&&(e.IsChecked=!0)})}),P(_),e=r(_),g(_[0].Ban,_[0].AccountType===w.OneBill,e,_[0].subscriberId)):P(_)},[]),y().useEffect(function(){U&&(null==B?void 0:B.length)>0&&(z(),sessionStorage.removeItem("itemsChecked"),F(!1))},[U]),t=function(e){return e.IsOnPreauthorizedPayments&&e.CreditCardDetails?y().createElement(y().Fragment,null,y().createElement(mn.FormattedMessage,{id:"SELECT_BILLS_CC_DESC",values:{CreditCardType:c(e.CreditCardDetails.CreditCardType),CCFourDigits:e.CreditCardDetails.CreditCardNumber.slice(-4),ExpiryDate:e.CreditCardDetails.ExpireMonth+"/"+e.CreditCardDetails.ExpireYear}})):e.IsOnPreauthorizedPayments&&e.BankAccountDetails?y().createElement(y().Fragment,null,y().createElement(mn.FormattedMessage,{id:"SELECT_BILLS_BANK_DESC",values:{BankName:e.BankAccountDetails.BankName,Code:e.BankAccountDetails.TransitCode,BankMaskedDigits:e.BankAccountDetails.AccountNumberMaskedDisplayView}})):y().createElement(y().Fragment,null,l.formatMessage({id:"ACCOUNT_BALANCE"}))},n=(null==s?void 0:s.filter(function(e){return e.IsOnPreauthorizedPayments}).length)===s.length,r=function(e){return e.map(function(e){return{accountNumber:e.Ban,subNumber:e.subscriberId,transactionID:d(e.Ban,A),payBalanceAmnt:0}})},y().useEffect(function(){if(s.length>1){f(M);var e=r(M);N(e),0===M.length&&(f([]),N([])),s.length===M.length?G(!0):G(!1)}},[M]),a={MyBill:l.formatMessage({id:"ACCOUNT_TYPENAME_MYBILL"}),Mobility:l.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY"}),OneBill:l.formatMessage({id:"ACCOUNT_TYPENAME_ONEBILL"}),TV:l.formatMessage({id:"ACCOUNT_TYPENAME_TV"}),Internet:l.formatMessage({id:"ACCOUNT_TYPENAME_INTERNET"}),HomePhone:l.formatMessage({id:"ACCOUNT_TYPENAME_HOMEPHONE"}),MobilityAndOneBill:l.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY_ONEBILL"}),SingleBan:l.formatMessage({id:"ACCOUNT_TYPENAME_SINGLEBAN"})},i=function(e){return!(!e.IsNM1Account&&e.AccountType===w.OneBill&&!e.IsOnPreauthorizedPayments||!(0!==e.Due&&void 0!==e.Due||e.IsOnPreauthorizedPayments))},y().useEffect(function(){var e=sessionStorage.getItem("itemsChecked"),t=e&&JSON.parse(e);b===j.SelectBills&&(null!==t&&t.length>0||C())},[b]),y().createElement("div",{className:["payment-border-b payment-border-gray-4",m?"":"payment-hidden"].join(" ").trim()},y().createElement("div",{className:["payment-flex payment-flex-col",b===j.SelectBills?"":"payment-hidden"].join(" ").trim()},y().createElement(pn.HeadingStep,{disableSrOnlyText:!0,status:"active",subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",title:s.filter(function(e){return!(null==e?void 0:e.IsOnPreauthorizedPayments)}).length>1?l.formatMessage({id:"SELECT_BILLS_HEADING"}):l.formatMessage({id:"SELECT_BILLS_HEADING_SINGULAR"}),id:b===j.SelectBills?"payment-setup-heading":void 0,"aria-hidden":b===j.SelectBills||void 0}),y().createElement("p",{className:"payment-text-gray payment-text-14 payment-mt-5","aria-hidden":"true",id:"payment-label"},l.formatMessage({id:"SELECT_BILLS_HEADING_DESC"}))),T&&y().createElement(y().Fragment,null,y().createElement("div",{className:"payment-mt-30"}),y().createElement(tr,null,y().createElement(Xn,null,y().createElement(Zn,{label:l.formatMessage({id:"ALERT_ERROR_SELECT_BILL_INFO"}),labelDescription:l.formatMessage({id:"ALERT_ERROR_SELECT_BILL_DESC"}),variant:"errorList",id:"error-alert-2"})))),y().createElement("div",{className:["payment-pt-0 checkbox-main",b===j.SelectBills?"":"payment-hidden"].join(" ").trim()},y().createElement("div",{className:"payment-mt-30"},y().createElement(pn.Checkbox,{id:"chxbx1",name:"checkboxname",value:"select all",variant:"default",checked:Y,onChange:function(e){e.target.checked?(G(!0),B&&B.length>0&&(L(B),V.current.forEach(function(e){e&&(e.checked=!0)}))):(G(!1),B.map(function(e,t){L(function(t){return t.filter(function(t){return t.BillName!==e.BillName})})}),B&&B.length>0&&V.current.forEach(function(e){e&&(e.checked=!1)}))}},y().createElement("label",null,l.formatMessage({id:"SELECT_ALL_BAN"})))),y().createElement("div",{className:"payment-mt-30 payment-flex payment-flex-col sm:payment-flex-row payment-flex-wrap payment-mb-15",role:"group","aria-labelledby":"chekcboxgroup-label"},B&&B.length>0&&B.sort(function(e,t){return e.NickName>t.NickName?1:-1}).map(function(e,n){var r;return y().createElement(Sr,{className:["checkboxitem payment-group/checkboxcard  payment-basis-0 sm:payment-basis-[47%] md:payment-basis-[35%] lg:payment-basis-1/3 payment-w-full sm:payment-w-auto payment-mr-15 payment-mb-15",e.IsOnPreauthorizedPayments?"payment-bg-gray-3":""].join(" ").trim(),id:"checkboxbill-".concat(n),idIndex:n,label:"checkboxBill".concat(n,"-label-").concat(n," checkboxBillBalance-").concat(n,"-label-").concat(n),isChecked:e.IsChecked,isDisabled:e.IsOnPreauthorizedPayments,billType:u(e.AccountType,e.IsNM1Account,a),billAccountNumber:null!==(r=e.NickName)&&void 0!==r?r:e.BillName,text:t(e),priceSettings:e.IsOnPreauthorizedPayments?void 0:{price:e.Due},ref:function(e){V.current[n]=e},item:e,isCheckedItems:M,setIsCheckedItems:L,isShowLabel:i(e),paymentItems:B,intl:l})})),T&&y().createElement(pn.Text,{elementType:"div",className:"payment-pb-15 payment-flex payment-items-center",id:"error-alert-3"},y().createElement(pn.Icon,{className:"payment-text-15 payment-text-red payment-mr-10",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"}),y().createElement(pn.Text,{elementType:"div",className:"payment-text-red payment-text-12"},l.formatMessage({id:"ALERT_ERROR_ONE_SELECT_BILL"}))),y().createElement("div",{className:"payment-pt-15 payment-pb-45 sm:payment-pb-60"},y().createElement(pn.Button,{variant:"primary",onClick:z,disabled:n},l.formatMessage({id:"CTA_NEXT"})))),y().createElement(Mr,{isActive:b>j.SelectBills,onIconLinkClick:function(e){p(j.SelectBills)},banDetails:x||[],isCheckedItems:M,isShow:m}))},Fr=function(e){return{createPayment:e.createPayment}},Hr=function(e){return{createMultiPaymentData:function(t,n,r,a){return e(fe({ban:t,type:n,details:r,sub:a}))},createOmnitureOnLoad:function(){return e(Fe({payload:"Banselected"}))}}},Yr=(0,_.connect)(Fr,Hr)((0,mn.injectIntl)(Ur)),jr=function(e){var t,n=e.intl,r=e.paymentItem,a=e.checkedBillItems,i=e.setCheckedCurrentBalanceItems,s=e.setCurrentSection,c=e.currentSection,d=e.language,m=e.accountInputValues,p=e.setAccountValues,b=(e.transactionIds,e.isBankPaymentSelected),f=e.setNotOptedBalanceItems,_=(e.checkedCurrentBalanceItems,e.createOmnitureOnCurrentBalance),g=o((0,E.useState)([]),2),v=g[0],N=g[1],A={MyBill:n.formatMessage({id:"ACCOUNT_TYPENAME_MYBILL"}),Mobility:n.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY"}),OneBill:n.formatMessage({id:"ACCOUNT_TYPENAME_ONEBILL"}),TV:n.formatMessage({id:"ACCOUNT_TYPENAME_TV"}),Internet:n.formatMessage({id:"ACCOUNT_TYPENAME_INTERNET"}),HomePhone:n.formatMessage({id:"ACCOUNT_TYPENAME_HOMEPHONE"}),MobilityAndOneBill:n.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY_ONEBILL"}),SingleBan:n.formatMessage({id:"ACCOUNT_TYPENAME_SINGLEBAN"})};return y().useEffect(function(){var e,t=l([],o(m),!1);v.length>0?(i(v),e=t.map(function(e){var t=v.find(function(t){return t.Ban===e.accountNumber&&t.subscriberId===e.subNumber});return e.payBalanceAmnt=t?t.Due:0,e}),p(e),f(a.filter(function(e){return e.Due>0&&e.AccountType!==w.OneBill&&!v.includes(e)}))):(i([]),p(t),f(a.filter(function(e){return e.Due>0&&e.AccountType!==w.OneBill})))},[v]),y().useEffect(function(){c===j.CurrentBalance&&_()},[c]),t=n.formatMessage({id:"PAY_CURRENT_BALANCE_DESC"}),y().createElement(y().Fragment,null,y().createElement("div",{className:[(j.CurrentBalance,"payment-border-b payment-border-gray-4"),c>j.CurrentBalance?"payment-hidden":""].join(" ").trim()},y().createElement("div",null,y().createElement(pn.HeadingStep,{disableSrOnlyText:!0,status:c===j.CurrentBalance?"active":"inactive",subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",title:(a.filter(function(e){return!(null==e?void 0:e.IsOnPreauthorizedPayments)}).length,n.formatMessage({id:"PAY_CURRENT_BALANCE_HEADING"})),id:c===j.CurrentBalance?"payment-setup-heading":"pre-auth-pay_curr_bal","aria-hidden":c===j.CurrentBalance?"true":void 0}),c===j.CurrentBalance&&y().createElement("div",{className:"payment-flex sm:payment-items-center"},y().createElement(pn.Icon,{className:"brui-text-blue payment-mt-10",iconClass:"bi_brui",iconName:"bi_info_notif_small"}),y().createElement("p",{className:"payment-text-gray payment-text-14 payment-leading-18 payment-mt-10 payment-ml-10",dangerouslySetInnerHTML:{__html:t}}))),y().createElement("div",{className:[c===j.CurrentBalance?"":"payment-hidden"].join(" ").trim()},y().createElement("div",{className:"payment-mt-30 payment-flex payment-flex-col sm:payment-flex-row payment-flex-wrap",role:"group","aria-labelledby":"chekcboxgroup-label"},a.map(function(e,t){var r;return e.Due>0&&e.AccountType!==w.OneBill?y().createElement(Lr,{className:"sm:payment-px-30 payment-mb-15",id:"checkboxBill-".concat(t),idIndex:t,label:"checkboxBalance-".concat(t,"-label-").concat(t," checkboxBalance-").concat(t,"-label-").concat(t,"-info"),isDisabled:e.IsOnPreauthorizedPayments,billType:u(e.AccountType,e.IsNM1Account,A),billAccountNumber:null!==(r=e.NickName)&&void 0!==r?r:e.BillName,text:n.formatMessage({id:"PAY_MY_BALANCE"}),priceSettings:e.IsOnPreauthorizedPayments?void 0:{price:e.Due},currentItem:e,isCheckedBalanceItems:v,setIsCheckedBalanceItems:N,intl:n}):null})),y().createElement("div",{className:"payment-text-gray payment-text-12 payment-mt-20"},y().createElement("p",null,n.formatMessage({id:"PAY_CURRENT_BALANCE_NOTE_1"})),y().createElement("p",null,n.formatMessage({id:"PAY_CURRENT_BALANCE_NOTE_2"}))),y().createElement("div",{className:"payment-pt-15 payment-pb-45 sm:payment-pb-60"},y().createElement(pn.Button,{variant:"primary",onClick:function(){s(j.TermsAndCondition)}},n.formatMessage({id:"CTA_NEXT"}))))),y().createElement(wr,{isActive:c>j.CurrentBalance,onIconLinkClick:function(e){s(j.CurrentBalance)},paymentItem:r,isCheckedBalanceItems:v,checkedBillItems:a,isBankPaymentSelected:b,currentSection:c,language:d}))},Gr=function(e){return{createOmnitureOnCurrentBalance:function(t){return e(Ye({data:t}))}}},Vr=(0,_.connect)(null,Gr)((0,mn.injectIntl)(jr)),zr=(0,mn.injectIntl)(function(e){var t,n,r=e.intl,a=e.multiban,i=e.submitMultiOrderPayment,o=e.accountInputValue,l=e.paymentItem,u=e.language,s=0;return a||(t=o.filter(function(e){var t;return e.transactionID===(null===(t=i[0])||void 0===t?void 0:t.OrderFormId)}).map(function(e){return e.accountNumber})[0],t&&(n=l.filter(function(e){return e.Ban===t}).map(function(e){return e.Due})[0])&&(s=n)),E.createElement(pn.Alert,{variant:"warning",className:"payment-border payment-relative sm:payment-py-30 sm:payment-px-30 payment-rounded-20 payment-p-16 payment-px-15 payment-py-30 sm:payment-flex payment-block",iconSize:"36"},E.createElement(pn.Text,{elementType:"div",className:"payment-pl-0 payment-pt-15 sm:payment-pl-16 sm:payment-pt-0"},a&&E.createElement(E.Fragment,null,E.createElement(pn.Heading,{level:"h2",variant:"xs",className:"payment-mb-15 payment-font-sans payment-font-bold"},r.formatMessage({id:"ALERT_CONFIRMATION_OPTED_SOME_BALANCE_MULTI"})),E.createElement("p",{className:"payment-text-14 payment-mb-10 payment-text-gray"},r.formatMessage({id:"ALERT_CONFIRMATION_OPTED_SOME_BALANCES_DESC_MULTI"})),E.createElement(pn.Text,{elementType:"div",className:"payment-mb-30 payment-mt-15"},i.map(function(e,t){var n=o.filter(function(t){return t.transactionID===e.OrderFormId&&0===t.payBalanceAmnt}).map(function(e){return e.accountNumber})[0],r=l.filter(function(e){return e.Ban===n}).map(function(e){return e.Due})[0];if(0!==r)return E.createElement(E.Fragment,null,E.createElement(pn.Text,{elementType:"div",className:"payment-flex payment-justify-between sm:payment-justify-normal"},E.createElement("label",{className:"payment-text-14 sm:payment-basis-1/4"},E.createElement("strong",null,n)),E.createElement(pn.Price,{language:u,showZeroDecimalPart:!0,price:r,variant:"defaultPrice",className:"!payment-text-14 payment-leading-14 payment-m-5 payment-font-normal"})))}))),!a&&E.createElement(E.Fragment,null,E.createElement(pn.Heading,{level:"h2",variant:"xs",className:"payment-mb-15 payment-font-sans payment-font-bold"},r.formatMessage({id:"ALERT_CONFIRMATION_OPTED_SOME_BALANCE_SINGLE"})),E.createElement("p",{className:"payment-text-14 payment-mb-10 payment-text-gray"},r.formatMessage({id:"ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_SINGLE_1"}),E.createElement(pn.Price,{language:u,price:Number(s),variant:"defaultPrice",className:"!payment-text-14 payment-leading-14 brui-m-5 payment-font-normal brui-inline-block"}),r.formatMessage({id:"ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_SINGLE_2"}))),E.createElement(pn.Button,{variant:"primary",size:"regular",onClick:function(){location.href="".concat(r.formatMessage({id:"CTA_MAKE_PAYMENT_LINK"}))}},r.formatMessage({id:"ALERT_CONFIRMATION_INFO_BUTTON_DESC"}))))}),qr=function(e){var t=e.intl;return E.createElement(pn.Alert,{variant:"info",className:"payment-border payment-relative sm:payment-py-30 sm:payment-px-30 payment-rounded-20 payment-p-16 payment-px-15 payment-py-30 sm:payment-flex payment-block",iconSize:"36",id:"alert-3"},E.createElement(pn.Text,{elementType:"div",className:"payment-pl-0 payment-pt-15 sm:payment-pl-16 sm:payment-pt-0"},E.createElement(pn.Heading,{level:"h2",variant:"xs",className:"payment-mb-12 payment-font-sans payment-font-bold payment-leading-22"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_HEADING"})),E.createElement("p",{className:"payment-text-14 payment-my-15 payment-text-gray"},t.formatMessage({id:"ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_MULTI"})),E.createElement(pn.Text,{elementType:"div",className:"sm:payment-flex payment-block"},E.createElement(pn.Text,{elementType:"div",className:"payment-pr-0 sm:payment-pr-10"},E.createElement(pn.Button,{variant:"primary",size:"regular",onClick:function(){location.href="".concat(t.formatMessage({id:"CTA_MAKE_PAYMENT_LINK"}))}},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_BUTTON_DESC"}))))))},Kr=(0,mn.injectIntl)(qr),Wr=function(e,t,n,r,a,i,o){if(e){if(!t)return{s_oPYM:"Bank payment",s_OPID:i[0].PaymentConfirmationNumber};if(n.length>0)return{s_oPYM:"Bank payment:optin",s_OPID:i[0].PaymentConfirmationNumber};if(0===n.length)return{s_oPYM:"Bank payment:optout",s_OPID:i[0].PaymentConfirmationNumber}}else if(!e){if(!t)return{s_oPYM:"Credit card",s_oCCDT:{CreditCardType:o},s_OPID:i[0].PaymentConfirmationNumber};if(n.length>0)return{s_oPYM:"Credit card:optin",s_oCCDT:{CreditCardType:o},s_OPID:i[0].PaymentConfirmationNumber};if(0===n.length)return{s_oPYM:"Credit card:optout",s_oCCDT:{CreditCardType:o},s_OPID:i[0].PaymentConfirmationNumber}}return{s_oPYM:"",s_oCCDT:""}},Xr=function(e,t,n,r){if(e){if(!t)return{s_oPYM:"Bank payment",s_OPID:""};if(n.length>0)return{s_oPYM:"Bank payment:optin",s_OPID:""};if(0===n.length)return{s_oPYM:"Bank payment:optout",s_OPID:""}}else if(!e){if(!t)return{s_oPYM:"Credit card",s_oCCDT:{CreditCardType:r},s_OPID:""};if(n.length>0)return{s_oPYM:"Credit card:optin",s_oCCDT:{CreditCardType:r},s_OPID:""};if(0===n.length)return{s_oPYM:"Credit card:optout",s_oCCDT:{CreditCardType:r},s_OPID:""}}return{s_oPYM:"",s_oCCDT:"",s_OPID:""}},Qr=function(e){var t,n,r,a,i,l,s,c,d,m,p,b=e.intl,f=e.paymentItem,y=e.checkedBillItems,_=e.checkedCurrentBalanceItems,g=e.showPaymentSummary,v=e.isPreauth,N=e.isNewbank,A=e.inputValue,C=e.inputBankValue,h=e.isShow,T=e.isBankPaymentSelected,I=e.submitMultiOrderPayment,R=e.submitMultiOrderFormStatus,x=e.accountInputValues,S=e.BankList,O=e.showCurrentBalance,M=e.currentSection,L=e.language,D=e.notOptedBalanceItems,B=e.setApiSatusIsFailed,P=e.creditCardAutopayOffers,k=e.debitCardAutopayOffers,U=e.bankitems,F=e.setOmnitureOnConfirmation,H=e.apiSatusIsFailed,Y=e.backCTAURL,G=e.setOmnitureOnConfirmationFailure,V=e.setOmnitureOnOneTimePaymentFailure,z={MyBill:b.formatMessage({id:"ACCOUNT_TYPENAME_MYBILL"}),Mobility:b.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY"}),OneBill:b.formatMessage({id:"ACCOUNT_TYPENAME_ONEBILL"}),TV:b.formatMessage({id:"ACCOUNT_TYPENAME_TV"}),Internet:b.formatMessage({id:"ACCOUNT_TYPENAME_INTERNET"}),HomePhone:b.formatMessage({id:"ACCOUNT_TYPENAME_HOMEPHONE"}),MobilityAndOneBill:b.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY_ONEBILL"}),SingleBan:b.formatMessage({id:"ACCOUNT_TYPENAME_SINGLEBAN"})},q={label:b.formatMessage({id:"PAYMENT_METHOD"}),debits:T?f&&f.length>1?(p=[],k&&(null==k||k.map(function(e){y&&y.map(function(t){e.Ban===t.Ban&&p.push(e)})})),p):k:null,credits:T?null:f&&f.length>1?function(){var e=[];return P&&(null==P||P.map(function(t){y&&y.map(function(n){t.Ban===n.Ban&&e.push(t)})})),e}():P},K={label:b.formatMessage({id:"AUTOPAY_TRANSACTION_NOTE_DESC"})},W={label:b.formatMessage({id:"AUTOPAY_TRANSACTION_NOTE_HEADING"})},X=q&&q.credits&&q.credits.length>0&&q.credits[0].AutopayEligibleSubscribers&&q.credits[0].AutopayEligibleSubscribers.length>0&&q.credits[0].AutopayEligibleSubscribers[0].autopayOffers&&q.credits[0].AutopayEligibleSubscribers[0].autopayOffers.length>0||q&&q.debits&&q.debits.length>0&&q.debits[0].AutopayEligibleSubscribers&&q.debits[0].AutopayEligibleSubscribers.length>0&&q.debits[0].AutopayEligibleSubscribers[0].autopayOffers&&q.debits[0].AutopayEligibleSubscribers[0].autopayOffers.length>0,Q=y.map(function(e){return{label:E.createElement(mn.FormattedMessage,{id:"SELECT_BILLS_ACCOUNT_TITLE",values:{accounttype:u(e.AccountType,e.IsNM1Account,z)}}),value:e.NickName,key:u(e.AccountType,e.IsNM1Account,z)}}),$=y.filter(function(e){return!_.includes(e)&&e.Due>0}),Z=E.useRef(null),J=T?b.formatMessage({id:"PAY_CURRENT_BALANCE_OPTED_IN"},{balance:new Intl.NumberFormat(L,{style:"currency",currency:"USD",currencyDisplay:"narrowSymbol"}).format(0)}):M===j.Confirmation?"":b.formatMessage({id:"PAY_CURRENT_BALANCE_OPTED_IN_PACC"}),ee=b.formatMessage({id:"PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_OUT_SINGULAR"}),te=b.formatMessage({id:"PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_OUT_PLURAL"}),ne=o(E.useState(!1),2),re=ne[0],ae=ne[1],ie=function(){M===j.Confirmation&&G(T?Xr(!0,O,_):Xr(!1,O,_,A.cardType))};return E.useEffect(function(){var e,t,n,r,i;if(R===$e.FAILED)B(!0),ie();else if(1===f.length&&R===$e.COMPLETED&&Object.values(I).length>0)try{for(r=(n=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}(Object.values(I))).next();!r.done;r=n.next())if(""!==r.value.ErrorCodeID.trim()){B(!0),ie();break}}catch(a){e={error:a}}finally{try{r&&!r.done&&(t=n.return)&&t.call(n)}finally{if(e)throw e.error}}R===$e.COMPLETED&&Object.values(I).length>0&&h&&!H&&F(T?Wr(T,O,_,0,0,Object.values(I)):Wr(T,O,_,0,0,Object.values(I),A.cardType)),R===$e.COMPLETED&&Z.current&&((i=Z.current.querySelector(".payment-focus-sr"))&&(i.scrollIntoView({behavior:"smooth"}),i.focus()),setTimeout(function(){var e,t=null===(e=null==Z?void 0:Z.current)||void 0===e?void 0:e.querySelector(".payment-focus-heading");t&&(t.scrollIntoView({behavior:"smooth"}),t.focus())},100),setTimeout(function(){var e,t=null===(e=null==Z?void 0:Z.current)||void 0===e?void 0:e.querySelector(".payment-focus-sr");t&&(t.style.display="none")},1e3),setTimeout(function(){ae(!0)},200))},[R]),r=function(){return Object.values(I).some(function(e){return(null==e?void 0:e.otp)&&!(null==e?void 0:e.otp.isSuccess)})?b.formatMessage({id:"TRANSACTION_SUBMITTED_HEADING"}):b.formatMessage({id:"CONFIRMATION_HEADING"})},a=function(){return Object.values(I).some(function(e){return(null==e?void 0:e.otp)&&!(null==e?void 0:e.otp.isSuccess)})?b.formatMessage({id:"UNPAID_BALANCE"}):b.formatMessage({id:"PAYMENT_AMOUNT"})},i=o(E.useState(!1),2),l=i[0],s=i[1],c=o(E.useState([]),2),d=c[0],m=c[1],E.useEffect(function(){var e,t,n,r;0===Object.values(d).length&&(e=Object.values(I).filter(function(e){return(null==e?void 0:e.otp)&&!(null==e?void 0:e.otp.isSuccess)}),t=e.map(function(e){return null==e?void 0:e.OrderFormId}),n=x.filter(function(e){return t.includes(e.transactionID)}).map(function(e){return e.accountNumber}),r=_.filter(function(e){return n.includes(e.Ban)}),_.length>r.length&&1!==_.length?s(!0):s(!1),m(r))},[d]),E.createElement(E.Fragment,null,R===$e.PENDING&&E.createElement(hr,{variant:"submitOrder"}),Object.values(I).length>0&&R===$e.COMPLETED&&E.createElement("div",{className:"brui-border-gray-4",ref:Z},E.createElement("div",{className:"payment-mb-15 brui-flex brui-items-center brui-justify-between payment-mt-30 sm:payment-mt-45",id:"ConfirmationDivID"},E.createElement("span",{className:"payment-focus-sr payment-sr-only",id:"pageConfirmationId",tabIndex:-1},b.formatMessage({id:"PAGE_TITLE_CONFIRMATON"})),E.createElement(pn.Heading,{className:"payment-focus-heading",level:"h2",variant:"lg",tabIndex:-1,id:h?"payment-setup-heading":void 0,"aria-hidden":h?"true":void 0},r())),!T&&Object.values(I).length>0&&Object.values(I).some(function(e){return(null==e?void 0:e.otp)&&!(null==e?void 0:e.otp.isSuccess)})&&re&&E.createElement("div",{id:"OtpFailure",className:"payment-pt-15 payment-pb-0"},E.createElement(dr,{checkedCurrentBalanceItems:_,submitMultiOrderPayment:Object.values(I),accountInputValue:x,language:L,notOptedBalanceItems:D,setOmnitureOnOneTimePaymentFailure:V})),E.createElement("div",{id:"ConfirmationSuccess",className:"payment-pt-15 payment-pb-15"},E.createElement(rr,{submitMultiOrderPayment:Object.values(I),accountInputValue:x,isBankPayment:T,checkedBillItems:y,language:L,paymentItem:f,creditCardAutopayOffers:P,debitCardAutopayOffers:k})),f.find(function(e){return e.AccountType!==w.OneBill})&&$.length>0&&!1===Object.values(I).some(function(e){return(null==e?void 0:e.otp)&&!(null==e?void 0:e.otp.isSuccess)})&&E.createElement("div",{id:"AvoidLatePayment",className:"payment-pb-15"},E.createElement(zr,{multiban:f.length>1,submitMultiOrderPayment:Object.values(I),accountInputValue:x,paymentItem:f,language:L})),f.find(function(e){return e.AccountType===w.OneBill})&&E.createElement("div",{id:"AvoidLatePayment",className:"payment-pb-15"},E.createElement(Kr,null)),E.createElement("div",{className:"payment-block payment-border payment-rounded-20 payment-relative payment-px-15 sm:payment-px-30 payment-py-30 sm:payment-pb-45"},E.createElement("div",null,E.createElement(pn.Heading,{level:"h3",variant:"md"},b.formatMessage({id:"PAYMENT_SUMMARY_TITLE"}))),E.createElement("div",{className:"payment-mt-30"},E.createElement(Br,{title:b.formatMessage({id:"BILL_INFORMATION_TITLE"})},Q.map(function(e,t){return E.createElement(qn,{className:t>0?"payment-mt-5":"",label:e.label,value:e.value})}))),E.createElement("div",{className:"payment-mt-45"},E.createElement(Br,{title:b.formatMessage({id:"PAYMENT_INFORMATION_TITLE"})},E.createElement(pr,{paymentItem:f,className:g?"":"payment-hidden",inputValue:A,isNewbank:N,isPreauth:v,inputBankValue:C,showHeading:!1,isBankPaymentSelected:T,bankList:S,creditCardAutopayOffers:P,debitCardAutopayOffers:k,checkedBillItems:y,bankitems:U,isConfirmation:!0}))),X?T?E.createElement("div",{className:"payment-mt-45"},E.createElement(Br,{title:b.formatMessage({id:"AUTOPAY_CREDITS_TITLE"}),role:"list"},null===(t=null==q?void 0:q.debits)||void 0===t?void 0:t.map(function(e,t){var n;return E.createElement(E.Fragment,{key:e.Ban||t},y&&y.length>1&&E.createElement("p",{className:"payment-text-14 payment-font-bold"},e.banInfo&&e.banInfo.nickName),null===(n=e.AutopayEligibleSubscribers)||void 0===n?void 0:n.map(function(e,t){var n;return null===(n=null==e?void 0:e.autopayOffers)||void 0===n?void 0:n.map(function(t,n){return E.createElement(qn,{key:"".concat(e.subscriberTelephoneNumber,"-").concat(n),label:e.subscriberTelephoneNumber,value:"en"===L?"$"+t.discountAmount.toFixed(2)+"/mo.":t.discountAmount.toFixed(2)+" $/mois",needSRText:!0,srText:"en"===L?t.discountAmount.toFixed(2)+" dollars per month":t.discountAmount.toFixed(2)+" dollars par mois",role:"listitem",isMultiBan:y.length>1,className:y.length>1?"payment-text-gray":"payment-text-black"})})}))}),E.createElement("p",{className:"payment-text-gray payment-text-14 payment-mt-15"},E.createElement("strong",null,W.label),K.label))):E.createElement("div",{className:"payment-mt-45"},E.createElement(Br,{title:b.formatMessage({id:"AUTOPAY_CREDITS_TITLE"}),role:"list"},null===(n=null==q?void 0:q.credits)||void 0===n?void 0:n.map(function(e,t){var n;return E.createElement(E.Fragment,{key:e.Ban||t},y&&y.length>1&&E.createElement("p",{className:"payment-text-14 payment-font-bold"},e.banInfo&&e.banInfo.nickName),null===(n=e.AutopayEligibleSubscribers)||void 0===n?void 0:n.map(function(e,t){var n;return null===(n=null==e?void 0:e.autopayOffers)||void 0===n?void 0:n.map(function(t,n){return E.createElement(qn,{key:"".concat(e.subscriberTelephoneNumber,"-").concat(n),label:e.subscriberTelephoneNumber,value:"en"===L?"$"+t.discountAmount.toFixed(2)+"/mo.":t.discountAmount.toFixed(2)+" $/mois",needSRText:!0,srText:"en"===L?t.discountAmount.toFixed(2)+" dollars per month":t.discountAmount.toFixed(2)+" dollars par mois",role:"listitem",isMultiBan:y.length>1,className:y.length>1?"payment-text-gray":"payment-text-black"})})}))}),E.createElement("p",{className:"payment-text-gray payment-text-14 payment-mt-15"},E.createElement("strong",null,W.label),K.label))):"",O&&E.createElement("div",{className:"payment-mt-45"},E.createElement(Br,{title:b.formatMessage({id:"CURRENT_BALANCE_TITLE"})},!T&&Object.values(I).length>0&&Object.values(I).some(function(e){return(null==e?void 0:e.otp)&&!(null==e?void 0:e.otp.isSuccess)})&&!l&&E.createElement("div",{className:"payment-flex payment-items-start payment-mb-15"},E.createElement(pn.Icon,{className:"brui-text-15 brui-text-red brui-mr-10 payment-mt-2",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"}),E.createElement("p",{className:"brui-text-red brui-text-14"},b.formatMessage({id:"SUMMARY_CURRENT_BAL_OTP_FAILURE"}))),E.createElement("div",{className:"payment-text-gray payment-text-14 payment-mt-5"},E.createElement("p",{className:"payment-leading-18",dangerouslySetInnerHTML:{__html:0===_.length?y.length-_.length>1?te:ee:_.every(function(e){return y.includes(e)})||_.some(function(e){return y.includes(e)})?J:void 0}})),E.createElement("div",null,f.length>1?E.createElement(E.Fragment,null,_.map(function(e){return E.createElement(Dr,{accountinfo:e.NickName,role:"list",childrole:"listitem",className:"first:payment-mt-15 payment-mb-15 last:payment-mb-0",isLabelOnError:!!(l&&d.filter(function(t){return t.Ban===e.Ban}).length>0)},E.createElement(E.Fragment,null,l&&d.filter(function(t){return t.Ban===e.Ban}).length>0&&E.createElement("div",{className:"payment-flex payment-items-start payment-mb-5"},E.createElement(pn.Icon,{className:"brui-text-15 brui-text-red brui-mr-10 payment-mt-2",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"}),E.createElement("p",{className:"brui-text-red brui-text-14"},b.formatMessage({id:"SUMMARY_CURRENT_BAL_OTP_FAILURE"}))),E.createElement(qn,{label:d.filter(function(t){return t.Ban===e.Ban}).length>0?b.formatMessage({id:"UNPAID_BALANCE"}):b.formatMessage({id:"PAYMENT_AMOUNT"}),value:E.createElement(pn.Price,{language:L,price:e.Due,variant:"ordinaryPrice",className:"!brui-text-14 brui-leading-18"}),needSRText:!0,srText:"".concat(e.Due," dollars"),className:"payment-text-black",isMultiBan:!0})))})):E.createElement(E.Fragment,null,_.map(function(e){return E.createElement(qn,{label:a(),value:E.createElement(pn.Price,{language:L,price:e.Due,variant:"ordinaryPrice",className:"!brui-text-14 brui-leading-18"}),needSRText:!0,srText:"".concat(e.Due," dollars"),className:"payment-text-black first:payment-mt-15 payment-mb-15 last:payment-mb-0"})})))))),E.createElement("div",{className:"payment-mt-30 payment-mb-45 sm:payment-mb-60"},E.createElement(pn.Button,{size:"regular",variant:"secondary",onClick:function(){window.location.href=Y+"#billinginformation"}},b.formatMessage({id:"BACK_TO_MY_BELL"})))))},$r=function(e){return{submitMultiOrderPayment:e.submitMultiOrderPayment,submitMultiOrderFormStatus:e.submitMultiOrderFormStatus}},Zr=function(e){return{setOmnitureOnConfirmation:function(t){return e(Ge({data:t}))},setOmnitureOnConfirmationFailure:function(t){return e(Ke({data:t}))},setOmnitureOnOneTimePaymentFailure:function(t){return e(We({data:t}))}}},Jr=(0,_.connect)($r,Zr)((0,mn.injectIntl)(Qr)),ea={error:"payment-text-red payment-text-[60px] payment-box-border payment-static payment-text-center",warning:"payment-text-yellow payment-text-[60px] payment-box-border payment-static payment-text-center"},ta=function(e){var t=e.iconVariant,n=e.errorHeaderText,r=e.errorHeaderTextSR,a=e.errorHeaderStyle,i=e.errorTextStyle,o=e.errorText,l=e.buttonText,u=e.onButtonClick,s=e.suggestionList;return y().createElement("div",{className:"payment-text-center payment-mt-60 sm:payment-max-w-[543px] md:payment-max-w-[548px] payment-mx-auto"},y().createElement("span",{"aria-label":"Warning"},y().createElement("span",{role:"img","aria-hidden":"true"},y().createElement(pn.Icon,{iconClass:"bi_brui",iconName:"bi_exclamation_c",className:ea[t]}))),y().createElement(pn.Heading,{level:"h2",variant:"default",className:["payment-text-32 payment-leading-38 payment-text-black payment-mb-5 payment-mt-30",a].join(" ").trim()},y().createElement("span",{"aria-hidden":"true"},n),y().createElement("span",{"aria-hidden":"false",className:"payment-sr-only"},r)),y().createElement("p",{className:["payment-text-gray payment-overflow-x-hidden payment-font-sans payment-text-[16px] payment-leading-[25px]",i].join(" ").trim()},o),s&&y().createElement(y().Fragment,null,y().createElement(pn.Divider,{direction:"horizontal",width:1,className:"payment-my-45 payment-bg-gray-4"}),y().createElement("div",{className:"payment-mb-45"},y().createElement(pn.Text,{className:"brui-text-14 payment-text-black payment-font-bold"},s.label),y().createElement("div",{className:"payment-mt-15 payment-text-left sm:payment-text-center"},s.items.map(function(e){return y().createElement("div",{className:"payment-flex sm:payment-justify-center payment-mb-5 last:payment-mb-0 payment-gap-5"},y().createElement("span",{className:"payment-mr-3",role:"img","aria-hidden":"true"},y().createElement(pn.Icon,{iconClass:"bi_brui",iconName:"bi_check_small_flat_fin",className:"payment-text-24 payment-text-blue"})),y().createElement(pn.Text,{className:"payment-leading-18 brui-text-14 payment-mt-3 payment-text-gray"},e))})))),y().createElement("div",{className:"payment-rounded-lg payment-m-20 payment-[focus-within:ring-4]"},y().createElement(pn.Button,{variant:"primary",onClick:u},l)))},na=ta,ra=function(e){var t=e.intl;return E.createElement(na,{iconVariant:"error",errorHeaderText:t.formatMessage({id:"FAILURE_API_BAN_HEADING"}),errorHeaderTextSR:t.formatMessage({id:"FAILURE_API_BAN_HEADING_SR"}),errorText:E.createElement(E.Fragment,null,E.createElement("p",null,t.formatMessage({id:"FAILURE_API_BAN_MAIN_DESC"}),"  "),E.createElement("p",{className:"payment-mt-8"},t.formatMessage({id:"FAILURE_API_BAN_MAIN_DESC_2"}),"  ")),buttonText:t.formatMessage({id:"FAILURE_API_BAN_BUTTON"}),suggestionList:{label:t.formatMessage({id:"FAILURE_API_BAN_SUB_DESC"}),items:[t.formatMessage({id:"FAILURE_API_BAN_SUB_DESC_LISTITEM_1"}),t.formatMessage({id:"FAILURE_API_BAN_SUB_DESC_LISTITEM_2"})]},onButtonClick:function(){window.location.reload()}})},aa=(0,mn.injectIntl)(ra),ia=function(e){function t(t){var n,r,a,i,o,l,u,s,c,m,p,b=e.call(this,t)||this;return b.setCreditCardInputValue=function(e){b.setState({creditCardInputValue:e})},b.setBankInputValue=function(e){b.setState({BankInputValue:e})},b.setCurrentSection=function(e){b.setState({currentSection:e})},b.setCheckedBillItems=function(e){b.setState({checkedBillItems:e})},b.setApiSatusIsFailed=function(e){b.setState({apiSatusIsFailed:e})},b.getQueryParameter=function(e){return new URLSearchParams(document.location.search.substring(1)).get(e)},b.setCheckedCurrentBalanceItems=function(e){b.setState({checkedCurrentBalanceItems:e})},b.setAccountValues=function(e){b.setState({accountInputValues:e})},b.setNotOptedBalanceItems=function(e){b.setState({notOptedBalanceItems:e})},b.parseDOMString=function(e){var t;return null===(t=(new DOMParser).parseFromString(e,"text/html").documentElement)||void 0===t?void 0:t.textContent},p=(null===(r=null===(n=t.Config)||void 0===n?void 0:n.getPaymentItem)||void 0===r?void 0:r.length)>1,b.state={localizationLoaded:!0,paymentHeadingStepState:p?"inactive":"active",currentSteps:":",currentSection:p?j.SelectBills:j.PaymentMethod,creditCardInputValue:W,BankInputValue:K,isBankSelected:!1,checkedBillItems:!1===p?null===(a=t.Config)||void 0===a?void 0:a.getPaymentItem:[],accountInputValues:!1===p?[{accountNumber:null===(o=null===(i=t.Config)||void 0===i?void 0:i.getPaymentItem[0])||void 0===o?void 0:o.Ban,subNumber:null===(u=null===(l=t.Config)||void 0===l?void 0:l.getPaymentItem[0])||void 0===u?void 0:u.subscriberId,transactionID:d(null===(c=null===(s=t.Config)||void 0===s?void 0:s.getPaymentItem[0])||void 0===c?void 0:c.Ban,null===(m=t.Config)||void 0===m?void 0:m.transactionIdArray),payBalanceAmnt:0}]:[],checkedCurrentBalanceItems:[],notOptedBalanceItems:[],apiSatusIsFailed:!1,InteracCode:""},b.onCurrentSteps=b.onCurrentSteps.bind(b),b.setIsHeadingStepActive=b.setIsHeadingStepActive.bind(b),b.setCreditCardInputValue=b.setCreditCardInputValue.bind(b),b.setBankInputValue=b.setBankInputValue.bind(b),b.setCurrentSection=b.setCurrentSection.bind(b),b.setCheckedBillItems=b.setCheckedBillItems.bind(b),b.setAccountValues=b.setAccountValues.bind(b),b.setCheckedCurrentBalanceItems=b.setCheckedCurrentBalanceItems.bind(b),b.setIsBankSelected=b.setIsBankSelected.bind(b),b.setNotOptedBalanceItems=b.setNotOptedBalanceItems.bind(b),b.setApiSatusIsFailed=b.setApiSatusIsFailed.bind(b),b.setInteracCode=b.setInteracCode.bind(b),b}return n(t,e),t.prototype.setInteracCode=function(e){this.setState({InteracCode:e})},t.prototype.onCurrentSteps=function(e){this.state.currentSteps!==e&&this.setState({currentSteps:e})},t.prototype.setIsHeadingStepActive=function(e){this.setState({paymentHeadingStepState:e})},t.prototype.setIsBankSelected=function(e){this.setState({isBankSelected:e})},t.prototype.removeSessionStorageCheckedItems=function(){var e=sessionStorage.getItem("itemsChecked");e&&e.length>0&&sessionStorage.removeItem("itemsChecked")},t.prototype.componentDidMount=function(){var e,t,n,r,a=this.getQueryParameter("code");a&&null!=a?(r=(null===(e=this.props.Config)||void 0===e?void 0:e.currentUrl)||"",window.history.pushState({path:r},"",r),this.setInteracCode(a),this.props.getInteracBankInfoAction(a),1===(null===(n=null===(t=this.props.Config)||void 0===t?void 0:t.getPaymentItem)||void 0===n?void 0:n.length)&&this.removeSessionStorageCheckedItems()):this.removeSessionStorageCheckedItems(),this.props.redirectUrlAction({})},t.prototype.render=function(){var e,t,n,r,a,i,o,l=this.props,u=l.localization,s=l.Config,c=(null===(e=null==s?void 0:s.getPaymentItem)||void 0===e?void 0:e.length)>1,d="ON"===(null==s?void 0:s.IsSingleClickEnabled),m="ON"===(null==s?void 0:s.IsAutopayCreditEnabled),p=null!==(n=null===(t=null==s?void 0:s.getPaymentItem)||void 0===t?void 0:t.some(function(e){return e.Due>0&&e.isOneTimePaymentEligible&&e.AccountType!==w.OneBill&&!e.isLastBillOnPreauth}))&&void 0!==n&&n,b=null!==(a=null===(r=null==s?void 0:s.getPaymentItem)||void 0===r?void 0:r.some(function(e){return e.Due>0&&e.isOneTimeCreditCardPaymentEnabled&&e.AccountType!==w.OneBill&&!e.isLastBillOnPreauth}))&&void 0!==a&&a,f=null===(i=null==s?void 0:s.getPaymentItem)||void 0===i?void 0:i.some(function(e){var t;return null!==(t=e.IsOnPreauthorizedPayments)&&void 0!==t&&t}),E=this.state,_=E.currentSection,g=E.creditCardInputValue,v=E.BankInputValue,N=E.isBankSelected,A=E.checkedBillItems,C=E.accountInputValues,h=E.checkedCurrentBalanceItems,T=E.notOptedBalanceItems,I=E.apiSatusIsFailed,R=E.InteracCode,x=null!==(o=null==A?void 0:A.some(function(e){return e.Due>0&&e.AccountType!==w.OneBill&&!e.isLastBillOnPreauth}))&&void 0!==o&&o;return p=p&&x&&d,b=b&&x&&d,y().createElement(mn.IntlProvider,{locale:u.locale,messages:u.messages},this.props.isLoading?y().createElement(hr,null):y().createElement(pn.Container,null,!I&&y().createElement(y().Fragment,null,_!==j.Confirmation&&y().createElement(y().Fragment,null,y().createElement(Yr,{paymentItem:s.getPaymentItem,isShow:c,onCurrentSteps:this.onCurrentSteps,setCurrentSection:this.setCurrentSection,currentSection:_,setCheckedBillItems:this.setCheckedBillItems,paymentItems:s.getPaymentItem,setAccountValues:this.setAccountValues,accountInputValues:C,transactionIds:s.transactionIdArray}),y().createElement(vr,{paymentItem:s.getPaymentItem,isHeadingStepActive:_===j.PaymentMethod?"active":"inactive",isSingleClickEnableForPACC:p,isSingleClickEnableForPAD:b,onCurrentSteps:this.onCurrentSteps,setHeadingSteps:this.setIsHeadingStepActive,setInputValue:this.setCreditCardInputValue,inputValue:g,setInputBankValue:this.setBankInputValue,inputBankValue:v,setIsBankSelected:this.setIsBankSelected,setCurrentSection:this.setCurrentSection,currentSection:_,checkedBillItems:A,bankList:s.getBankList,accountInputValues:C,creditCardAutopayOffers:s.creditCardAutopayOffers,debitCardAutopayOffers:s.debitCardAutopayOffers,language:s.language,isInteractEnabled:s.IsInteracEnabled,IsAutopayCreditEnabled:m,InteracCode:R}),(p||b)&&y().createElement(Vr,{paymentItem:s.getPaymentItem,checkedBillItems:A,checkedCurrentBalanceItems:h,setCheckedCurrentBalanceItems:this.setCheckedCurrentBalanceItems,isShow:_===j.CurrentBalance,onCurrentSteps:this.onCurrentSteps,setCurrentSection:this.setCurrentSection,currentSection:_,language:s.language,setAccountValues:this.setAccountValues,accountInputValues:C,transactionIds:s.transactionIdArray,isBankPaymentSelected:N,setNotOptedBalanceItems:this.setNotOptedBalanceItems}),y().createElement(xr,{isActive:_===j.TermsAndCondition,onCurrentSteps:this.onCurrentSteps,setCurrentSection:this.setCurrentSection,currentSection:_,checkedBillItems:A,paymentItem:s.getPaymentItem,province:s.province,language:s.language,userProfileProv:s.userProfileProvince,accountInputValues:C,isBankSelected:N,setApiSatusIsFailed:this.setApiSatusIsFailed,creditCardAutopayOffers:s.creditCardAutopayOffers,debitCardAutopayOffers:s.debitCardAutopayOffers})),_===j.Confirmation&&y().createElement(Jr,{paymentItem:s.getPaymentItem,checkedBillItems:A,checkedCurrentBalanceItems:h,showPaymentSummary:!0,isNewbank:!1,isPreauth:f,inputValue:g,isShow:_===j.Confirmation,inputBankValue:v,isBankPaymentSelected:N,BankList:s.getBankList,showCurrentBalance:p||b,language:s.language,accountInputValues:C,currentSection:_,notOptedBalanceItems:T,setApiSatusIsFailed:this.setApiSatusIsFailed,creditCardAutopayOffers:s.creditCardAutopayOffers,debitCardAutopayOffers:s.debitCardAutopayOffers,bankitems:[],apiSatusIsFailed:I,backCTAURL:s.flowInitUrl})),I&&y().createElement(aa,null)))},t.prototype.componentDidUpdate=function(e,t){var n,r=this.props.Config.pagetitle||"",a=r.replace(/Step/g,"");t.currentSteps!==this.state.currentSteps&&(r="".concat(r," ").concat(this.state.currentSteps),document.title=this.parseDOMString(r)||""),this.state.currentSection===j.Confirmation&&(n="".concat(G.Confirmation," ").concat(a),document.title=this.parseDOMString(n)||"")},t.displayName="App",t}(y().Component),oa=function(e,t){return{localization:e.localization,Config:t.Config,isLoading:e.isLoading}},la=function(e){return{redirectUrlAction:function(){e(Me({}))},getInteracBankInfoAction:function(t){e(Be({code:t}))}}},ua=(0,_.connect)(oa,la)(ia),sa=function(e){function t(t,n,r){var a=e.call(this)||this;return a.store=t,a.config=n,a}return n(t,e),t.prototype.init=function(){var e,t;this.config.setConfig(g.LoggerConfigKeys.SeverityLevel,this.config.logLevel),this.store.dispatch(Te({ban:this.config.getPaymentItem[0].Ban,sub:this.config.getPaymentItem[0].subscriberId})),null!=this.config.getPaymentItem&&1===this.config.getPaymentItem.length&&(t=[{accountNumber:(e=this.config.getPaymentItem[0]).Ban,subNumber:e.subscriberId,transactionID:d(e.Ban,this.config.transactionIdArray),payBalanceAmnt:0}],this.store.dispatch(fe({ban:e.Ban,type:e.AccountType===w.OneBill,details:t,sub:e.subscriberId})))},t.prototype.destroy=function(){this.store.destroy()},t.prototype.render=function(e){var t=this.store,n=this.config;e.render(E.createElement(_.Provider,{store:t},E.createElement(ua,{Config:n})))},a([(0,g.Widget)({namespace:"Preauth/Setup"}),i("design:paramtypes",[dn,R,g.Logger])],t)}(g.ViewWidget),ca=sa}(),t}())},function(e){"use strict";e.exports=t},function(e){"use strict";e.exports=n},function(e){"use strict";e.exports=r},function(e){"use strict";e.exports=a},function(e){"use strict";e.exports=i},function(e){"use strict";e.exports=o},function(e){"use strict";e.exports=l},function(e){"use strict";e.exports=u},function(e,t,n){"use strict";var r=n(10);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot}],m={};return s.d=function(e,t){for(var n in t)s.o(t,n)&&!s.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},s.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},s.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},c={},function(){"use strict";function e(e,t,r){var l,u=n(n({},e),{"loader.staticWidgetMappings":{"bell-preauth-setup":{factory:function(){return s(2)},namespace:"Preauth/Setup"}}});if((0,o.Init)(u),l=document.getElementById(t))try{l.innerHTML="",(0,i.createRoot)(l).render(a.createElement(a.StrictMode,null,a.createElement(o.WidgetLoader,{widget:"bell-preauth-setup"})))}catch(c){l.innerHTML='<div style="padding: 20px; color: #d32f2f; border: 1px solid #d32f2f; border-radius: 4px; background-color: #ffeaea;">Failed to load payment widget. Please refresh the page.</div>'}}var t,n,r,a,i,o;s.r(c),s.d(c,{initialize:function(){return e}}),t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},t(e,n)},n=function(){return n=Object.assign||function(e){var t,n,r,a;for(n=1,r=arguments.length;n<r;n++)for(a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},n.apply(this,arguments)},Object.create,Object.create,r=function(e){return r=Object.getOwnPropertyNames||function(e){var t,n=[];for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[n.length]=t);return n},r(e)},"function"==typeof SuppressedError&&SuppressedError,a=s(1),i=s(11),o=s(4)}(),c}()},"object"==typeof exports&&"object"==typeof module?module.exports=factory(require("react"),require("react-redux"),require("bwtk"),require("redux"),require("redux-actions"),require("redux-observable"),require("rxjs"),require("react-intl"),require("react-dom")):"function"==typeof define&&define.amd?define("Bundle",["react","react-redux","bwtk","redux","redux-actions","redux-observable","rxjs","react-intl","react-dom"],factory):"object"==typeof exports?exports.Bundle=factory(require("react"),require("react-redux"),require("bwtk"),require("redux"),require("redux-actions"),require("redux-observable"),require("rxjs"),require("react-intl"),require("react-dom")):this.Bundle=factory(this.React,this.ReactRedux,this.bwtk,this.Redux,this.ReduxActions,this.ReduxObservable,this.rxjs,this.ReactIntl,this.ReactDOM);
//# sourceMappingURL=bell-preauth-setup-bundle-bundle.min.js.map