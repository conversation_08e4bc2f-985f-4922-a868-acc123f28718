import { CreditCardDetails, BankAccountDetails, PaymentItem, SelectListItem } from './index';
import { PreAuthorizedPaymentMethod, PaymentMethodCreditOption, PaymentMethodDebitOption } from './Enums';

export interface IPreAuthorizedPayment {
  items: PaymentItem[];
  selectedItems: PaymentItem[];
  failedItems: PaymentItem[];
  successfullItems: PaymentItem[];
  currentItem: PaymentItem;
  selectedBanID: string;
  selectedCreditCard: CreditCardDetails;
  bankAccountInfo: BankAccountDetails;
  transactionID: string;
  completedTransactionID: string;
  padPaccTransactionID: string;
  customerEmail: string;
  contactUs: string;
  preAuthPaymentMethods: SelectListItem[];
  isAllBillsPreAuthorized: boolean;
  isSignUpPreAuthorized: boolean;
  isSignUpPreAuthorizedCompleted: boolean;
  hasSuccessPADPACCSignUp: boolean;
  hasFailedPADPACCSignUp: boolean;
  enableValidation: boolean;
  enableTransactionSummary: boolean;
  isCreditCardAgreementSelected: boolean;
  isBankAgreementSelected: boolean;
  selectedPaymentMethod?: PreAuthorizedPaymentMethod;
  selectedUpdatePaymentMethod: PreAuthorizedPaymentMethod;
  selectedPaymentMethodCreditOption?: PaymentMethodCreditOption;
  selectedPaymentMethodDebitOption?: PaymentMethodDebitOption;
  bankAccountCacheKey: string;
  creditCardCacheKey: string;
  showNoNameOnCard: boolean;
  acceptPrepaidCardNumber: boolean;
  showPACCAgreement: boolean;
  errorCode_CreditCard: string[];
  errorCode_Bank: string[];
  uniqueCreditCards: CreditCardDetails[];
  uniqueBankAccounts: BankAccountDetails[];
  reUsePadPaccFromBan: string;
  message: string;
  isFail: boolean;
  flowInitUrl: string;
}

export const defaultPreAuthorizedPayment: IPreAuthorizedPayment = {
  items: [],
  selectedItems: [],
  failedItems: [],
  successfullItems: [],
  currentItem: {} as PaymentItem, // Assuming PaymentItem has required properties set later
  selectedBanID: '',
  selectedCreditCard: {} as CreditCardDetails, // Assuming empty initial state
  bankAccountInfo: {} as BankAccountDetails, // Assuming empty initial state
  transactionID: '',
  completedTransactionID: '',
  padPaccTransactionID: '',
  customerEmail: '<EMAIL>',
  contactUs: '',
  preAuthPaymentMethods: [],
  isAllBillsPreAuthorized: false,
  isSignUpPreAuthorized: false,
  isSignUpPreAuthorizedCompleted: false,
  hasSuccessPADPACCSignUp: false,
  hasFailedPADPACCSignUp: false,
  enableValidation: false,
  enableTransactionSummary: false,
  isCreditCardAgreementSelected: false,
  isBankAgreementSelected: false,
  selectedPaymentMethod: undefined,
  selectedUpdatePaymentMethod: PreAuthorizedPaymentMethod.Debit, // Example default
  selectedPaymentMethodCreditOption: undefined,
  selectedPaymentMethodDebitOption: undefined,
  bankAccountCacheKey: '',
  creditCardCacheKey: '',
  showNoNameOnCard: false,
  acceptPrepaidCardNumber: false,
  showPACCAgreement: false,
  errorCode_CreditCard: [],
  errorCode_Bank: [],
  uniqueCreditCards: [],
  uniqueBankAccounts: [],
  reUsePadPaccFromBan: '',
  message: '',
  isFail: false,
  flowInitUrl: '',
};

export default IPreAuthorizedPayment;
