/** @type {import('tailwindcss').Config} */
/* global module */
module.exports = {
    content: ["./src/utils/**/*.tsx", "./src/views/**/*.tsx", "./src/**/*.tsx", "./src/default-messages.json"],
    theme: {
        fontFamily: {
          "bellslim-black": ["bellslim-black"],
          "bellslim-heavy": ["bellslim-heavy"],
          sans: ["Helvetica", "Arial", "sans-serif"]
        },
        screens: {
          'max-318': {'max': '318px'},
          sm: "768px",
          md: "992px",
          lg: "1240px",
        },
        extend: {
          spacing: {
            0: "0px",
            1: "1px",
            2: "2px",
            3: "3px",
            5: "5px",
            7: "7px",
            8: "8px",
            10: "10px",
            12: "12px",
            13: "13px",
            15: "15px",
            16: "16px",
            18: "18px",
            20: "20px",
            22: "22px",
            24: "24px",
            25: "25px",
            30: "30px",
            32: "32px",
            35: "35px",
            40: "40px",
            44: "44px",
            45: "45px",
            50: "50px",
            55: "55px",
            60: "60px",
            999: "999px"
          },
          borderWidth: {
            DEFAULT: "1px",
            1: "1px",
            0: "0",
            2: "2px",
            3: "3px",
            4: "4px"
          },
          borderRadius: {
            2: "2px",
            4: "4px",
            5: "5px",
            6: "6px",
            10: "10px",
            12: "12px",
            20: "20px",
            24: "24px"
          },
          colors: {
            blue: {
              DEFAULT: "#00549a",
              1: "#003778",
              2: "#0075ff",
              3: "#3376ae",
              4: "#d4dce8",
              5: "#007BFF"
            },
            white: {
              DEFAULT: "#FFFFFF",
              1: "#6698c2"
            },
            black: {
              DEFAULT: "#111",
              1: "#000000"
            },
            gray: {
              DEFAULT: "#555",
              1: "#d4d4d4",
              2: "#BABEC2",
              3: "#f4f4f4",
              4: "#e1e1e1",
              5: "#707070",
              6: "#EEEEEE",
              7: "#8D8D8D",
              8: "#E1E1E1",
              9: "#2B2B2B",
            },
            lightgray: {
              DEFAULT: "#CACACA"
            },
            blackSelection: {
              DEFAULT: "#00215E"
            },
            transparent: {
              1: "rgba(19, 28, 53, 0.08)"
            },
            red: {
              DEFAULT: "#bd2025"
            },
            yellow: {
              DEFAULT: "#C27F1F"
            }
          },
          fontSize: {
            10: "10px",
            12: "12px",
            14: "14px",
            15: "15px",
            18: "18px",
            22: "22px",
            24: "24px",
            26: "26px",
            30: "30px",
            32: "32px",
            34: "34px",
            40: "40px"
          },
          lineHeight: ({ theme }) => ({
            17: "17px",
            18: "18px",
            28: "28px",
            36: "36px",
            38: "38px",
            46: "46px",
            ...theme("fontSize")
          }),
          letterSpacing: {
            0.35: "0.35px",
            0.4: "0.4px",
            0.45: "0.45px",
            0.5: "0.5px",
            0.7: "0.7px",
            0.75: "0.75px",
            1: "1px"
          },
          ringWidth: {
            5: "5px"
          },
          outlineOffset: {
            2: "2px",
            3: "3px"
          },
          boxShadow: {
            "2sm": "0 8px 20px 0 #131C3533;",
            "3sm": "0px 6px 25px 0px rgba(0, 0, 0, 0.12)",
            "4sm": "0px 2px 3px 0px rgba(0, 0, 0, 0.20)"
          },
        }
      },
      plugins: [],
      prefix: "payment-"
};