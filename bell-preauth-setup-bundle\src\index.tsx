import * as React from "react";
import { createRoot } from "react-dom/client";
import { WidgetLoader, Init } from "bwtk";


export function initialize(config: any, containerId: string, debug: any) {
  const updatedConfig = {
    ...config, "loader.staticWidgetMappings": {
      "bell-preauth-setup": {
        factory: () => require("bell-preauth-setup"),
        namespace: "Preauth/Setup"
      }
    }
  };
  Init(updatedConfig);

  const container = document.getElementById(containerId);
  if (container) {
    try {
      // Clear any existing content
      container.innerHTML = '';

      // Create React 18 root with proper error boundaries
      const reactRoot = createRoot(container);

      // Wrap in React.StrictMode for React 18 compatibility
      reactRoot.render(
        <React.StrictMode>
          <WidgetLoader widget="bell-preauth-setup" />
        </React.StrictMode>
      );
    } catch (error) {
      console.error('Error initializing bell-preauth-setup widget:', error);
      // Fallback: show error message in container
      container.innerHTML = '<div style="padding: 20px; color: #d32f2f; border: 1px solid #d32f2f; border-radius: 4px; background-color: #ffeaea;">Failed to load payment widget. Please refresh the page.</div>';
    }
  } else {
    console.error(`Container element with id "${containerId}" not found`);
  }
}
